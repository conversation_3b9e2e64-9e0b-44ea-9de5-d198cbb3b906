import { EquipmentCategoryI18nMigrationConverter } from '../converters/07-02-convert-equipment-category-i18n-inserts';

async function convertEquipmentCategoryI18nData() {
  // Create converter and run conversion
  const converter = new EquipmentCategoryI18nMigrationConverter();
  await converter.convertFile();
}

// Run the conversion
convertEquipmentCategoryI18nData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
