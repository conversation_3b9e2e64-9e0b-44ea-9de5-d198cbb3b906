import { DbUtils } from '@rie/utils';
import { OUTPUT_PATH } from '../../constants';
import { BaseConverter } from '../base-converter';

export class PermissionsMigrationConverter extends BaseConverter {
  private permissionIdMappings: Record<string, string> = {};
  async convertFile(): Promise<void> {
    // Define permission domains
    const domains = [
      'address',
      'applicationSector',
      'building',
      'campus',
      'equipment',
      'excellenceHub',
      'fundingProject',
      'infrastructure',
      'innovationLab',
      'institution',
      'media',
      'people',
      'researchField',
      'room',
      'serviceContract',
      'serviceOffer',
      'technique',
      'unit',
      'user',
      'vendor',
      'visibility',
      'applicationSector',
      'documentationCategory',
      'equipmentCategory',
      'equipmentStatus',
      'equipmentType',
      'excellenceHub',
      'fundingProjectType',
      'fundingProjectIdentifierType',
      'innovationLab',
      'infrastructureStatus',
      'infrastructureType',
      'institutionType',
      'mediaType',
      'peopleRoleType',
      'roomCategory',
      'researchField',
      'technique',
      'unitType',
      'visibility',
    ];

    // Define common actions for each domain
    const commonActions = ['read', 'create', 'update', 'delete'];
    // The SQL content to append
    let permissionsInsertStatement =
      'INSERT INTO permissions (id, domain, action) VALUES\n';
    // Generate domain-specific permissions
    domains.forEach((domain, domainIndex) => {
      commonActions.forEach((action, actionIndex) => {
        const permissionId = DbUtils.cuid2();
        permissionsInsertStatement += `('${permissionId}', '${domain}', '${action}')${domainIndex === domains.length - 1 && actionIndex === commonActions.length - 1 ? ';' : ',\n'}`;
        this.permissionIdMappings[`${domain}-${action}`] = permissionId;
      });
    });

    // Create the output directory if it doesn't exist
    const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
    await this.safeMkdir(outputDir, { recursive: true });

    await this.writeMappingsToJson(
      outputDir,
      { mysql: 'permissions', postgres: 'permissions' },
      this.permissionIdMappings,
    );

    // console.log(permissionsInsertStatement);

    // Append the SQL content to the output file
    await this.safeAppendFile(OUTPUT_PATH, permissionsInsertStatement);
    console.log('Permissions data added successfully!');
  }
}
