'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { useDeleteDirectoryEntityHelper } from '@/helpers/use-delete-directory-entity.helpers';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { Button } from '@/ui/button';
import type { UnitListItemDTO } from '@rie/api-contracts';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const unitsColumns = (): ColumnDef<UnitListItemDTO>[] => {
  const directoryEntity = 'unit' as const;

  return [
    {
      accessorKey: 'name',
      cell: (cell) => {
        const nameField = cell.getValue() as { locale: string; value: string };
        return nameField?.value || '-';
      },
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'acronym',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.acronym"
        />
      ),
      id: 'acronym',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'parent',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.parentName"
        />
      ),
      id: 'parent',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'createdAt',
      cell: (cell) => {
        const format = useFormatter();
        const dateValue = cell.getValue() as string;
        return dateValue
          ? format.dateTime(new Date(dateValue), {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.createdAt"
        />
      ),
      id: 'createdAt',
      maxSize: 150,
      minSize: 120,
    },
    {
      accessorKey: 'updatedAt',
      cell: (cell) => {
        const format = useFormatter();
        const dateValue = cell.getValue() as string;
        return dateValue
          ? format.dateTime(new Date(dateValue), {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'updatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <UnitActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const UnitActions = ({ row }: { row: Row<UnitListItemDTO> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteDirectoryEntity } = useDeleteDirectoryEntityHelper('unit');
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.unit;

  // Extract the display name from the localized name field
  const displayName = row.original.name?.value || 'Unit';

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-unit"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteDirectoryEntity(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: displayName })}
        trigger={
          <Button
            size="icon"
            variant="destructive"
            data-testid="delete-unit"
          >
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
