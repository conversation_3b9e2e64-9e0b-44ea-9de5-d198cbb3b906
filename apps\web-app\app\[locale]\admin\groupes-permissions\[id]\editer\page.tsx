import { EditPermissionGroup } from '@/app/[locale]/admin/groupes-permissions/[id]/editer/edit-permission-group';
import { getQueryClientOptions } from '@/constants/query-client';
import { getPermissionsGroupByIdOptions } from '@/hooks/admin/permissions/permissions-groups.options';
import { redirect } from '@/lib/navigation';
import type { ResourcePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import { QueryClient } from '@tanstack/react-query';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export default async function PermissionsPage({ params }: ResourcePageParams) {
  const { id, locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: `/admin/groupes-permissions/${id}/editer` },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const permissionGroup = await queryClient.fetchQuery(
    getPermissionsGroupByIdOptions<'edit'>({ id, view: 'edit' }),
  );

  if (!permissionGroup) {
    return notFound();
  }

  console.log('permissionGroup', JSON.stringify(permissionGroup, null, 2));

  return (
    <div className="container mx-auto py-10">
      <EditPermissionGroup id={id} initialData={permissionGroup} />
    </div>
  );
}
