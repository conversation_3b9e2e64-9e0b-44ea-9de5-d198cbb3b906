import { getRedirectPath } from '@/helpers/get-redirect-path';
import { describe, expect, it } from 'vitest';

describe('getRedirectPath', () => {
  describe('URL decoding', () => {
    it('should decode URL-encoded paths', () => {
      const result = getRedirectPath('%2Fequipements%2FE2232%2Fediter', 'fr');
      expect(result).toEqual({
        pathname: '/equipements/[id]/editer',
        params: { id: 'E2232' },
      });
    });

    it('should handle complex encoded paths', () => {
      const result = getRedirectPath(
        '%2Finfrastructures%2FI000001%2Fedit',
        'en',
      );
      expect(result).toEqual({
        pathname: '/infrastructures/[id]/edit',
        params: { id: 'I000001' },
      });
    });

    it('should handle encoded bottin paths', () => {
      const result = getRedirectPath(
        '%2Fbottin%2Fbatiments%2FB123%2Fediter',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/bottin/batiments/[id]/editer',
        params: { id: 'B123' },
      });
    });
  });

  describe('parameter extraction', () => {
    it('should extract id parameter from encoded dynamic routes', () => {
      const result = getRedirectPath('%2Fequipements%2FE000875%2Fediter', 'fr');
      expect(result).toEqual({
        pathname: '/equipements/[id]/editer',
        params: { id: 'E000875' },
      });
    });

    it('should handle encoded IDs with hyphens', () => {
      const result = getRedirectPath(
        '%2Fdirectory%2Fpeople%2Fabc-def-123',
        'en',
      );
      expect(result).toEqual({
        pathname: '/directory/people/[id]',
        params: { id: 'abc-def-123' },
      });
    });

    it('should handle encoded campus paths', () => {
      const result = getRedirectPath(
        '%2Fbottin%2Fcampus%2FC456%2Fediter',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/bottin/campus/[id]/editer',
        params: { id: 'C456' },
      });
    });
  });

  describe('locale handling', () => {
    it('should match encoded French localized paths', () => {
      const result = getRedirectPath(
        '%2Fbottin%2Fetablissements%2FE789%2Fediter',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/bottin/etablissements/[id]/editer',
        params: { id: 'E789' },
      });
    });

    it('should match encoded English localized paths', () => {
      const result = getRedirectPath(
        '%2Fdirectory%2Fbuildings%2FB321%2Fedit',
        'en',
      );
      expect(result).toEqual({
        pathname: '/directory/buildings/[id]/edit',
        params: { id: 'B321' },
      });
    });
  });

  describe('routes without parameters', () => {
    it('should return pathname only for encoded static routes', () => {
      const result = getRedirectPath('%2Finfrastructures', 'fr');
      expect(result).toEqual({
        pathname: '/infrastructures',
      });
    });

    it('should handle encoded directory routes', () => {
      const result = getRedirectPath('%2Fdirectory%2Fpeople', 'en');
      expect(result).toEqual({
        pathname: '/directory/people',
      });
    });

    it('should handle encoded admin routes', () => {
      const result = getRedirectPath('%2Fadmin%2Fpermissions', 'fr');
      expect(result).toEqual({
        pathname: '/admin/permissions',
      });
    });
  });

  describe('fallback behavior', () => {
    it('should return root pathname for encoded unmatched routes', () => {
      const result = getRedirectPath('%2Fnon-existent-route', 'fr');
      expect(result).toEqual({
        pathname: '/',
      });
    });

    it('should return root pathname for encoded invalid paths', () => {
      const result = getRedirectPath('%2Finvalid%2Fpath%2Fstructure', 'en');
      expect(result).toEqual({
        pathname: '/',
      });
    });

    it('should handle double-encoded paths', () => {
      const result = getRedirectPath(
        '%252Fequipements%252FE2232%252Fediter',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/',
      });
    });
  });

  describe('edge cases', () => {
    it('should handle encoded paths with encoded query parameters', () => {
      const result = getRedirectPath(
        '%2Fequipements%2FE123%2Fediter%3Fparam%3Dvalue',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/',
      });
    });

    it('should handle encoded paths with encoded hash fragments', () => {
      const result = getRedirectPath(
        '%2Fequipements%2FE123%2Fediter%23section',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/',
      });
    });

    it('should handle encoded special characters in IDs', () => {
      const result = getRedirectPath(
        '%2Fbottin%2Fpersonnes%2FP%2D123%2Fediter',
        'fr',
      );
      expect(result).toEqual({
        pathname: '/bottin/personnes/[id]/editer',
        params: { id: 'P-123' },
      });
    });

    it('should handle mixed encoding scenarios', () => {
      const result = getRedirectPath('%2Fequipements%2FE2232%2Fediter', 'fr');
      expect(result).toEqual({
        pathname: '/equipements/[id]/editer',
        params: { id: 'E2232' },
      });
    });
  });
});
