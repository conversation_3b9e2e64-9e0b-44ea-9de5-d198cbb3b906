import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { TimeUnitMigrationConverter } from '../converters/04-01-convert-time-unit-inserts';

async function convertTimeUnitData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new TimeUnitMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertTimeUnitData().catch(console.error);
