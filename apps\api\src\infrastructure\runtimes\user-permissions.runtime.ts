import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  RolesRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  RolesServiceLive,
  UserPermissionsServiceLive,
  UsersServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const UserPermissionsServicesLayer = Layer.mergeAll(
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  RolesRepositoryLive.Default,
  UsersServiceLive.Default,
  AccessTreeServiceLive.Default,
  RolesServiceLive.Default,
  UserPermissionsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const UserPermissionsRuntime = ManagedRuntime.make(
  UserPermissionsServicesLayer,
);
