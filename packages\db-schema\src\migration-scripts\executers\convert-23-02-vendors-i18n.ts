import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { VendorI18nMigrationConverter } from '../converters/23-02-convert-vendor-i18n-inserts';

async function convertVendorI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new VendorI18nMigrationConverter();
    // Convert the file
    await converter.convertFile();
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertVendorI18nData().catch(console.error);
