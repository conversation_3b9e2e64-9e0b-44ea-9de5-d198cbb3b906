/**
 * Testable BaseConverter class
 *
 * Since BaseConverter is abstract, we create a concrete implementation
 * that exposes protected methods as public for testing purposes.
 */

import { BaseConverter } from '../base-converter';

export class TestableBaseConverter extends BaseConverter {
  constructor(dryRun = false) {
    super(dryRun);
  }

  // Expose protected methods as public for testing
  public testParseCSVValues(csvString: string): string[] {
    return this.parseCSVValues(csvString);
  }

  public testSplitValueRows(valuesContent: string): string[] {
    return this.splitValueRows(valuesContent);
  }

  public testExtractValuesFromInsertStatement(
    insertStatement: string,
    sqlContent: string,
    tableName: string,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  ): Record<string, any> {
    return this.extractValuesFromInsertStatement(
      insertStatement,
      sqlContent,
      tableName,
    );
  }

  public testExtractInsertStatements(
    sqlContent: string,
    tableName: string,
  ): string[] {
    return this.extractInsertStatements(sqlContent, tableName);
  }

  public testExtractColumnNames(createTableStatement: string): string[] {
    return this.extractColumnNames(createTableStatement);
  }

  public testExtractCreateStatement(
    sqlContent: string,
    tableName: string,
  ): string {
    return this.extractCreateStatement(sqlContent, tableName);
  }

  // Helper methods for testing error conditions
  public testFormatSqlValue(value: string | number | null | undefined): string {
    return this.formatSqlValue(value);
  }

  public testParseNullableString(value: string): string | null {
    return this.parseNullableString(value);
  }

  public testFormatDate(dateStr: string | null): string | null {
    return this.formatDate(dateStr);
  }

  // Mock implementation of abstract methods (not used in tests)
  async convertFile(): Promise<void> {
    // Mock implementation for testing
  }
}
