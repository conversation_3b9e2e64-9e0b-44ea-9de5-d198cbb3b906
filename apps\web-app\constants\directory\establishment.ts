import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const initialColumnVisibility = {
  acronym: true,
  lastUpdatedAt: true,
  name: true,
  type: true,
} as const;

export const establishmentFormSections = {
  affiliations: {
    key: 'affiliations',
    order: 2,
  },
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const establishmentFormDefaultValues = (
  locale: SupportedLocale,
): EstablishmentFormSchema => ({
  acronym: [{ locale, value: '' }],
  establishmentType: { value: '', label: '' },
  name: [{ locale, value: '' }],
  pseudonym: [{ locale, value: '' }],
});
