import { InstitutionFormRequestDtoSchema } from '@rie/api-contracts';
import {
    DbInstitutionI18NInputSchema,
    InstitutionInputSchema,
} from '@rie/domain/schemas';
import type { InstitutionI18NInput, Writable } from '@rie/domain/types';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const InstitutionFormToDomainInput = Schema.transformOrFail(
    InstitutionFormRequestDtoSchema,
    InstitutionInputSchema,
    {
        strict: true,
        decode: (formDto, _, ast) => {
            return ParseResult.try({
                try: () => {
                    const translationsMap = new Map<string, Writable<InstitutionI18NInput>>();

                    const ensureLocale = (locale: string) => {
                        if (!translationsMap.has(locale)) {
                            translationsMap.set(locale, {
                                locale,
                                name: null,
                                description: null,
                                otherNames: null,
                                acronyms: null,
                            });
                        }
                    };

                    for (const item of formDto.name) {
                        ensureLocale(item.locale);
                        const itemFound = translationsMap.get(item.locale);
                        if (itemFound) {
                            itemFound.name = item.value;
                        }
                    }

                    for (const item of formDto.description) {
                        ensureLocale(item.locale);
                        const itemFound = translationsMap.get(item.locale);
                        if (itemFound) {
                            itemFound.description = item.value;
                        }
                    }

                    for (const item of formDto.otherNames) {
                        ensureLocale(item.locale);
                        const itemFound = translationsMap.get(item.locale);
                        if (itemFound) {
                            itemFound.otherNames = item.value;
                        }
                    }

                    for (const item of formDto.acronyms) {
                        ensureLocale(item.locale);
                        const itemFound = translationsMap.get(item.locale);
                        if (itemFound) {
                            itemFound.acronyms = item.value;
                        }
                    }

                    const translations = Array.from(translationsMap.values()).map((t) =>
                        Schema.decodeSync(DbInstitutionI18NInputSchema)(t),
                    );

                    return {
                        isActive: formDto.isActive ?? true, // Default to true for new institutions
                        modifiedBy: null,
                        guidId: formDto.guidId ?? null,
                        typeId: formDto.institutionType.value,
                        translations: translations,
                    };
                },
                catch: (error) =>
                    new ParseResult.Type(ast, formDto, (error as Error).message),
            });
        },
        encode: (domainInput, _, ast) =>
            ParseResult.fail(
                new ParseResult.Forbidden(
                    ast,
                    domainInput,
                    'Encoding from domain to form is not supported by this transformer',
                ),
            ),
    },
);