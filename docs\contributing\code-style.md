# Code Style Guide

This document outlines the coding standards and style guidelines for the RIE project. Following these guidelines ensures consistency, readability, and maintainability across the codebase.

## 🎯 General Principles

### 1. Consistency

- Follow existing patterns in the codebase
- Use the same naming conventions throughout
- Maintain consistent file and folder structures

### 2. Readability

- Write self-documenting code
- Use descriptive names for variables, functions, and classes
- Keep functions small and focused on a single responsibility

### 3. Maintainability

- Avoid deep nesting and complex logic
- Use proper error handling
- Write comprehensive tests

### 4. Performance

- Consider performance implications of your code
- Avoid premature optimization
- Use appropriate data structures and algorithms

## 📝 TypeScript Guidelines

### Naming Conventions

#### Variables and Functions

```typescript
// Use camelCase for variables and functions
const userName = 'john.doe'
const isAuthenticated = true

// Use descriptive names
const getUserPermissions = async (userId: string) => { /* ... */ }
const calculateTotalPrice = (items: CartItem[]) => { /* ... */ }

// Avoid abbreviations unless they're widely understood
const config = getConfiguration() // ✅ Good
const cfg = getConfiguration()    // ❌ Avoid
```

#### Constants

```typescript
// Use SCREAMING_SNAKE_CASE for constants
const MAX_RETRY_ATTEMPTS = 3
const API_BASE_URL = 'https://api.example.com'
const DEFAULT_PAGE_SIZE = 20
```

#### Types and Interfaces

```typescript
// Use PascalCase for types and interfaces
interface UserProfile {
  readonly id: string
  readonly name: string
  readonly email: string
}

type UserStatus = 'ACTIVE' | 'INACTIVE' | 'PENDING'

// Use descriptive names that indicate purpose
interface CreateUserRequest {
  readonly name: string
  readonly email: string
  readonly role?: string
}

// Prefer interfaces over types for object shapes
interface ApiResponse<T> {
  readonly data: T
  readonly status: number
  readonly message?: string
}
```

#### Enums

```typescript
// Use PascalCase for enum names and SCREAMING_SNAKE_CASE for values
enum UserRole {
  ADMINISTRATOR = 'ADMINISTRATOR',
  LAB_MANAGER = 'LAB_MANAGER',
  RESEARCHER = 'RESEARCHER',
  STUDENT = 'STUDENT'
}

enum HttpStatus {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500
}
```

### Type Definitions

#### Function Signatures

```typescript
// Use explicit return types for public functions
const createUser = async (data: CreateUserRequest): Promise<User> => {
  // Implementation
}

// Use readonly for immutable data
interface UserPreferences {
  readonly theme: 'light' | 'dark'
  readonly language: 'en' | 'fr'
  readonly notifications: readonly NotificationSetting[]
}

// Use proper error types
const validateUser = (user: User): Result<User, ValidationError> => {
  // Implementation
}
```

#### Generic Types

```typescript
// Use descriptive generic parameter names
interface Repository<TEntity, TKey = string> {
  findById(id: TKey): Promise<TEntity | null>
  create(entity: Omit<TEntity, 'id'>): Promise<TEntity>
  update(id: TKey, updates: Partial<TEntity>): Promise<TEntity>
  delete(id: TKey): Promise<void>
}

// Use constraints when appropriate
interface Identifiable {
  readonly id: string
}

interface EntityService<T extends Identifiable> {
  process(entity: T): Promise<void>
}
```

### Error Handling

#### Custom Errors

```typescript
// Create specific error classes
export class UserNotFoundError extends Error {
  constructor(userId: string) {
    super(`User with ID ${userId} not found`)
    this.name = 'UserNotFoundError'
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly field: string,
    public readonly value: unknown
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}
```

#### Effect-ts Error Handling

```typescript
// Use Effect-ts for functional error handling
import { Effect, Data } from 'effect'

export class DatabaseError extends Data.TaggedError('DatabaseError')<{
  readonly cause: unknown
  readonly operation: string
}> {}

export class UserNotFoundError extends Data.TaggedError('UserNotFoundError')<{
  readonly userId: string
}> {}

const findUser = (id: string): Effect.Effect<User, UserNotFoundError | DatabaseError> =>
  Effect.gen(function* (_) {
    try {
      const user = yield* _(userRepository.findById(id))
      if (!user) {
        yield* _(Effect.fail(new UserNotFoundError({ userId: id })))
      }
      return user
    } catch (error) {
      yield* _(Effect.fail(new DatabaseError({ 
        cause: error, 
        operation: 'findUser' 
      })))
    }
  })
```

## ⚛️ React Guidelines

### Component Structure

#### Functional Components

```typescript
// Use functional components with hooks
interface UserCardProps {
  readonly user: User
  readonly onEdit?: (user: User) => void
  readonly onDelete?: (userId: string) => void
  readonly className?: string
}

export function UserCard({ 
  user, 
  onEdit, 
  onDelete, 
  className 
}: UserCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  
  const handleEdit = useCallback(() => {
    setIsEditing(true)
    onEdit?.(user)
  }, [user, onEdit])
  
  return (
    <div className={cn('user-card', className)}>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <div className="actions">
        <Button onClick={handleEdit}>Edit</Button>
        <Button 
          variant="destructive" 
          onClick={() => onDelete?.(user.id)}
        >
          Delete
        </Button>
      </div>
    </div>
  )
}
```

#### Custom Hooks

```typescript
// Create reusable custom hooks
export function useUser(userId: string) {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: () => userApi.getById(userId),
    enabled: !!userId,
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('User created successfully')
    },
    onError: (error) => {
      toast.error('Failed to create user')
      console.error('User creation error:', error)
    },
  })
}
```

### Component Organization

#### File Structure

```
components/
├── ui/                           # Shadcn/UI components (reusable primitives)
│   ├── button.tsx
│   ├── input.tsx
│   ├── dialog.tsx
│   ├── table.tsx
│   ├── form.tsx
│   └── stories/                  # Storybook stories
│       └── button.stories.ts
├── form-fields/                  # Specialized form field components
│   ├── input-field.tsx
│   ├── combobox-field.tsx
│   ├── datepicker-field.tsx
│   └── multiselect-field.tsx
├── form-layout/                  # Form layout components
│   ├── form-layout.tsx
│   ├── form-main.tsx
│   └── form-sidebar.tsx
├── data-table/                   # Table components
│   └── data-table.tsx
├── filters/                      # Filtering components
│   ├── filters.tsx
│   ├── filter-item.tsx
│   └── selected-filters.tsx
├── permissions/                  # Permission-related components
│   ├── permission-gate.tsx
│   └── route-guard.tsx
├── main-layout/                  # Main application layout
│   ├── main-layout.tsx
│   ├── header/
│   │   ├── header.tsx
│   │   ├── profile-menu.tsx
│   │   └── locale-switcher.tsx
│   ├── sidebar/
│   │   ├── sidebar.tsx
│   │   └── navigation/
│   └── top-menu/
│       └── top-menu.tsx
├── card-equipment/               # Equipment-specific components
│   ├── card-equipment.tsx
│   └── card-equipment.stories.ts
├── card-infrastructure/          # Infrastructure-specific components
│   ├── card-infrastructure.tsx
│   └── card-infrastructure.stories.ts
├── entity-page/                  # Generic entity page components
│   ├── entity-page-header.tsx
│   └── entity-page-header-directory.tsx
├── details-section/              # Detail view components
│   └── details-section.tsx
├── table-pagination/             # Pagination components
│   └── table-pagination.tsx
├── search-input/                 # Search components
│   └── search-input.tsx
├── hooks/                        # Component-specific hooks
│   └── use-toast.tsx
└── [component-name]/             # Individual component folders
    ├── component-name.tsx        # Main component
    ├── component-name.test.tsx   # Tests (optional)
    ├── component-name.stories.ts # Storybook (optional)
    └── types.ts                  # Component types (optional)
```

#### Export Patterns

```typescript
// Use named exports for components
export function UserList({ filters }: UserListProps) {
  // Component implementation
}

// Export types alongside components
export interface UserListProps {
  readonly filters: UserFilters
  readonly onUserSelect?: (user: User) => void
}

// Use barrel exports for feature modules
// features/users/index.ts
export { UserList } from './user-list'
export { UserCard } from './user-card'
export { UserForm } from './user-form'
export type { UserListProps, UserCardProps, UserFormProps } from './types'
```

## 🎨 Styling Guidelines

### Tailwind CSS

#### Class Organization

```typescript
// Group classes logically
<div className={cn(
  // Layout
  'flex items-center justify-between',
  // Spacing
  'p-4 mb-6',
  // Appearance
  'bg-white border border-gray-200 rounded-lg shadow-sm',
  // Interactive states
  'hover:shadow-md transition-shadow',
  // Responsive
  'md:p-6 lg:p-8',
  // Conditional classes
  isActive && 'ring-2 ring-blue-500',
  className
)}>
```

#### Custom Utilities

```typescript
// Use the cn utility for conditional classes
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)
```

## 🗄️ Database Guidelines

### Schema Design

#### Table Naming

```sql
-- Use plural nouns for table names
-- Use descriptive junction table names
```

#### Drizzle Schema

```typescript
// Use descriptive schema definitions
import { DbUtils } from '@rie/utils';

export const users = pgTable('users', {
  id: text('id')
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  email: text().unique().notNull(),
  name: text().notNull(),
  status: text().notNull().default('ACTIVE'),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
})

// Define relationships clearly
export const userRoles = pgTable('user_roles', {
  userId: text().references(() => users.id).notNull(),
  roleId: text().references(() => roles.id).notNull(),
  resourceType: text(),
  resourceId: text(),
  grantedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  grantedBy: text().references(() => users.id),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.roleId, table.resourceType, table.resourceId] }),
}))
```

## 🧪 Testing Guidelines

### Test Structure

#### Unit Tests

```typescript
// Use descriptive test names
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // Arrange
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
      }
      
      // Act
      const result = await userService.createUser(userData)
      
      // Assert
      expect(result).toMatchObject({
        name: userData.name,
        email: userData.email,
      })
      expect(result.id).toBeDefined()
    })
    
    it('should throw ValidationError for invalid email', async () => {
      // Arrange
      const userData = {
        name: 'John Doe',
        email: 'invalid-email',
      }
      
      // Act & Assert
      await expect(userService.createUser(userData))
        .rejects
        .toThrow(ValidationError)
    })
  })
})
```

#### Component Tests

```typescript
// Test component behavior, not implementation
describe('UserCard', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    status: 'ACTIVE' as const,
  }
  
  it('should display user information', () => {
    render(<UserCard user={mockUser} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })
  
  it('should call onEdit when edit button is clicked', async () => {
    const onEdit = vi.fn()
    render(<UserCard user={mockUser} onEdit={onEdit} />)
    
    await user.click(screen.getByRole('button', { name: /edit/i }))
    
    expect(onEdit).toHaveBeenCalledWith(mockUser)
  })
})
```

## 📁 File Organization

### Import Order

```typescript
// 1. Node modules
import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'

// 2. Internal packages (monorepo)
import { User } from '@rie/domain'
import { formatDate } from '@rie/utils'

// 3. Relative imports (same package)
import { Button } from '@/components/ui/button'
import { userApi } from '@/lib/api/users'
import { cn } from '@/lib/utils'

// 4. Type-only imports (at the end)
import type { UserFilters } from '@/types/user'
```

### File Naming

```
// Use kebab-case for files
user-management.tsx
equipment-form.tsx
api-client.ts

// Use PascalCase for component files (optional)
UserManagement.tsx
EquipmentForm.tsx

// Use descriptive names
user-permissions.service.ts
equipment-validation.schema.ts
database-connection.config.ts
```

## 🔧 Tool Configuration

### Biome Configuration

The project uses Biome for linting and formatting. Configuration is in `biome.jsonc`:

```bash
# Check code quality
pnpm lint

# Fix auto-fixable issues
pnpm lint:fix

# Format code
pnpm format
```

### VS Code Settings

Recommended VS Code settings are in `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  }
}
```

## ✅ Checklist

Before submitting code, ensure:

- [ ] Code follows naming conventions
- [ ] Functions are properly typed
- [ ] Error handling is implemented
- [ ] Tests are written and passing
- [ ] Code is formatted with Biome
- [ ] No linting errors
- [ ] Documentation is updated if needed
- [ ] Imports are organized correctly
- [ ] No console.log statements in production code
- [ ] Performance considerations are addressed

---

**Remember**: These guidelines exist to help maintain code quality and consistency. When in doubt, look at existing code for patterns and ask the team for guidance.
