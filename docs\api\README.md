# API Documentation

This section contains comprehensive documentation for the RIE API server, including authentication, permissions, and endpoint specifications.

## 🚀 API Overview

The RIE API is a RESTful service built with Hono and Effect-ts, providing endpoints for managing research infrastructure and equipment. The API follows OpenAPI 3.0 specifications and includes automatic documentation generation.

### Base Information

- **Base URL**: `http://localhost:4000/api/v2` (development)
- **API Version**: v2
- **Documentation**: Available at `http://localhost:4000/api/doc` (Swagger UI)
- **OpenAPI Spec**: Available at `http://localhost:4000/api/openapi`

### Key Features

- **RESTful Design**: Standard HTTP methods and status codes
- **Type Safety**: Full TypeScript integration with runtime validation
- **Authentication**: Session-based authentication with Better Auth
- **Authorization**: Hybrid approach combining Role-based access control (RBAC) and Attribute-based access control (ABAC)
- **Validation**: Request/response validation with <PERSON> Schemas
- **Documentation**: Auto-generated OpenAPI documentation
- **Error Handling**: Structured error responses
- **Internationalization**: Multi-language support

## 🔐 Authentication & Authorization

### Authentication Flow

1. **Login**: User provides credentials to `/auth/login`
2. **Session**: API creates secure session and returns session cookie
3. **Authorization**: Session cookie automatically included in subsequent requests
4. **Validation**: Middleware validates session and extracts user info

### Authorization Model

The API uses a hierarchical role-based access control system:

```
Users → Roles → Permission Groups → Permissions
```

- **Context-Aware**: Permissions can be scoped to specific resources
- **Hierarchical**: Permissions inherit from parent contexts
- **Flexible**: Support for institution, unit, infrastructure, and equipment contexts

For detailed information, see:

- [Authentication Guide](./authentication.md)
- [Permissions System](./permissions.md)

## 📋 API Structure

### Endpoint Organization

```
/api/v2/
├── auth/                    # Authentication endpoints
│   ├── POST /login         # User login
│   ├── POST /register      # User registration
│   ├── POST /logout        # User logout
│   └── GET /me             # Current user info
├── users/                   # User management
│   ├── GET /               # List users
│   ├── POST /              # Create user
│   ├── GET /:id            # Get user
│   ├── PUT /:id            # Update user
│   ├── DELETE /:id         # Delete user
│   └── /:id/roles          # User role management
├── roles/                   # Role management
├── permissions/             # Permission management
├── institutions/            # Institution management
├── units/                   # Unit management
├── infrastructures/         # Infrastructure management
└── equipment/               # Equipment management
```

### HTTP Methods & Status Codes

#### Standard Methods

- **GET**: Retrieve resources
- **POST**: Create new resources
- **PUT**: Update existing resources (full replacement)
- **PATCH**: Partial updates
- **DELETE**: Remove resources

#### Status Codes

- **200 OK**: Successful GET, PUT, PATCH
- **201 Created**: Successful POST
- **204 No Content**: Successful DELETE
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server errors

## 📝 Request/Response Format

### Content Type

All requests and responses use `application/json` content type.

### Request Headers

```http
Content-Type: application/json
Cookie: session=<session-id>
Accept-Language: en|fr
```

**Note**: Authentication is handled via secure HTTP-only session cookies. No manual authorization headers are required as the session cookie is automatically included by the browser.

### Response Format

#### Success Response

```json
{
  "id": "123",
  "name": "Equipment Name",
  "type": "MICROSCOPE",
  "status": "AVAILABLE",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "name",
        "message": "Name is required"
      }
    ]
  }
}
```

#### Paginated Response

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

## 🔍 Query Parameters

### Common Parameters

#### Pagination

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

#### Sorting

- `sort`: Field to sort by
- `order`: Sort direction (`asc` or `desc`)

#### Filtering

- `filter[field]`: Filter by field value
- `search`: Text search across multiple fields

#### Example

```text
GET /api/v2/equipment?page=2&limit=10&sort=name&order=asc&filter[type]=MICROSCOPE&search=advanced
```

## 🛡️ Security

### Authentication

- Session-based authentication with Better Auth
- Secure HTTP-only session cookies
- Configurable session expiration
- Automatic session renewal

### Authorization

- Role-based access control
- Resource-level permissions
- Context-aware authorization

### Data Protection

- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

### Rate Limiting

- Request rate limiting per user/IP
- Configurable limits per endpoint
- Graceful degradation

## 🔧 Development Tools

### Interactive Documentation

Access the Swagger UI at `http://localhost:8000/doc` for:

- Interactive API exploration
- Request/response examples
- Schema documentation
- Authentication testing

### API Testing

- **Postman/Insomnia**: Import OpenAPI spec
- **curl**: Command-line testing
- **Automated Tests**: Integration test suite

### Monitoring

- Request/response logging
- Performance metrics
- Error tracking
- Health check endpoints

## 📚 Detailed Documentation

### Core Concepts

- [Authentication System](./authentication.md) - How authentication works
- [Permissions System](./permissions.md) - Role-based access control
- [Error Handling](./error-handling.md) - Error codes and responses

### API Endpoints

- [User Management](./endpoints/users.md) - User CRUD operations
- [Role Management](./endpoints/roles.md) - Role and permission management
- [Equipment Management](./endpoints/equipment.md) - Equipment operations
- [Infrastructure Management](./endpoints/infrastructures.md) - Infrastructure operations

### Advanced Topics

- [Pagination](./pagination.md) - Handling large datasets
- [Filtering & Search](./filtering.md) - Query capabilities
- [Internationalization](./i18n.md) - Multi-language support
- [Webhooks](./webhooks.md) - Event notifications

## 🚀 Getting Started

### Quick Start

1. Start the API server: `cd apps/api && bun run dev`
2. Visit the documentation: `http://localhost:8000/doc`
3. Authenticate using the login endpoint
4. Explore the available endpoints

### Example Request

```bash
# Login to create session (save cookies)
curl -X POST http://localhost:8000/api/v2/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' \
  -c cookies.txt

# Use session cookie to access protected endpoint
curl -X GET http://localhost:8000/api/v2/users \
  -b cookies.txt
```

**Note**: When using the API from a web browser, session cookies are automatically handled. The curl examples above show manual cookie management for testing purposes.

## 🆘 Support

### Common Issues

- Check the [troubleshooting guide](../guides/troubleshooting.md)
- Verify authentication tokens are valid
- Ensure proper request headers are set
- Check API server logs for detailed errors

### Getting Help

- Review the interactive documentation
- Check the development guides
- Ask in team chat or create an issue

---

**Last Updated**: January 2025  
**API Version**: v2.0.0
