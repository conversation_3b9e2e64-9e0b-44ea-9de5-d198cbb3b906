import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { FundingProjectIdentifierTypeMigrationConverter } from '../converters/16-01-convert-funding-project-identifier-type-inserts';

async function convertFundingProjectIdentifierTypeData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new FundingProjectIdentifierTypeMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertFundingProjectIdentifierTypeData().catch(console.error);
