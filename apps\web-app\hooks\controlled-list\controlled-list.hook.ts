"use client";

import { getControlledListSelectOptions, getMultipleControlledListsSelectOptions } from '@/hooks/controlled-list/controlled-list.options';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useAppStore } from '@/providers/app-store-provider';
import type { ControlledListKey } from '@/types/common';
import { CONTROLLED_LIST_ENTITIES } from '@/types/controlled-list/controlled-list.type';
import { useQuery } from '@tanstack/react-query';
import { useDebounce } from '@uidotdev/usehooks';
import { useCallback, useEffect, useState } from 'react';

// Type pour la compatibilité avec ComboboxFieldInfiniteScroll
type ControlledListSelectData = {
    data?: Array<{ value: string; label: string }>;
    isLoading: boolean;
    error: Error | null;
    isError: boolean;
    fetchNextPage?: () => void;
    hasNextPage?: boolean;
    isFetching?: boolean;
    isFetchingNextPage?: boolean;
};

// Hook pour une seule liste contrôlée
export const useGetControlledListSelect = <
    Key extends ControlledListKey,
>({
    controlledListKey,
}: {
    controlledListKey: Key;
}) => {
    const locale = useAvailableLocale();
    return useQuery(getControlledListSelectOptions({ controlledListKey, locale }));
};

// Hook pour plusieurs listes contrôlées avec gestion de la recherche
export const useControlledListSelect = (
    controlledListKeys: ControlledListKey[],
) => {
    const locale = useAvailableLocale();
    const setControlledListFilter = useAppStore(
        (state) => state.setControlledListFilter,
    );

    const [searchTerm, setSearchTerm] = useState('');
    const [activeControlledListKey, setActiveControlledListKey] =
        useState<ControlledListKey>();
    const debouncedSearchTerm = useDebounce(searchTerm, 250);

    useEffect(() => {
        if (activeControlledListKey) {
            setControlledListFilter(activeControlledListKey, debouncedSearchTerm);
        }
    }, [debouncedSearchTerm, activeControlledListKey, setControlledListFilter]);

    const handleOnSearchTermChange = useCallback(
        (value: string, controlledListKey?: ControlledListKey) => {
            setSearchTerm(value);
            setActiveControlledListKey(controlledListKey);
        },
        [],
    );

    // Utilisation d'une seule requête pour récupérer toutes les listes (plus efficace)
    const multipleListsQuery = useQuery(
        getMultipleControlledListsSelectOptions({ controlledListKeys, locale })
    );

    // Construction de l'objet selectsData avec les résultats
    // Interface compatible avec les requêtes infinies pour la rétrocompatibilité
    const selectsData = controlledListKeys.reduce<
        Partial<Record<ControlledListKey, ControlledListSelectData>>
    >((acc, key) => {
        const entityName = CONTROLLED_LIST_ENTITIES[key];
        const listData = entityName ? multipleListsQuery.data?.[entityName] : undefined;

        acc[key] = {
            data: listData,
            isLoading: multipleListsQuery.isLoading,
            error: multipleListsQuery.error,
            isError: multipleListsQuery.isError,
            // Pour les listes contrôlées complètes, pas de pagination
            fetchNextPage: () => { }, // Fonction vide car pas de pagination
            hasNextPage: false, // Toutes les données sont déjà chargées
            isFetching: multipleListsQuery.isFetching,
            isFetchingNextPage: false, // Pas de pagination
        };
        return acc;
    }, {});

    return { handleOnSearchTermChange, selectsData };
};
