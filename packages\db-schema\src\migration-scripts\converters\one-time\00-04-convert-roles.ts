import { DbUtils } from '@rie/utils';
import { OUTPUT_PATH } from '../../constants';
import { BaseConverter } from '../base-converter';

interface Role {
  name: string;
  description: string;
  permissionGroups: string[]; // Permission group names
  inheritsFrom: string[]; // Parent role names
}

export class RolesMigrationConverter extends BaseConverter {
  private roleIdMappings: Record<string, string> = {};
  private permissionGroupIdMappings: Record<string, string> = {};

  private async loadPermissionGroupMappings(): Promise<void> {
    try {
      this.permissionGroupIdMappings =
        await this.loadEntityIdMappings('permission_groups');
    } catch (error) {
      console.error('Error loading permission group mappings:', error);
      throw error;
    }
  }

  async convertFile(): Promise<void> {
    // Load existing permission group mappings
    await this.loadPermissionGroupMappings();

    // Define all roles for Phase 1 (Base) + Phase 2 (Global)
    const allRoles: Role[] = [
      {
        name: 'BaseExplorer',
        description:
          'Provides global read permissions for discovery and reference across all domains.',
        permissionGroups: ['CanViewEquipment', 'CanViewInfrastructures'],
        inheritsFrom: [], // Base role - no inheritance
      },
      {
        name: 'UdeM',
        description: 'Allows users to access data with "UdeM" visibility.',
        permissionGroups: ['CanViewUdeMData'],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'UdeMPartner',
        description:
          'Allows users to access data with "UdeMPartner" visibility.',
        permissionGroups: ['CanViewPartnerData'],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'User',
        description: 'Allows users to access data with "Private" visibility.',
        permissionGroups: ['CanViewPrivateData'],
        inheritsFrom: ['BaseExplorer'],
      },

      // Phase 2: Global Roles
      {
        name: 'SuperExplorer',
        description:
          'Provides comprehensive read access across all domains and entities except user management.',
        permissionGroups: [
          'CanAccessDirectory',
          'CanAccessControlledLists',
          'CanViewBuilding',
          'CanViewCampus',
          'CanViewFundingProject',
          'CanViewInstitution',
          'CanViewPeople',
          'CanViewRoom',
          'CanViewUnit',
          'CanViewVendor',
        ],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'SuperEditor',
        description:
          'Provides comprehensive management rights across all domains except user management.',
        permissionGroups: [
          'InfrastructureManagement',
          'EquipmentManagement',
          'BuildingManagement',
          'CampusManagement',
          'FundingProjectManagement',
          'InstitutionManagement',
          'PeopleManagement',
          'RoomManagement',
          'UnitManagement',
          'VendorManagement',
          'CanManageControlledList',
        ],
        inheritsFrom: ['SuperExplorer'],
      },
      {
        name: 'SystemAdmin',
        description: 'Full system administration rights with all permissions.',
        permissionGroups: ['CanManageDirectoryEntityPermissions'],
        inheritsFrom: ['SuperEditor'],
      },

      // Phase 3: Contextual Role Templates
      // Institution Context Roles
      {
        name: 'InstitutionExplorer',
        description:
          'Provides read access to institution-specific data and related entities.',
        permissionGroups: [
          'CanViewInstitution',
          'CanViewPeople',
          'CanViewUnit',
          'CanViewBuilding',
          'CanViewRoom',
        ],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'InstitutionEditor',
        description:
          'Allows editing institution data and managing related entities within the institution context.',
        permissionGroups: [
          'CanEditInstitution',
          'CanCreateUnit',
          'CanEditUnit',
          'CanCreateBuilding',
          'CanEditBuilding',
          'CanCreateRoom',
          'CanEditRoom',
        ],
        inheritsFrom: ['InstitutionExplorer'],
      },
      {
        name: 'InstitutionManager',
        description: 'Full management rights within an institution context.',
        permissionGroups: [
          'InstitutionManagement',
          'UnitManagement',
          'BuildingManagement',
          'RoomManagement',
        ],
        inheritsFrom: ['InstitutionEditor'],
      },

      // Unit Context Roles
      {
        name: 'UnitExplorer',
        description:
          'Provides read access to unit-specific data and related entities.',
        permissionGroups: ['CanViewUnit', 'CanViewPeople', 'CanViewRoom'],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'UnitEditor',
        description:
          'Allows editing unit data and managing related entities within the unit context.',
        permissionGroups: ['CanEditUnit', 'CanCreateRoom', 'CanEditRoom'],
        inheritsFrom: ['UnitExplorer'],
      },
      {
        name: 'UnitManager',
        description: 'Full management rights within a unit context.',
        permissionGroups: ['UnitManagement', 'RoomManagement'],
        inheritsFrom: ['UnitEditor'],
      },

      // Infrastructure Context Roles
      {
        name: 'InfrastructureExplorer',
        description:
          'Provides read access to infrastructure-specific data and related entities.',
        permissionGroups: ['CanViewPeople', 'CanViewRoom'],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'InfrastructureEditor',
        description:
          'Allows editing infrastructure data and managing related entities within the infrastructure context.',
        permissionGroups: ['CanEditInfrastructure'],
        inheritsFrom: ['InfrastructureExplorer'],
      },
      {
        name: 'InfrastructureManager',
        description: 'Full management rights within an infrastructure context.',
        permissionGroups: ['InfrastructureManagement'],
        inheritsFrom: ['InfrastructureEditor'],
      },

      // Equipment Context Roles
      {
        name: 'EquipmentExplorer',
        description:
          'Provides read access to equipment-specific data and related entities.',
        permissionGroups: ['CanViewPeople'],
        inheritsFrom: ['BaseExplorer'],
      },
      {
        name: 'EquipmentManager',
        description:
          'Full management rights for equipment within a specific context.',
        permissionGroups: ['EquipmentManagement'],
        inheritsFrom: ['EquipmentExplorer'],
      },
    ];

    // Generate roles INSERT statement
    let rolesInsertStatement =
      'INSERT INTO roles (id, name, description) VALUES\n';

    // Generate role permission groups and inheritance relationships
    const rolePermissionGroupRelationships: Array<{
      roleId: string;
      groupId: string;
    }> = [];
    const roleInheritanceRelationships: Array<{
      childRoleId: string;
      parentRoleId: string;
    }> = [];

    allRoles.forEach((role, index) => {
      const roleId = DbUtils.cuid2();
      rolesInsertStatement += `('${roleId}', '${role.name}', '${role.description}')${index === allRoles.length - 1 ? ';' : ',\n'}`;
      this.roleIdMappings[role.name] = roleId;

      // Process permission groups for this role
      for (const permissionGroupName of role.permissionGroups) {
        const groupId = this.permissionGroupIdMappings[permissionGroupName];
        if (groupId) {
          rolePermissionGroupRelationships.push({ roleId, groupId });
        } else {
          console.warn(
            `Permission group ID not found for: ${permissionGroupName}`,
          );
        }
      }

      // Process inheritance relationships for this role
      for (const parentRoleName of role.inheritsFrom) {
        const parentRoleId = this.roleIdMappings[parentRoleName];
        if (parentRoleId) {
          roleInheritanceRelationships.push({
            childRoleId: roleId,
            parentRoleId,
          });
        } else {
          console.warn(
            `Parent role ID not found for: ${parentRoleName} (child: ${role.name})`,
          );
        }
      }
    });

    // Generate role permission groups INSERT statement
    let rolePermissionGroupsInsertStatement = '';
    if (rolePermissionGroupRelationships.length > 0) {
      rolePermissionGroupsInsertStatement =
        '\nINSERT INTO role_permission_groups (role_id, group_id) VALUES\n';

      rolePermissionGroupRelationships.forEach((relationship, index) => {
        rolePermissionGroupsInsertStatement += `('${relationship.roleId}', '${relationship.groupId}')${index === rolePermissionGroupRelationships.length - 1 ? ' ON CONFLICT DO NOTHING;' : ',\n'}`;
      });
    }

    // Generate role inheritance INSERT statement
    let roleInheritanceInsertStatement = '';
    if (roleInheritanceRelationships.length > 0) {
      roleInheritanceInsertStatement =
        '\nINSERT INTO role_inheritance (child_role_id, parent_role_id) VALUES\n';

      roleInheritanceRelationships.forEach((relationship, index) => {
        roleInheritanceInsertStatement += `('${relationship.childRoleId}', '${relationship.parentRoleId}')${index === roleInheritanceRelationships.length - 1 ? ' ON CONFLICT DO NOTHING;' : ',\n'}`;
      });
    }

    // Create the output directory if it doesn't exist
    const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
    await this.safeMkdir(outputDir, { recursive: true });

    await this.writeMappingsToJson(
      outputDir,
      { mysql: 'roles', postgres: 'roles' },
      this.roleIdMappings,
    );

    // Combine all SQL statements
    const allSqlStatements =
      rolesInsertStatement +
      rolePermissionGroupsInsertStatement +
      roleInheritanceInsertStatement;

    console.log(rolesInsertStatement);
    console.log(rolePermissionGroupsInsertStatement);
    console.log(roleInheritanceInsertStatement);

    // Append the SQL content to the output file
    await this.safeAppendFile(OUTPUT_PATH, allSqlStatements);
    console.log(
      'Base, global, and contextual roles and relationships data added successfully!',
    );
  }
}
