'use client';

import { buildingsColumns } from '@/app/[locale]/bottin/batiments/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/buildings';
import type { SupportedLocale } from '@/types/locale';
import type { DbBuilding } from '@rie/domain/types';

type BuildingsListProps = {
  locale: SupportedLocale;
};

export const BuildingsList = ({ locale }: BuildingsListProps) => {
  return (
    <DirectoryList<'building', 'list', DbBuilding, Record<string, unknown>>
      directoryListKey="building"
      view="list"
      locale={locale}
      columns={buildingsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="buildings"
    />
  );
};
