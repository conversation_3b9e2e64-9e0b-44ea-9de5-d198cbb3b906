import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  FundingProjectsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  FundingProjectsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const FundingProjectsServicesLayer = Layer.mergeAll(
  FundingProjectsRepositoryLive.Default,
  FundingProjectsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const FundingProjectsRuntime = ManagedRuntime.make(
  FundingProjectsServicesLayer,
);
