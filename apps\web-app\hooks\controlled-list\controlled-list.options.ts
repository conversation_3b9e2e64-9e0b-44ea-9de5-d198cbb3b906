import {
    getControlledListSelect,
    getMultipleControlledListsSelect,
} from '@/services/controlled-list/controlled-list.service';
import type { ControlledListKey } from '@/types/common';
import type { ControlledListSelectResult } from '@/types/controlled-list/controlled-list.type';
import type { SupportedLocale } from '@/types/locale';
import { queryOptions } from '@tanstack/react-query';

export const getControlledListSelectOptions = <
    Key extends ControlledListKey,
    locale extends SupportedLocale,
>({
    controlledListKey,
    locale,
}: {
    controlledListKey: Key;
    locale: locale;
}) => {
    return queryOptions<ControlledListSelectResult>({
        queryFn: () => getControlledListSelect({ controlledListKey }),
        queryKey: ['controlled-list', controlledListKey, 'select', locale],
    });
};

// Option pour récupérer plusieurs listes contrôlées en une seule requête (plus efficace)
export const getMultipleControlledListsSelectOptions = <
    locale extends SupportedLocale,
>({
    controlledListKeys,
    locale,
}: {
    controlledListKeys: ControlledListKey[];
    locale: locale;
}) => {
    return queryOptions<Record<string, ControlledListSelectResult>>({
        queryFn: () => getMultipleControlledListsSelect(controlledListKeys),
        queryKey: ['controlled-lists', 'multiple', controlledListKeys.sort(), 'select', locale],
    });
};
