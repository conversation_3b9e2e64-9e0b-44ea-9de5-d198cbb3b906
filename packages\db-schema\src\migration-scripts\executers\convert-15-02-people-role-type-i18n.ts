import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { PeopleRoleTypeI18nMigrationConverter } from '../converters/15-02-convert-people-role-type-i18n-inserts';

async function convertPeopleRoleTypeI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new PeopleRoleTypeI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertPeopleRoleTypeI18nData().catch(console.error);
