import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RoomsRuntime } from '@/infrastructure/runtimes/rooms.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  RoomDetailResponseDtoSchema,
  RoomEditResponseDtoSchema,
  RoomFormRequestDtoSchema,
  RoomListItemDtoSchema,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema
} from '@rie/domain/schemas';
import { RoomsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllRoomsRoute = describeRoute({
  description: 'Lister toutes les salles',
  operationId: 'getAllRooms',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(RoomListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Salles retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const getRoomByIdRoute = describeRoute({
  description: 'Obtenir une salle par ID',
  operationId: 'getRoomById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue: detail ou edit',
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              RoomDetailResponseDtoSchema,
              RoomEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Salle trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Salle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const createRoomRoute = describeRoute({
  description: 'Créer une salle',
  operationId: 'createRoom',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoomFormRequestDtoSchema),
        example: {
          number: 'NEW-ROOM-001',
          area: 25.5,
          floorLoad: null,
          building: {
            value: 'bw6c4rifj8239sc6gheruphi',
            label: 'Main Building',
          },
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(RoomDetailResponseDtoSchema),
        },
      },
      description: 'Salle créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - Le bâtiment n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const updateRoomRoute = describeRoute({
  description: 'Mettre à jour une salle',
  operationId: 'updateRoom',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
      example: 'vot1nira0alg10houmycxefw',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoomFormRequestDtoSchema),
        example: {
          number: 'UPDATED-ROOM-001',
          area: 30.0,
          floorLoad: null,
          building: {
            value: 'bw6c4rifj8239sc6gheruphi',
            label: 'Main Building',
          },
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(RoomDetailResponseDtoSchema),
        },
      },
      description: 'Salle mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Salle non trouvée ou le bâtiment n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

export const deleteRoomRoute = describeRoute({
  description: 'Supprimer une salle',
  operationId: 'deleteRoom',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la salle (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Salle supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Salle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Rooms'],
});

const roomsRoute = new Hono<{
  Variables: HonoVariables;
}>();

roomsRoute.get(
  '/',
  getAllRoomsRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.getAllRoomsWithView({ view });
    }).pipe(
      withPolicy(checkPermission('room:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Rooms][GET /] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Rooms][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Rooms][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.get(
  '/:id',
  getRoomByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');
    const { id } = ctx.req.valid('param');
    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    console.log('[Route][Rooms][GET /:id] id=%s view=%s locale=%s fallback=%s', id, view, locale, fallbackLocale);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.getRoomByIdWithView({ id, view });
    }).pipe(
      withPolicy(checkPermission('room:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.post(
  '/',
  createRoomRoute,
  // Debug incoming body before schema validation to investigate 400s  
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Rooms][POST] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Rooms][POST] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Rooms][POST] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Rooms][POST] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Rooms][POST] Calling room service...');
      const roomService = yield* RoomsServiceLive;
      console.log('[Route][Rooms][POST] Room service obtained, calling createRoom...');
      try {
        const result = yield* roomService.createRoom({
          number: body.number,
          area: body.area,
          floorLoad: body.floorLoad,
          buildingId: body.building?.value || body.buildingId,
          modifiedBy: user.id,
        });
        console.log('[Route][Rooms][POST] Service call completed successfully');
        return result;
      } catch (error) {
        console.error('[Route][Rooms][POST] Service call failed:', error);
        throw error;
      }
    }).pipe(
      withPolicy(checkPermission('room:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Rooms][POST] Running program...');
    const result = await RoomsRuntime.runPromiseExit(program);
    console.log('[Route][Rooms][POST] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Rooms][POST] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Rooms][POST] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Rooms][POST] Success! Returning result');
      return ctx.json(result.value, 201);
    }

    console.log('[Route][Rooms][POST] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.put(
  '/:id',
  updateRoomRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  // Debug incoming body before schema validation to investigate 400s
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Rooms][PUT] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Rooms][PUT] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Rooms][PUT] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Rooms][PUT] Room ID:', id);
    console.log('[Route][Rooms][PUT] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Rooms][PUT] Calling room service...');
      const roomService = yield* RoomsServiceLive;
      console.log('[Route][Rooms][PUT] Room service obtained, calling updateRoom...');
      const result = yield* roomService.updateRoom({
        id,
        room: {
          number: body.number,
          area: body.area,
          floorLoad: body.floorLoad,
          buildingId: body.building?.value || body.buildingId,
          modifiedBy: user.id,
        },
      });
      console.log('[Route][Rooms][PUT] Service call completed successfully');
      return result;
    }).pipe(
      withPolicy(checkPermission('room:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Rooms][PUT] Running program...');
    const result = await RoomsRuntime.runPromiseExit(program);
    console.log('[Route][Rooms][PUT] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Rooms][PUT] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Rooms][PUT] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Rooms][PUT] Success! Returning result');
      return ctx.json(result.value);
    }

    console.log('[Route][Rooms][PUT] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

roomsRoute.delete(
  '/:id',
  deleteRoomRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const roomService = yield* RoomsServiceLive;
      return yield* roomService.deleteRoom(id);
    }).pipe(
      withPolicy(checkPermission('room:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await RoomsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Room deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { roomsRoute };

