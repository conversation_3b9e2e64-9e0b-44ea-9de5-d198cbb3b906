# Installation Guide

This guide provides detailed installation instructions for the RIE development environment.

## 📋 Prerequisites

### Required Software

1. **Node.js** (v20 or higher)

   ```bash
   # Check version
   node --version
   
   # Install via volta (recommended)
   curl https://get.volta.sh | bash
   volta install node
   ```

2. **pnpm** (v10 or higher)

   ```bash
   # Install pnpm
   npm install -g pnpm
   
   # Check version
   pnpm --version
   ```

3. **Bun** (latest)

   ```bash
   # Install Bun
   curl -fsSL https://bun.sh/install | bash
   
   # Check version
   bun --version
   ```

4. **Docker** and **Docker Compose**
   - [Install Docker Desktop](https://www.docker.com/products/docker-desktop/)
   - Verify installation: `docker --version && docker-compose --version`

5. **Git**

   ```bash
   # Check version
   git --version
   
   # Configure Git (if not already done)
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

### Optional but Recommended

- **VS Code** with recommended extensions (see [development-environment.md](./development-environment.md))
- **PostgreSQL client** (pgAdmin, DBeaver, or similar)

## 🚀 Installation Steps

### 1. Clone the Repository

```bash
# Clone the repository
git clone <repository-url>
cd rie-fullstack

# Check you're on the main branch
git branch
```

### 2. Install Dependencies

```bash
# Install all dependencies
pnpm install --frozen-lockfile

# This will install dependencies for:
# - Root workspace
# - All apps (api, web-app, web-app-e2e)
# - All packages (shared libraries)
```

### 3. Environment Configuration

#### API Environment (.env)

```bash
# At the root of the project copy the example env file
cp env.example .env

# Update the .env file with your settings
nano .env  # or use your preferred editor
```

### 4. Database Setup

```bash
# From the root directory
cd packages/db-schema
pnpm db:start
```

### 5. Build Shared Packages

```bash
# From root directory
pnpm build:packages

# This builds all shared packages that other apps depend on
```

### 6. Verify Installation

#### Start Development Servers

```bash
# From root directory - starts API and web-app
turbo dev --filter=@rie/api --filter=web-app

# Or start individually:
# Terminal 1: API
cd apps/api && pnpm dev

# Terminal 2: Web App
cd apps/web-app && pnpm dev
```

#### Check Services

1. **Web App**: <http://localhost:3000/rie>
   - Should show the RIE homepage
   - Try logging in/registering

2. **API**: <http://localhost:4000>
   - Should return API status

3. **API Documentation**: <http://localhost:4000/api/doc>
   - Should show Swagger UI with all endpoints

4. **Database Studio**:

   ```bash
   cd packages/db-schema && bun run db:studio-pg
   ```

   - Should open Drizzle Studio in browser

## 🔧 Configuration Details

### Database Configuration

The application uses PostgreSQL with the following setup:

- **DB**: `riedb`
- **Port**: 5432
- **User**: rie
- **Password**: rie123 (configurable in docker-compose.yml)

### PostgreSQL SSL/TLS Configuration

The application supports multiple SSL/TLS configuration options for database connections, providing flexibility for different deployment environments and security requirements.

#### SSL Configuration Options

The codebase supports four main SSL configuration approaches:

1. **`ssl: false`** - No encryption
   - Disables SSL/TLS entirely
   - Fastest performance (no encryption overhead)
   - **Security Risk**: Data transmitted in plain text

2. **`ssl: true`** - Basic SSL with default settings
   - Enables SSL with Node.js default TLS settings
   - Provides encryption but minimal certificate validation
   - Good balance of security and simplicity

3. **`ssl: { rejectUnauthorized: false }`** - SSL without certificate validation
   - Forces SSL encryption but skips certificate verification
   - Protects against network sniffing but not man-in-the-middle attacks
   - Suitable for staging environments with self-signed certificates

4. **`ssl: { rejectUnauthorized: true, ca: ..., cert: ..., key: ... }`** - Full SSL with certificate validation
   - Complete SSL/TLS security with proper certificate validation
   - Highest security level with mutual authentication
   - Required for production environments

#### Environment-Specific Usage

**Development Environment:**
```typescript
// Recommended: No SSL for local development
ssl: false
```
- **When to use**: Local development with localhost database
- **Benefits**: Maximum performance, no certificate management
- **Security**: Acceptable for local-only connections

**Staging Environment:**
```typescript
// Recommended: SSL without certificate validation
ssl: { rejectUnauthorized: false }
```
- **When to use**: Testing environments with self-signed certificates
- **Benefits**: Tests SSL configuration without certificate complexity
- **Security**: Protects against network sniffing, allows self-signed certs

**Production Environment:**
```typescript
// Required: Full SSL with certificate validation
ssl: {
  rejectUnauthorized: true,
  ca: process.env.DB_SSL_CA,
  cert: process.env.DB_SSL_CERT,
  key: process.env.DB_SSL_KEY,
}
```
- **When to use**: Production deployments
- **Benefits**: Maximum security with proper certificate validation
- **Security**: Complete protection against all SSL/TLS attacks

#### Security Context and Trade-offs

**Performance Impact:**
- **Connection Overhead**: 27-73% slower connection establishment (varies by key size)
- **Query Throughput**: 29-40% reduction in operations per second
- **CPU Usage**: 6-10% additional CPU overhead
- **Memory**: ~10-20KB additional memory per connection

**Security Benefits:**
- **Data Encryption**: Protects sensitive data in transit
- **Authentication**: Verifies database server identity
- **Integrity**: Prevents data tampering during transmission
- **Compliance**: Required for SOC 2, HIPAA, PCI DSS standards

**Network Context:**
- **Private Networks**: SSL still recommended for defense-in-depth
- **Public Networks**: SSL absolutely required
- **Internal Services**: Consider SSL for compliance and insider threat protection

#### Configuration Examples

The SSL configuration is managed in `packages/auth/src/lib/drizzle-client.ts`:

```typescript
const getSSLConfig = (
  connectionType: 'none' | 'verify-self' | 'verify-ca',
): boolean | ConnectionOptions => {
  switch (connectionType) {
    case 'verify-ca':
      // Production: Full certificate validation
      return {
        rejectUnauthorized: true,
        ca: process.env.DB_SSL_CA,
        cert: process.env.DB_SSL_CERT,
        key: process.env.DB_SSL_KEY,
      };
    case 'verify-self':
      // Staging: SSL without certificate validation
      return {
        rejectUnauthorized: false,
      };
    case 'none':
      // Development: No SSL
      return false;
    default:
      // Fallback: Basic SSL
      return true;
  }
};
```

**Environment Variable Configuration:**

Add to your `.env` file:

```env
# SSL Configuration
DATABASE_CONNECTION_SECURITY=none          # Development
# DATABASE_CONNECTION_SECURITY=verify-self # Staging
# DATABASE_CONNECTION_SECURITY=verify-ca   # Production

# Production SSL Certificates (when using verify-ca)
DB_SSL_CA=/path/to/ca-certificate.crt
DB_SSL_CERT=/path/to/client-certificate.crt
DB_SSL_KEY=/path/to/client-private.key
```

**Connection Pool Configuration:**

The SSL settings are applied to the connection pool:

```typescript
const pool = new Pool({
  connectionString: Redacted.value(env.PG_DATABASE_URL),
  ssl: getSSLConfig(env.DATABASE_CONNECTION_SECURITY),
  max: 20,                    // Connection pool size
  idleTimeoutMillis: 30000,   // Keep connections longer to amortize SSL handshake cost
  connectionTimeoutMillis: 2000, // Account for SSL handshake time
});
```

#### Best Practices

1. **Use Connection Pooling**: Reduces SSL handshake overhead by reusing connections
2. **Certificate Management**: Implement proper certificate rotation and monitoring
3. **Environment Separation**: Use different SSL configurations per environment
4. **Performance Testing**: Measure SSL impact in your specific deployment
5. **Compliance Alignment**: Choose SSL level based on regulatory requirements

#### Troubleshooting SSL Issues

**Common SSL Connection Errors:**

```bash
# Certificate verification failed
Error: self signed certificate in certificate chain
# Solution: Use rejectUnauthorized: false for staging

# SSL connection required
Error: connection requires SSL
# Solution: Enable SSL in client configuration

# Certificate file not found
Error: ENOENT: no such file or directory
# Solution: Verify certificate file paths and permissions
```

**Testing SSL Configuration:**

```bash
# Test SSL connection with psql
psql "postgresql://user:pass@host:port/db?sslmode=require"

# Verify SSL is active in database
SELECT ssl_is_used();
```

### Port Configuration

Default ports used:

- **Web App**: 3000
- **API**: 8000
- **PostgreSQL**: 5432
- **Storybook**: 6006 (when running)
- **Drizzle Studio**: 4983 (when running)

### Environment Variables Reference

#### API Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PG_DATABASE_URL` | PostgreSQL connection string | - | Yes |
| `BETTER_AUTH_SECRET` | Secret for auth tokens | - | Yes |
| `BETTER_AUTH_URL` | Auth service URL | - | Yes |
| `PORT` | API server port | 8000 | No |
| `NODE_ENV` | Environment mode | development | No |
| `CORS_ORIGIN` | Allowed CORS origins | * | No |

#### Web App Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_API_BASE_URL` | API base URL | - | Yes |
| `BETTER_AUTH_SECRET` | Secret for auth tokens | - | Yes |
| `BETTER_AUTH_URL` | Auth service URL | - | Yes |
| `DATABASE_URL` | Database URL for auth | - | Yes |

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Find and kill processes
lsof -ti:3000 | xargs kill -9
lsof -ti:8000 | xargs kill -9
```

#### Database Connection Failed

```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Restart PostgreSQL
docker-compose restart postgres

# Check logs
docker-compose logs postgres
```

#### Permission Denied (macOS/Linux)

```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) ~/.pnpm-store
```

#### Bun Installation Issues

```bash
# Reinstall Bun
curl -fsSL https://bun.sh/install | bash

# Add to PATH (if needed)
echo 'export PATH="$HOME/.bun/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Verification Commands

```bash
# Check all required tools
node --version
pnpm --version
bun --version
docker --version
git --version

# Check services
curl http://localhost:4000/health
curl http://localhost:3000

# Check database
docker-compose exec postgres psql -U postgres -d rie_dev -c "\dt"
```

## ✅ Next Steps

Once installation is complete:

1. **[Set up your development environment](./development-environment.md)**
2. **[Make your first contribution](./first-contribution.md)**
3. **[Learn the architecture](../architecture/README.md)**

---

**Having issues?** Check the [troubleshooting guide](../guides/troubleshooting.md) or ask for help!
