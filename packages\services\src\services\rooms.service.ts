import { Room } from '@rie/domain/aggregates';
import { RoomNotFoundError } from '@rie/domain/errors';
import type { RoomInputSchema } from '@rie/domain/schemas';
import type { CollectionViewType, ResourceViewType } from '@rie/domain/types';
import { BuildingsRepositoryLive, CampusesRepositoryLive, InstitutionsRepositoryLive, RoomsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';
import {
  serializeRoomToDetail,
  serializeRoomToFormEdit,
  serializeRoomToListItem,
  serializeRoomToSelectOption,
} from '../serializers';

type RoomInput = Schema.Schema.Type<typeof RoomInputSchema>;

export class RoomsServiceLive extends Effect.Service<RoomsServiceLive>()(
  'RoomsServiceLive',
  {
    dependencies: [RoomsRepositoryLive.Default, BuildingsRepositoryLive.Default, CampusesRepositoryLive.Default, InstitutionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllRooms = () =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          const allRooms = yield* repo.findAllRooms();
          return allRooms.filter((room) => room.isActive === true);
        });

      const getAllRoomsWithView = ({ view }: { view: CollectionViewType }) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          const roomDataArray = yield* repo.findAllRooms();
          const activeRooms = roomDataArray.filter((room) => room.isActive === true);

          // Serialize based on view type
          switch (view) {
            case 'list': {
              // Get buildings for building names and jurisdiction info
              const buildingsRepo = yield* BuildingsRepositoryLive;
              const buildings = yield* buildingsRepo.findAllBuildings();
              const buildingNameById = new Map<string, string>();
              const buildingCampusById = new Map<string, string>();

              for (const building of buildings) {
                // Use first available translation name or fallback to ID
                const translations = building.translations || [];
                const firstNameTranslation = translations.find(t => t.name && t.name.trim() !== '');
                const buildingName = firstNameTranslation?.name || building.sadId || building.id;
                buildingNameById.set(building.id, buildingName);

                // Store campus ID for jurisdiction lookup
                if (building.campusId) {
                  buildingCampusById.set(building.id, building.campusId);
                }
              }

              // Get campuses and institutions for jurisdiction
              const campusesRepo = yield* CampusesRepositoryLive;
              const institutionsRepo = yield* InstitutionsRepositoryLive;

              // Get all unique campus IDs
              const campusIds = Array.from(new Set(buildingCampusById.values()));

              // Build jurisdiction lookup map
              const jurisdictionById = new Map<string, string | null>();

              for (const campusId of campusIds) {
                try {
                  const campus = yield* campusesRepo.findCampusById(campusId);
                  if (campus) {
                    const campusData = yield* campus.toRaw();
                    if (campusData.institutionId) {
                      const institution = yield* institutionsRepo.findInstitutionById(campusData.institutionId);
                      if (institution && (institution as unknown as { translations?: Array<{ locale: string; name: string | null }> }).translations) {
                        const instTranslations = (institution as unknown as {
                          translations: Array<{
                            locale: string;
                            name: string | null;
                          }>
                        }).translations;
                        // For now, use the first available name (could be enhanced with locale support)
                        const anyNameInst = instTranslations.find((t) => !!t.name && t.name.trim() !== '');
                        const jurisdictionLabel = anyNameInst?.name ?? null;
                        jurisdictionById.set(campusId, jurisdictionLabel);
                      }
                    }
                  }
                } catch {
                  // If campus or institution not found, set to null
                  jurisdictionById.set(campusId, null);
                }
              }

              // Convert room data to aggregates and serialize
              const roomEffects = activeRooms.map((roomData) =>
                Effect.gen(function* () {
                  const room = yield* Room.fromDatabaseData(roomData);
                  const dto = yield* serializeRoomToListItem(room);

                  // Get jurisdiction through building -> campus -> institution chain
                  let jurisdiction: string | null = null;
                  if (roomData.buildingId) {
                    const campusId = buildingCampusById.get(roomData.buildingId);
                    if (campusId) {
                      jurisdiction = jurisdictionById.get(campusId) ?? null;
                    }
                  }

                  return {
                    ...dto,
                    building: roomData.buildingId ? buildingNameById.get(roomData.buildingId) ?? null : null,
                    jurisdiction,
                  };
                })
              );
              return yield* Effect.all(roomEffects);
            }
            case 'select': {
              // Convert room data to aggregates and serialize
              const roomEffects = activeRooms.map((roomData) =>
                Effect.gen(function* () {
                  const room = yield* Room.fromDatabaseData(roomData);
                  return yield* serializeRoomToSelectOption(room);
                })
              );
              return yield* Effect.all(roomEffects);
            }
            default:
              return yield* Effect.fail(new Error(`Unknown view type: ${view}`));
          }
        });      // Internal method for repository access (used by updateRoom, deleteRoom)
      const findRoomByIdInternal = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          const room = yield* repo.findRoomById(id);
          if (!room) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          return room;
        });

      // Public method with serializer (used by API routes)
      const getRoomById = (id: string) =>
        Effect.gen(function* () {
          const room = yield* findRoomByIdInternal(id);
          return room;
        });

      const getRoomByIdWithView = ({ id, view }: { id: string; view: ResourceViewType }) =>
        Effect.gen(function* () {
          const roomData = yield* findRoomByIdInternal(id);

          switch (view) {
            case 'detail': {
              const room = yield* Room.fromDatabaseData(roomData);
              return yield* serializeRoomToDetail(room);
            }
            case 'edit':
              return yield* serializeRoomToFormEdit(roomData);
            default:
              return yield* Effect.fail(new Error(`Unknown view type: ${view}`));
          }
        });

      const createRoom = (room: RoomInput) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          return yield* repo.createRoom({ room });
        });

      const updateRoom = ({ id, room }: { room: RoomInput; id: string }) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          // Check if room exists using internal method
          const existingRoom = yield* findRoomByIdInternal(id);

          if (!existingRoom) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          return yield* repo.updateRoom({
            roomId: id,
            room,
          });
        });

      const deleteRoom = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          // Check if room exists using internal method
          const existingRoom = yield* findRoomByIdInternal(id);
          if (!existingRoom) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          const result = yield* repo.deleteRoom(id);
          return result.length > 0;
        });

      return {
        getAllRooms,
        getAllRoomsWithView,
        getRoomById,
        getRoomByIdWithView,
        createRoom,
        updateRoom,
        deleteRoom,
      } as const;
    }),
  },
) { }
