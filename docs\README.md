# RIE Documentation

Welcome to the RIE (Research Infrastructure and Equipment) management system documentation. This guide will help you understand, contribute to, and work with the RIE platform.

## 🚀 Quick Start

New to the project? Start here:

1. **[Getting Started](./getting-started/README.md)** - Set up your development environment
2. **[Installation Guide](./getting-started/installation.md)** - Step-by-step setup instructions
3. **[Your First Contribution](./getting-started/first-contribution.md)** - Make your first code change

## 📚 Documentation Sections

### 🏗️ Architecture

Understand how the system is built:

- **[Architecture Overview](./architecture/README.md)** - High-level system design
- **[Monorepo Structure](./architecture/monorepo-structure.md)** - How the codebase is organized
- **[API Architecture](./architecture/api-architecture.md)** - Backend design patterns
- **[Frontend Architecture](./architecture/frontend-architecture.md)** - Frontend patterns and conventions
- **[Database Design](./architecture/database-design.md)** - Database schema and relationships

### 🛠️ Development Guides

Practical guides for daily development:

- **[API Development](./guides/api-development.md)** - Building and extending the API
- **[Frontend Development](./guides/frontend-development.md)** - Working with the Next.js frontend
- **[Testing](./guides/testing.md)** - Testing strategies and best practices
- **[Deployment](./guides/deployment.md)** - How to deploy the application
- **[Troubleshooting](./guides/troubleshooting.md)** - Common issues and solutions

### 🔌 API Documentation

API-specific documentation:

- **[API Overview](./api/README.md)** - API structure and conventions
- **[Authentication](./api/authentication.md)** - How authentication works
- **[Permissions System](./api/permissions.md)** - Role-based access control
- **[API Endpoints](./api/endpoints/)** - Detailed endpoint documentation

### 🤝 Contributing

Guidelines for contributing to the project:

- **[Contributing Guide](./contributing/README.md)** - How to contribute
- **[Code Style](./contributing/code-style.md)** - Coding standards and conventions
- **[Pull Request Process](./contributing/pull-request-process.md)** - How to submit changes
- **[Release Process](./contributing/release-process.md)** - How releases are managed

## 🏃‍♂️ Quick Commands

```bash
# Install dependencies
pnpm install --frozen-lockfile

# Start development
pnpm dev

# Run tests
pnpm test

# Build everything
pnpm build

# Lint and format
pnpm lint
pnpm format
```

## 🆘 Need Help?

- **Issues**: Check the [troubleshooting guide](./guides/troubleshooting.md)
- **Questions**: Ask in team chat or create an issue
- **Bugs**: Report bugs through GitHub issues
- **Features**: Discuss new features with the team first

## 📖 Additional Resources

- **[Project README](../README.md)** - Basic project information
- **[CLAUDE.md](../CLAUDE.md)** - AI assistant guidance
- **[API README](../apps/api/README.md)** - API-specific details
- **[Web App README](../apps/web-app/README.md)** - Frontend-specific details

---

**Last Updated**: January 2025  
**Maintained By**: RIE Development Team
