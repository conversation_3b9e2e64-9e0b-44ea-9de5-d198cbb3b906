import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { UnitMigrationConverter } from '../converters/24-01-convert-unit-inserts';

async function convertUnitData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new UnitMigrationConverter();
    // Convert the file
    await converter.convertFile();
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertUnitData().catch(console.error);
