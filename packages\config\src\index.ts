import * as Config from 'effect/Config';
import * as Effect from 'effect/Effect';

import { dirname, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { config } from 'dotenv';

const currentFilename = fileURLToPath(import.meta.url);
const currentDirname = dirname(currentFilename);

// Multi-context environment loading
const loadEnvironmentVariables = () => {
  // Try multiple .env file locations
  const envPaths = [
    resolve(process.cwd(), '.env'), // Workspace root
    resolve(process.cwd(), '.env.local'), // Local overrides
    resolve(currentDirname, '../../../.env'), // Loads from project root
  ];

  for (const path of envPaths) {
    try {
      config({ path, override: false }); // Don't override existing values
    } catch {
      // Silently continue if file doesn't exist
    }
  }
};

// Load environment variables when module is imported
loadEnvironmentVariables();

export class ConfigLive extends Effect.Service<ConfigLive>()('ConfigClient', {
  accessors: true,
  effect: Effect.gen(function* () {
    return {
      // Client
      NEXT_PUBLIC_RIE_API_URL: yield* Config.string(
        'NEXT_PUBLIC_RIE_API_URL',
      ).pipe(Config.withDefault('https://rie-devel.cen.umontreal.ca/api/')), // TODO: To be removed
      NEXT_PUBLIC_RIE_AUTH_URL: yield* Config.string(
        'NEXT_PUBLIC_RIE_AUTH_URL',
      ).pipe(Config.withDefault('https://rie-devel.cen.umontreal.ca/api/auth')), // TODO: To be removed
      NEXT_PUBLIC_ORIGIN_URL: yield* Config.string(
        'NEXT_PUBLIC_ORIGIN_URL',
      ).pipe(Config.withDefault('http://localhost:3000')),
      NEXT_PUBLIC_API_BASE_URL: yield* Config.string(
        'NEXT_PUBLIC_API_BASE_URL',
      ).pipe(Config.withDefault('http://localhost:4000/api')),
      NEXT_PUBLIC_BASE_PATH: yield* Config.string('NEXT_PUBLIC_BASE_PATH').pipe(
        Config.withDefault('/rie'),
      ),
      NEXT_PUBLIC_VERSION: yield* Config.string('NEXT_PUBLIC_VERSION').pipe(
        Config.withDefault('1.0.0'),
      ),
      NEXT_PUBLIC_POSTHOG_KEY: yield* Config.string(
        'NEXT_PUBLIC_POSTHOG_KEY',
      ).pipe(
        Config.withDefault('phc_mEE5kxvnVvBdFiXNOUX0Yn3p3WtFmFprU1bCFu1XjXB'),
      ),
      NEXT_PUBLIC_POSTHOG_HOST: yield* Config.string(
        'NEXT_PUBLIC_POSTHOG_HOST',
      ).pipe(Config.withDefault('https://us.i.posthog.com')),
      // Server
      logLevel: 'info',
      PG_DATABASE_URL: yield* Config.redacted('PG_DATABASE_URL'),
      PORT: yield* Config.integer('PORT').pipe(Config.withDefault(4000)),
      BETTER_AUTH_SECRET: yield* Config.redacted('BETTER_AUTH_SECRET'),
      GITHUB_CLIENT_ID: yield* Config.redacted('GITHUB_CLIENT_ID'),
      GITHUB_CLIENT_SECRET: yield* Config.redacted('GITHUB_CLIENT_SECRET'),
      MICROSOFT_CLIENT_ID: yield* Config.redacted('MICROSOFT_CLIENT_ID'),
      DATABASE_CONNECTION_SECURITY: yield* Config.literal(
        'none', // No SSL, usually for local development
        'verify-self', // Verify server identity, but don't verify certificate, usually for development server
        'verify-ca', // Verify server identity and certificate, usually for production
      )('DATABASE_CONNECTION_SECURITY').pipe(Config.withDefault('none')),
      MICROSOFT_CLIENT_SECRET: yield* Config.redacted(
        'MICROSOFT_CLIENT_SECRET',
      ),
      RIE_AUTH_CLIENT_ID: yield* Config.redacted('RIE_AUTH_CLIENT_ID'), // TODO: To be removed
      RIE_AUTH_CLIENT_SECRET: yield* Config.redacted('RIE_AUTH_CLIENT_SECRET'), // TODO: To be removed
      dialect: 'postgresql',
      NODE_ENV: yield* Config.literal(
        'development',
        'production',
        'staging',
      )('NODE_ENV').pipe(Config.withDefault('development')),
    };
  }),
}) {}

export const env = Effect.runSync(
  Effect.provide(ConfigLive, ConfigLive.Default),
);
