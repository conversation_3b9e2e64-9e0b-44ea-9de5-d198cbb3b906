import { RoleAlreadyExistsError, RoleNotFoundError } from '@rie/domain/errors';
import {
  RoleInputToDBInput,
  dbRoleToRoleResource,
  dbRolesToRoleCollection,
} from '@rie/domain/serializers';
import { DbRoleToRoleDetail } from '@rie/domain/serializers';
import type {
  CollectionViewParamType,
  ResourceViewType,
  RoleInput,
} from '@rie/domain/types';
import { RolesRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

interface UpdateRoleParams extends RoleInput {
  id: string;
}

export class RolesServiceLive extends Effect.Service<RolesServiceLive>()(
  'RolesServiceLive',
  {
    dependencies: [RolesRepositoryLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get all roles
       */
      const getAllRoles = ({ view }: CollectionViewParamType) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;
          const roles = yield* rolesRepository.findAllRoles();
          const t1 = performance.now();

          console.log(`Call to getAllRoles took ${t1 - t0} milliseconds.`);

          return dbRolesToRoleCollection(roles, view);
        });
      };

      /**
       * Get a role by ID with all its relationships
       */
      const getRoleById = (
        id: string,
        { view }: { view: ResourceViewType },
      ) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;
          const role = yield* rolesRepository.findRoleById(id);

          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          const t1 = performance.now();
          console.log(`Call to getRoleById took ${t1 - t0} milliseconds.`);

          return dbRoleToRoleResource(role, view);
        });
      };

      const createRole = (input: RoleInput) => {
        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Transform input to database format
          const dbInput = Schema.decodeUnknownSync(RoleInputToDBInput)(input);
          // Check if role already exists
          const existingRole = yield* rolesRepository.checkRoleExists(
            dbInput.name,
          );

          if (existingRole) {
            return yield* Effect.fail(
              new RoleAlreadyExistsError({ name: dbInput.name }),
            );
          }

          const createdRole = yield* rolesRepository.createRole(dbInput);

          return Schema.decodeUnknownSync(DbRoleToRoleDetail)(createdRole);
        });
      };

      /**
       * Update a role
       */
      const updateRole = ({ id, ...input }: UpdateRoleParams) => {
        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Transform input to database format
          const dbInput = Schema.decodeUnknownSync(RoleInputToDBInput)(input);
          // Check if role exists
          const role = yield* rolesRepository.findRoleById(id);
          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          // Check if another role with the same name already exists
          const existingRole = yield* rolesRepository.checkRoleExists(
            dbInput.name,
          );
          if (existingRole && existingRole.id !== id) {
            return yield* Effect.fail(
              new RoleAlreadyExistsError({ name: dbInput.name }),
            );
          }

          const updatedRole = yield* rolesRepository.updateRole(dbInput, id);

          return Schema.decodeUnknownSync(DbRoleToRoleDetail)(updatedRole);
        });
      };

      /**
       * Delete a role
       */
      const deleteRole = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(id);
          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          const result = yield* rolesRepository.deleteRole(id);
          const t1 = performance.now();

          console.log(`Call to deleteRole took ${t1 - t0} milliseconds.`);

          return result.length > 0;
        });
      };

      /**
       * Get all permissions for a role (direct, inherited, and from groups)
       */
      const getRolePermissions = (roleId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(roleId);
          if (!role) {
            return yield* Effect.fail(new Error('Role not found'));
          }

          const roleWithPermissions =
            yield* rolesRepository.getRolePermissions(roleId);
          const t1 = performance.now();

          console.log(
            `Call to getRolePermissions took ${t1 - t0} milliseconds.`,
          );

          return roleWithPermissions;
        });
      };

      /**
       * Get all permission groups for a role
       */
      const getRolePermissionGroups = (roleId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(roleId);
          if (!role) {
            return yield* Effect.fail(new Error('Role not found'));
          }

          const permissionGroups =
            yield* rolesRepository.getRolePermissionGroups(roleId);
          const t1 = performance.now();

          console.log(
            `Call to getRolePermissionGroups took ${t1 - t0} milliseconds.`,
          );

          return permissionGroups;
        });
      };

      return {
        getAllRoles,
        getRoleById,
        createRole,
        updateRole,
        deleteRole,
        getRolePermissions,
        getRolePermissionGroups,
      } as const;
    }),
  },
) {}
