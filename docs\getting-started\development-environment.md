# Development Environment Setup

This guide helps you configure your development environment for optimal productivity with the RIE project.

## 🎯 Recommended IDE: Visual Studio Code

### Required Extensions

Install these extensions for the best development experience:

```bash
# Install via command line
code --install-extension bradlc.vscode-tailwindcss
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension biomejs.biome
code --install-extension ms-playwright.playwright
code --install-extension bradlc.vscode-tailwindcss
code --install-extension ms-vscode.vscode-json
```

#### Essential Extensions

1. **TypeScript and JavaScript**
   - `ms-vscode.vscode-typescript-next` - Enhanced TypeScript support
   - `ms-vscode.vscode-json` - JSON language support

2. **Code Quality**
   - `biomejs.biome` - Linting and formatting (replaces ESLint + Prettier)

3. **Frontend Development**
   - `bradlc.vscode-tailwindcss` - Tailwind CSS IntelliSense
   - `ms-vscode.vscode-css-peek` - CSS navigation

4. **Testing**
   - `ms-playwright.playwright` - Playwright test support
   - `vitest.explorer` - Vitest test runner

5. **Database**
   - `ms-mssql.sql-database-projects-vscode` - SQL support
   - `cweijan.vscode-postgresql-client2` - PostgreSQL client

### VS Code Settings

Create or update `.vscode/settings.json` in the project root:

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  
  "files.associations": {
    "*.css": "tailwindcss"
  },
  
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.next": true,
    "**/coverage": true
  }
}
```

### VS Code Tasks

Create `.vscode/tasks.json` for common development tasks:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Development",
      "type": "shell",
      "command": "pnpm dev",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Run Tests",
      "type": "shell",
      "command": "pnpm test",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    },
    {
      "label": "Build All",
      "type": "shell",
      "command": "pnpm build",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    }
  ]
}
```

## 🔧 Terminal Setup

### Recommended Terminal: iTerm2 (macOS) or Windows Terminal

### Shell Configuration

Add these aliases to your shell profile (`.bashrc`, `.zshrc`, etc.):

```bash
# RIE Project Aliases
alias rie-dev="cd /path/to/rie-fullstack && pnpm dev"
alias rie-api="cd /path/to/rie-fullstack/apps/api"
alias rie-web="cd /path/to/rie-fullstack/apps/web-app"
alias rie-test="cd /path/to/rie-fullstack && pnpm test"
alias rie-build="cd /path/to/rie-fullstack && pnpm build"

# Database shortcuts
alias rie-db-studio="cd /path/to/rie-fullstack/apps/api && bun run db:studio-pg"
alias rie-db-migrate="cd /path/to/rie-fullstack/apps/api && bun run db:migrate-pg"
alias rie-db-seed="cd /path/to/rie-fullstack/apps/api && bun run db:seed-pg"

# Docker shortcuts
alias rie-docker-up="cd /path/to/rie-fullstack && docker-compose up -d"
alias rie-docker-down="cd /path/to/rie-fullstack && docker-compose down"
alias rie-docker-logs="cd /path/to/rie-fullstack && docker-compose logs -f"
```

## 🛠️ Development Tools

### Git Configuration

Configure Git for the project:

```bash
# Set up Git hooks (if using Husky)
cd rie-fullstack
pnpm prepare

# Configure Git for better commit messages
git config --local commit.template .gitmessage
```

### Database Tools

#### Option 1: Drizzle Studio (Built-in)

```bash
cd apps/api
bun run db:studio-pg
# Opens at http://localhost:4983
```

#### Option 2: pgAdmin (External)

- Download from [pgAdmin.org](https://www.pgadmin.org/)
- Connect to `localhost:5432` with credentials from docker-compose.yml

#### Option 3: DBeaver (Free Universal Tool)

- Download from [DBeaver.io](https://dbeaver.io/)
- Create PostgreSQL connection to `localhost:5432`

### API Testing Tools

#### Option 1: Built-in Swagger UI

- Available at <http://localhost:8000/doc> when API is running
- Interactive API documentation and testing

#### Option 2: Insomnia/Postman

- Import the API collection from `tests/insomnia-workspace.json`
- Pre-configured requests for all endpoints

#### Option 3: curl/httpie

```bash
# Test API health
curl http://localhost:8000/health

# Test with authentication
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/v2/users
```

## 🎨 Code Quality Setup

### Biome Configuration

The project uses Biome for linting and formatting. Configuration is in `biome.jsonc`:

```bash
# Check code quality
pnpm lint

# Fix auto-fixable issues
pnpm lint:fix

# Format code
pnpm format
```

### Pre-commit Hooks

The project uses Husky for Git hooks:

```bash
# Install hooks
pnpm prepare

# Hooks will run automatically on:
# - pre-commit: lint and format
# - pre-push: run tests
```

## 🧪 Testing Setup

### Test Runners

1. **Vitest** (Unit/Integration tests)

   ```bash
   # Run all tests
   pnpm test
   
   # Run tests in watch mode
   pnpm test:watch
   
   # Run with coverage
   pnpm test:coverage
   ```

2. **Playwright** (E2E tests)

   ```bash
   cd apps/web-app-e2e
   
   # Run E2E tests
   pnpm e2e:test
   
   # Run headless
   pnpm e2e:headless
   ```

3. **Storybook** (Component testing)

   ```bash
   cd apps/web-app
   pnpm storybook
   # Opens at http://localhost:6006
   ```

## 🔍 Debugging Setup

### VS Code Debugging

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug API",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/apps/api/src/index.ts",
      "cwd": "${workspaceFolder}/apps/api",
      "runtimeExecutable": "bun",
      "runtimeArgs": ["--inspect"],
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "name": "Debug Next.js",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/apps/web-app/node_modules/.bin/next",
      "args": ["dev"],
      "cwd": "${workspaceFolder}/apps/web-app",
      "env": {
        "NODE_OPTIONS": "--inspect"
      }
    }
  ]
}
```

### Browser DevTools

For frontend debugging:

- **React DevTools**: Install browser extension
- **Redux DevTools**: For state management debugging
- **Network tab**: Monitor API calls
- **Console**: Check for errors and logs

## 📱 Mobile Development (Optional)

If working on mobile-responsive features:

```bash
# Test on different devices
cd apps/web-app
pnpm dev -- --hostname 0.0.0.0

# Access from mobile device on same network
# http://YOUR_IP:3000
```

## 🚀 Performance Monitoring

### Development Tools

1. **Next.js Bundle Analyzer**

   ```bash
   cd apps/web-app
   ANALYZE=true pnpm build
   ```

2. **Lighthouse** (built into Chrome DevTools)
   - Test performance, accessibility, SEO

3. **React Profiler** (React DevTools)
   - Profile component performance

## ✅ Environment Verification

Run this checklist to verify your setup:

```bash
# 1. Check tool versions
node --version    # Should be v18+
pnpm --version    # Should be v8+
bun --version     # Should be latest
docker --version  # Should be installed

# 2. Check project setup
cd rie-fullstack
pnpm install --frozen-lockfile  # Should complete without errors
pnpm build:packages             # Should build successfully

# 3. Check services
pnpm dev                        # Should start all services
# Visit http://localhost:3000    # Should load web app
# Visit http://localhost:8000    # Should return API status

# 4. Check database
cd apps/api
bun run db:studio-pg           # Should open Drizzle Studio

# 5. Check tests
cd ../..
pnpm test                      # Should run tests successfully
```

## 🆘 Troubleshooting

### Common Issues

1. **TypeScript errors in VS Code**
   - Restart TypeScript server: `Cmd/Ctrl + Shift + P` → "TypeScript: Restart TS Server"

2. **Biome not working**
   - Check if Biome extension is installed and enabled
   - Restart VS Code

3. **Database connection issues**
   - Verify PostgreSQL is running: `docker-compose ps`
   - Check environment variables in `.env` files

4. **Port conflicts**
   - Kill processes: `lsof -ti:3000 | xargs kill -9`

---

**Next**: [Make your first contribution](./first-contribution.md)
