'use client';

import { roomsColumns } from '@/app/[locale]/bottin/locaux/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/rooms';
import type { SupportedLocale } from '@/types/locale';
import type { RoomListItemDTO } from '@rie/api-contracts';

type RoomsListProps = {
  locale: SupportedLocale;
};

export const RoomsList = ({ locale }: RoomsListProps) => {
  return (
    <DirectoryList<'local', 'list', RoomListItemDTO, Record<string, unknown>>
      directoryListKey="local"
      view="list"
      locale={locale}
      columns={roomsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="rooms"
    />
  );
};
