'use client';

import { Header<PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { TableColumnsSelect } from '@/components/table-column-select/table-columns-select';
import {
  type ColumnDef,
  type ColumnPinningState,
  type Table as TanstackTable,
  type VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { type ReactNode, useState } from 'react';

type BaseTable<T extends object, C extends object> = {
  columns: ColumnDef<T, C>[];
  data: T[];
  initialColumnVisibility: VisibilityState;
  pinnedColumns: ColumnPinningState;
  resourceName: string;
  render: (table: TanstackTable<T>) => ReactNode;
};

type BaseTableWithoutPagination = {
  enablePagination: false;
  pageIndex?: never;
  pageSize?: never;
  totalCount?: never;
};

type BaseTableWithPagination = {
  enablePagination: true;
  pageIndex: number;
  pageSize: number;
  totalCount: number;
};

type BaseTableProps<T extends object, C extends object> = (
  | BaseTableWithoutPagination
  | BaseTableWithPagination
) &
  BaseTable<T, C>;

export const BaseTable = <T extends object, C extends object>({
  columns,
  data,
  initialColumnVisibility,
  pageIndex,
  pageSize,
  totalCount,
  pinnedColumns,
  resourceName,
  enablePagination,
  render,
}: BaseTableProps<T, C>) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility,
  );

  const table = useReactTable({
    columns,
    data,
    enableColumnPinning: true,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...(enablePagination && { getPaginationRowModel: getPaginationRowModel() }),
    ...(enablePagination && { pageCount: Math.ceil(totalCount / pageSize) }),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      columnPinning: pinnedColumns,
      columnVisibility,
      ...(enablePagination && { pagination: { pageIndex, pageSize } }),
    },
  });

  return (
    <div className="flex w-full flex-col">
      <div className="flex gap-x-4 self-end">
        <TableColumnsSelect
          optionRenderer={(columnId) => (
            <HeaderRenderer
              namespace={`${resourceName}.table.columns`}
              translationKey={columnId}
            />
          )}
          table={table}
        />
      </div>
      <div className="mb-2 min-w-full border">{render(table)}</div>
    </div>
  );
};
