import {
  CampusAddressSchema,
  CivicAddressSchema,
  CivicCampusAddressSchema,
} from '@/schemas/common/address.schema';
import {
  createOptionalStringOfMaxLengthSchema,
  createSelectOptionSchema,
  createTranslatableFieldMaxLengthSchema,
} from '@rie/domain/schemas';
import * as Either from 'effect/Either';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

const ValidatedCivicCampusAddressSchema = Schema.transformOrFail(
  CivicCampusAddressSchema,
  CivicCampusAddressSchema,
  {
    strict: true,
    decode: (input, _, ast) => {
      if (input.addressType === 'campusAddress') {
        const campusValidation = Schema.decodeUnknownEither(
          CampusAddressSchema,
        )(input.data);

        if (Either.isLeft(campusValidation)) {
          return ParseResult.fail(
            new ParseResult.Type(
              ast,
              input,
              'Please fill in all required campus address fields',
            ),
          );
        }
      } else if (input.addressType === 'civicAddress') {
        const civicValidation = Schema.decodeUnknownEither(CivicAddressSchema)(
          input.data,
        );
        if (Either.isLeft(civicValidation)) {
          return ParseResult.fail(
            new ParseResult.Type(
              ast,
              input,
              'Please fill in all required civic address fields',
            ),
          );
        }
      }

      return ParseResult.succeed(input);
    },
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema for acronyms field (renamed from acronym)
const AcronymsSchema = createTranslatableFieldMaxLengthSchema({
  field: {
    maxLength: 30,
    maxLengthErrorMessage: "Acronym can't contain more than 30 characters",
  },
  required: false,
});

const NameSchema = createTranslatableFieldMaxLengthSchema({
  field: {
    maxLength: 30,
    maxLengthErrorMessage: "Acronym can't contain more than 30 characters",
  },
  required: false,
});

// Schema for otherNames field (renamed from alias)
const OtherNamesSchema = createTranslatableFieldMaxLengthSchema({
  field: {
    maxLength: 1000,
    maxLengthErrorMessage:
      "Other names can't contain more than 1000 characters",
  },
  required: false,
});

// Schema for description field
const DescriptionFieldSchema = createTranslatableFieldMaxLengthSchema({
  field: {
    maxLength: 2000,
    maxLengthErrorMessage:
      "Description can't contain more than 2000 characters",
  },
  required: false,
});

const PhoneSchema = createOptionalStringOfMaxLengthSchema({
  fieldMaxLength: 15,
  maxLengthErrorMessage: () =>
    'description.contactDetails.fields.phone.error.max',
});

const GeneralInfoSectionSchema = Schema.Struct({
  name: NameSchema,
  acronyms: AcronymsSchema,
  otherNames: OtherNamesSchema,
  description: DescriptionFieldSchema,
});

const ContactDetailsSectionSchema = Schema.Struct({
  address: ValidatedCivicCampusAddressSchema,
  phone: PhoneSchema,
  website: Schema.NullOr(Schema.String),
});

const IdentificationSectionSchema = Schema.Struct({
  affiliatedPersons: Schema.Array(Schema.String),
  associatedFinancialProject: Schema.Array(Schema.String),
});

const AssociatedFinancialProjectSchema = Schema.Array(
  createSelectOptionSchema(),
);

const ScientificManagerSchema = Schema.Array(createSelectOptionSchema()).pipe(
  Schema.minItems(1, {
    message: () =>
      'description.inCharge.fields.scientificAndTechnicalManager.error.required',
  }),
);

const TechnicalManagerSchema = Schema.Array(createSelectOptionSchema()).pipe(
  Schema.minItems(1, {
    message: () =>
      'description.inCharge.fields.scientificAndTechnicalManager.error.required',
  }),
);

const InChargeSectionSchema = Schema.Struct({
  associatedFinancialProject: AssociatedFinancialProjectSchema,
  scientificManager: ScientificManagerSchema,
  technicalManager: TechnicalManagerSchema,
});

export const InfrastructureDescriptionSectionSchema = Schema.Struct({
  ...GeneralInfoSectionSchema.fields,
  ...ContactDetailsSectionSchema.fields,
  ...IdentificationSectionSchema.fields,
  ...InChargeSectionSchema.fields,
});
