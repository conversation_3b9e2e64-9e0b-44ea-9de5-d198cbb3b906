import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllFundingProjectsOptions = <
  View extends CollectionViewParamType['view'],
>({
  locale,
  view,
}: CollectionOptionsParams<View>) =>
  getDirectoryListOptions({
    directoryListKey: 'fundingProjects' as const,
    locale,
    view,
  });

export const getFundingProjectByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getDirectoryByIdOptions({
    directoryListKey: 'fundingProjects',
    id,
    view,
  } as const);
