'use client';

import { roleColumns } from '@/app/[locale]/admin/roles/columns';
import { PermissionsTable } from '@/components/permissions-table/permissions-table';
import { initialColumnVisibility } from '@/constants/roles';
import { useGetAllRoles } from '@/hooks/admin/permissions/roles.hook';
import type { SupportedLocale } from '@/types/locale';

type ListRolesProps = {
  locale: SupportedLocale;
};

export const ListRoles = ({ locale }: ListRolesProps) => {
  const { data: roles, isLoading } = useGetAllRoles<'list'>({
    view: 'list',
  });

  return (
    <PermissionsTable
      columns={roleColumns(locale)}
      data={roles ?? []}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      resourceName="roles"
    />
  );
};
