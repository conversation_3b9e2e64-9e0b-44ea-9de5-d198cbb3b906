import { handleEffectError } from '@/api/v2/utils/error-handler';
import { PeopleRuntime } from '@/infrastructure/runtimes/people.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  PersonInputSchema,
  PersonSchema,
  ResourceIdSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { PeopleServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

export const getPersonByIdRoute = describeRoute({
  description: 'Obtenir une personne par ID',
  operationId: 'getPersonById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la personne (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PersonSchema),
        },
      },
      description: 'Personne trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Personne non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['People'],
});

export const createPersonRoute = describeRoute({
  description: 'Créer une personne',
  operationId: 'createPerson',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PersonInputSchema),
        example: {
          guidId: 'person_guid_123',
          uid: 'john.doe',
          firstName: 'John',
          lastName: 'Doe',
          userId: 'user_123',
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PersonSchema),
        },
      },
      description: 'Personne créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - L'utilisateur n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['People'],
});

export const updatePersonRoute = describeRoute({
  description: 'Mettre à jour une personne',
  operationId: 'updatePerson',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la personne (format CUID)',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PersonInputSchema),
        example: {
          guidId: 'updated_person_guid_123',
          uid: 'jane.doe',
          firstName: 'Jane',
          lastName: 'Doe',
          userId: 'user_456',
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PersonSchema),
        },
      },
      description: 'Personne mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Personne non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['People'],
});

export const deletePersonRoute = describeRoute({
  description: 'Supprimer une personne',
  operationId: 'deletePerson',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la personne (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Personne supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Personne non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['People'],
});

export const getAllPeopleRoute = describeRoute({
  description: 'Lister toutes les personnes',
  operationId: 'getAllPeople',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(PersonSchema)),
        },
      },
      description: 'Personnes retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['People'],
});

const peopleRoute = new Hono<{
  Variables: HonoVariables;
}>();

peopleRoute.get('/', getAllPeopleRoute, async (ctx) => {
  const user = ctx.get('user');
  const session = ctx.get('session');

  if (!user || !session) {
    return ctx.json({ error: 'Unauthorized' }, 401);
  }

  const program = Effect.gen(function* () {
    const peopleService = yield* PeopleServiceLive;
    return yield* peopleService.getAllPeople();
  }).pipe(
    withPolicy(checkPermission('people:read')),
    Effect.provideService(CurrentUser, {
      sessionId: session.id,
      userId: UserIdSchema.make(user.id),
    }),
  );
  const result = await PeopleRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

peopleRoute.get(
  '/:id',
  getPersonByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');
    const id = ctx.req.param('id');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const peopleService = yield* PeopleServiceLive;
      return yield* peopleService.getPersonById(id);
    }).pipe(
      withPolicy(checkPermission('people:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await PeopleRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

peopleRoute.post(
  '/',
  createPersonRoute,
  validator('json', PersonInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const peopleService = yield* PeopleServiceLive;
      return yield* peopleService.createPerson({
        ...body,
        modifiedBy: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('people:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await PeopleRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

peopleRoute.put(
  '/:id',
  updatePersonRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', PersonInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const peopleService = yield* PeopleServiceLive;
      return yield* peopleService.updatePerson({
        id,
        person: {
          ...body,
          modifiedBy: user.id,
        },
      });
    }).pipe(
      withPolicy(checkPermission('people:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await PeopleRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

peopleRoute.delete(
  '/:id',
  deletePersonRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const peopleService = yield* PeopleServiceLive;
      return yield* peopleService.deletePerson(id);
    }).pipe(
      withPolicy(checkPermission('people:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await PeopleRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Person deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { peopleRoute };
