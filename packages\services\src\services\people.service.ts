import { PersonNotFoundError } from '@rie/domain/errors';
import type { PersonInputSchema } from '@rie/domain/schemas';
import { PeopleRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type PersonInput = Schema.Schema.Type<typeof PersonInputSchema>;

export class PeopleServiceLive extends Effect.Service<PeopleServiceLive>()(
  'PeopleServiceLive',
  {
    dependencies: [PeopleRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllPeople = () =>
        Effect.gen(function* () {
          const repo = yield* PeopleRepositoryLive;
          return yield* repo.findAllPeople();
        });

      const getPersonById = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* PeopleRepositoryLive;
          const person = yield* repo.findPersonById(id);
          if (!person) {
            return yield* Effect.fail(new PersonNotFoundError({ id }));
          }
          return person;
        });

      const createPerson = (person: PersonInput) =>
        Effect.gen(function* () {
          const repo = yield* PeopleRepositoryLive;
          return yield* repo.createPerson({ person });
        });

      const updatePerson = ({
        id,
        person,
      }: { person: PersonInput; id: string }) =>
        Effect.gen(function* () {
          const repo = yield* PeopleRepositoryLive;
          const existingPerson = yield* repo.findPersonById(id);
          if (!existingPerson) {
            return yield* Effect.fail(new PersonNotFoundError({ id }));
          }
          return yield* repo.updatePerson({
            personId: id,
            person,
          });
        });

      const deletePerson = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* PeopleRepositoryLive;
          const existingPerson = yield* repo.findPersonById(id);
          if (!existingPerson) {
            return yield* Effect.fail(new PersonNotFoundError({ id }));
          }
          const result = yield* repo.deletePerson(id);
          return result.length > 0;
        });

      return {
        getAllPeople,
        getPersonById,
        createPerson,
        updatePerson,
        deletePerson,
      } as const;
    }),
  },
) {}
