'use client';

import { establishmentsColumns } from '@/app/[locale]/bottin/etablissements/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/establishments';
import type { SupportedLocale } from '@/types/locale';
import type { InstitutionList } from '@rie/domain/types';

type EstablishmentsListProps = {
  locale: SupportedLocale;
};

export const EstablishmentsList = ({ locale }: EstablishmentsListProps) => {
  return (
    <DirectoryList<
      'establishment',
      'list',
      InstitutionList,
      Record<string, unknown>
    >
      directoryListKey="establishment"
      view="list"
      locale={locale}
      columns={establishmentsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="establishments"
    />
  );
};
