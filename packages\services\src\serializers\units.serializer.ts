import {
  type SelectOptionDTO,
  SelectOptionDtoSchema,
  type UnitDetailResponseDTO,
  UnitDetailResponseDtoSchema,
  type UnitEditResponseDTO,
  UnitEditResponseDtoSchema,
  type UnitListItemDTO,
  UnitListItemDtoSchema,
} from '@rie/api-contracts';
import type { Unit } from '@rie/domain/aggregates';
import { DbUnitDataSchema } from '@rie/domain/schemas';
import type { UnitData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes a Unit aggregate into the shape required for a list view.
 *
 * Uses the Unit aggregate's composite translation logic to provide intelligent
 * locale fallback behavior, ensuring the best available translation is selected
 * for each field based on the requested locale and fallback preferences.
 */
export function serializeUnitToListItem(
  unit: Unit,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<UnitListItemDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbUnitDataSchema,
    UnitListItemDtoSchema,
    {
      strict: true,
      decode: (unitData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Unit aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = unit.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Try to compute parent name from parent (institution or unit) translations (prefer requested locale)
            type ParentTranslation = { locale: string; name: string | null };
            const parentData = (unitData as unknown as {
              parent?: {
                type?: 'institution' | 'unit';
                institution?: { translations?: ParentTranslation[] };
                unit?: { translations?: ParentTranslation[] };
              };
            }).parent;

            let parentName: string | null = null;
            if (parentData) {
              // Get translations from either institution or unit based on parent type
              const parentTranslations = parentData.type === 'institution'
                ? parentData.institution?.translations
                : parentData.unit?.translations;

              if (Array.isArray(parentTranslations)) {
                const preferred = parentTranslations.find(
                  (t) => t.locale === locale && t.name && t.name.trim() !== '',
                );
                const fallback = parentTranslations.find(
                  (t) => t.locale === fallbackLocale && t.name && t.name.trim() !== '',
                );
                const anyName = parentTranslations.find(
                  (t) => t.name && t.name.trim() !== '',
                );
                parentName = preferred?.name ?? fallback?.name ?? anyName?.name ?? null;
              }
            }

            return {
              id: unitData.id,
              /** We are forced to cast `compositeTranslations.name.value` as string
               * because in the database it is defined as string | null.
               * However, we have defined a rule that states that at least one translation must have a name.
               * Therefore, we can safely cast to string.
               **/
              name: {
                locale: compositeTranslations.name.locale,
                value: compositeTranslations.name.value as string,
              },
              acronym: compositeTranslations.acronyms.value,
              parent: parentName,
              createdAt: unitData.createdAt,
              updatedAt: unitData.updatedAt,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              unitData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize unit to list item',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    unit.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
  );
}

/**
 * Serializes a Unit aggregate into the shape required for a detailed view.
 *
 * Uses the Unit aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for all translatable fields. The resulting DTO
 * contains the best available translation for each field based on the requested
 * locale and fallback preferences, formatted for detailed view consumption.
 */
export function serializeUnitToDetail(
  unit: Unit,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<UnitDetailResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbUnitDataSchema, // Source
    UnitDetailResponseDtoSchema, // Target
    {
      strict: true,
      decode: (unitData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Unit aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = unit.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            return {
              id: unitData.id,
              isActive: unitData.isActive,
              typeId: unitData.typeId,
              parentId: unitData.parentId,
              translations: {
                /** We are forced to cast `compositeTranslations.name.value` as string
                 * because in the database it is defined as string | null.
                 * However, we have defined a rule that states that at least one translation must have a name.
                 * Therefore, we can safely cast to string.
                 **/
                name: {
                  locale: compositeTranslations.name.locale,
                  value: compositeTranslations.name.value as string,
                },
                description: {
                  locale: compositeTranslations.description.locale,
                  value: compositeTranslations.description.value,
                },
                otherNames: {
                  locale: compositeTranslations.otherNames.locale,
                  value: compositeTranslations.otherNames.value,
                },
                acronyms: {
                  locale: compositeTranslations.acronyms.locale,
                  value: compositeTranslations.acronyms.value,
                },
              },
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              unitData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize unit to detail view',
            );
          },
        });
      },

      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    unit.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
  );
}

/**
 * Serializes a Unit aggregate into a simple value/label pair for select inputs.
 *
 * Uses the Unit aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for the label field. This ensures that select options
 * display the best available unit name based on the requested locale and fallback
 * preferences, providing a consistent user experience across different languages.
 */
export function serializeUnitToSelectOption(
  unit: Unit,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbUnitDataSchema,
    SelectOptionDtoSchema,
    {
      strict: true,
      decode: (unitData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Unit aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = unit.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            return {
              value: unitData.id,
              label: compositeTranslations.name.value as string,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              unitData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize unit to select option',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    unit.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
  );
}

/**
 * Serializes database unit data into the shape required for an edit form.
 *
 * This function transforms raw database unit data (including all translations)
 * into the format expected by the frontend edit form. Unlike other serializers
 * that work with Unit aggregates and provide locale-based fallback behavior,
 * this function works directly with database data and preserves all available
 * translations for editing purposes.
 *
 * The resulting DTO contains all unit fields plus an array of all available
 * translations, allowing the edit form to display and modify translations
 * in multiple locales simultaneously.
 */
export function serializeUnitToFormEdit(
  unitData: UnitData,
): Effect.Effect<UnitEditResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbUnitDataSchema,
    UnitEditResponseDtoSchema,
    {
      strict: true,
      decode: (data, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Transform database translations to the format expected by the edit form
            const transformedTranslations = data.translations.map(
              (translation) => ({
                name: {
                  locale: translation.locale,
                  value: translation.name,
                },
                description: {
                  locale: translation.locale,
                  value: translation.description,
                },
                otherNames: {
                  locale: translation.locale,
                  value: translation.otherNames,
                },
                acronyms: {
                  locale: translation.locale,
                  value: translation.acronyms,
                },
              }),
            );

            return {
              id: data.id,
              isActive: data.isActive,
              typeId: data.typeId,
              parentId: data.parentId,
              translations: transformedTranslations,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              data,
              error instanceof Error
                ? error.message
                : 'Failed to serialize unit to edit form',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return Schema.decode(transformer)(unitData);
}
