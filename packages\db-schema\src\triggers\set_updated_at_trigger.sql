-- This is a generic, reusable function that sets the `updated_at` column
-- to the current timestamp. It can be used by triggers on any table.
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- --- Triggers --- --
-- Apply the trigger to the 'units' table.
-- You should create a similar trigger for any other table that has an `updated_at` column.

DROP TRIGGER IF EXISTS set_units_updated_at ON units;
CREATE TRIGGER set_units_updated_at
BEFORE UPDATE ON units
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Example for another table (e.g., unit_parents)
-- DROP TRIGGER IF EXISTS set_unit_parents_updated_at ON unit_parents;
-- CREATE TRIGGER set_unit_parents_updated_at
-- BEFORE UPDATE ON unit_parents
-- FOR EACH ROW
-- EXECUTE FUNCTION trigger_set_timestamp();
