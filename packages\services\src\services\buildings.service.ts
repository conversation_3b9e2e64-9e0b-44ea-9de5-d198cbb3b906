import type { BuildingFormRequestDTO } from '@rie/api-contracts';
import { BuildingNotFoundError } from '@rie/domain/errors';
import type { DBBuildingInput, ResourceViewType } from '@rie/domain/types';
import { BuildingsRepositoryLive, CampusesRepositoryLive, InstitutionsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';
import { BuildingFormToDomainInput } from '../mappers/building-form.mapper';
import { serializeBuildingToEditClient } from '../serializers/buildings.serializer';

export class BuildingsServiceLive extends Effect.Service<BuildingsServiceLive>()(
  'BuildingsServiceLive',
  {
    dependencies: [BuildingsRepositoryLive.Default, CampusesRepositoryLive.Default, InstitutionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllBuildings = () => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const all = yield* repo.findAllBuildings();
          const t1 = performance.now();
          console.log(`Call to getAllBuildings took ${t1 - t0} ms.`);
          return all;
        });
      };

      const getAllBuildingsSelect = (params: {
        locale: string;
        fallbackLocale: string;
      }) =>
        Effect.gen(function* () {
          const tStart = performance.now();
          const repo = yield* BuildingsRepositoryLive;
          const buildings = yield* repo.findAllBuildings();
          const afterFetch = performance.now();

          console.log(
            `[Service][Buildings] fetched ${buildings.length} buildings in ${afterFetch - tStart} ms (select view, locale=${params.locale}, fallback=${params.fallbackLocale})`,
          );

          // Transform to select options format
          const result = buildings.map((building) => {
            const translations = building.translations ?? [];
            const preferred = translations.find(
              (t) => t.locale === params.locale && !!t.name && t.name.trim() !== '',
            );
            const fallback = translations.find(
              (t) => t.locale === params.fallbackLocale && !!t.name && t.name.trim() !== '',
            );
            const anyName = translations.find(
              (t) => !!t.name && t.name.trim() !== '',
            );

            const nameCandidate = preferred?.name ?? fallback?.name ?? anyName?.name;
            const safeLabel =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (building.sadId ?? building.id);

            return {
              value: building.id,
              label: safeLabel,
            };
          });

          console.log(
            `[Service][Buildings] serialized select ${result.length} options in ${performance.now() - afterFetch} ms`,
          );
          return result;
        });

      const getBuildingById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const building = yield* repo.findBuildingById(params.id);
          if (!building) {
            return yield* Effect.fail(new BuildingNotFoundError({ id: params.id }));
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              const t1 = performance.now();
              console.log(`Call to getBuildingById detail took ${t1 - t0} ms.`);
              return building; // For now return raw data, can serialize later if needed
            }
            case 'edit': {
              // Resolve campus and jurisdiction labels
              let campusLabel: string | null = null;
              let jurisdictionLabel: string | null = null;
              let jurisdictionId: string | null = null;

              if (building.campusId) {
                const campusesRepo = yield* CampusesRepositoryLive;
                const campus = yield* campusesRepo.findCampusById(building.campusId);
                if (campus) {
                  const campusData = yield* campus.toRaw();
                  // Find preferred translation for campus name
                  const translations = campusData.translations ?? [];
                  const preferred = translations.find(
                    (t) => t.locale === params.locale && !!t.name && t.name.trim() !== '',
                  );
                  const fallback = translations.find(
                    (t) => t.locale === params.fallbackLocale && !!t.name && t.name.trim() !== '',
                  );
                  const anyName = translations.find((t) => !!t.name && t.name.trim() !== '');
                  campusLabel = preferred?.name ?? fallback?.name ?? anyName?.name ?? null;

                  // Get jurisdiction from campus's institution
                  if (campusData.institutionId) {
                    jurisdictionId = campusData.institutionId;
                    const institutionsRepo = yield* InstitutionsRepositoryLive;
                    const institution = yield* institutionsRepo.findInstitutionById(
                      campusData.institutionId,
                    );
                    if (institution && (institution as unknown as { translations?: Array<{ locale: string; name: string | null }> }).translations) {
                      const instTranslations = (institution as unknown as {
                        translations: Array<{
                          locale: string;
                          name: string | null;
                        }>
                      }).translations;
                      const preferredInst = instTranslations.find(
                        (t) => t.locale === params.locale && !!t.name && t.name.trim() !== '',
                      );
                      const fallbackInst = instTranslations.find(
                        (t) => t.locale === params.fallbackLocale && !!t.name && t.name.trim() !== '',
                      );
                      const anyNameInst = instTranslations.find(
                        (t) => !!t.name && t.name.trim() !== '',
                      );
                      jurisdictionLabel = preferredInst?.name ?? fallbackInst?.name ?? anyNameInst?.name ?? null;
                    }
                  }
                }
              }

              const result = yield* serializeBuildingToEditClient(
                {
                  ...building,
                  isActive: true, // Default value since it's not in the current type
                },
                campusLabel,
                jurisdictionLabel,
                jurisdictionId
              );
              const t1 = performance.now();
              console.log(`Call to getBuildingById edit took ${t1 - t0} ms.`);
              return result;
            }
            default: {
              // TypeScript exhaustiveness check
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });
      };

      const createBuilding = (params: {
        buildingDto: BuildingFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Map the incoming DTO to a clean domain input shape
          const domainInput = yield* Schema.decode(BuildingFormToDomainInput)(
            params.buildingDto,
          );

          // 2. Create building data directly for database insertion
          const repo = yield* BuildingsRepositoryLive;
          const buildingData: DBBuildingInput = {
            campusId: domainInput.campus?.value || null,
            civicAddressId: null, // TODO: Handle civic addresses when needed
            jurisdictionId: domainInput.jurisdiction?.value || null,
            sadId: null,
            diId: null,
            translations: domainInput.name.map((nameItem) => {
              const aliasItem = domainInput.alias.find((a) => a.locale === nameItem.locale);
              return {
                locale: nameItem.locale as "fr" | "en",
                name: nameItem.value,
                description: null,
                otherNames: aliasItem?.value || null, // Form 'alias' maps to database 'otherNames'
                acronyms: null, // Required by domain schema but not used for buildings
              };
            }),
          };

          const buildingWithMetadata = {
            ...buildingData,
            modifiedBy: params.userId,
          };

          return yield* repo.createBuilding({ building: buildingWithMetadata });
        });

      const updateBuilding = ({
        id,
        buildingDto,
        userId,
      }: {
        id: string;
        buildingDto: BuildingFormRequestDTO;
        userId: string;
      }) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }

          // 1. Map the incoming DTO to a clean domain input shape
          const domainInput = yield* Schema.decode(BuildingFormToDomainInput)(
            buildingDto,
          );

          // 2. Create building data directly for database update
          const buildingData: DBBuildingInput = {
            campusId: domainInput.campus?.value || null,
            civicAddressId: null, // TODO: Handle civic addresses when needed
            jurisdictionId: domainInput.jurisdiction?.value || null,
            sadId: null,
            diId: null,
            translations: domainInput.name.map((nameItem) => {
              const aliasItem = domainInput.alias.find((a) => a.locale === nameItem.locale);
              return {
                locale: nameItem.locale as "fr" | "en",
                name: nameItem.value,
                description: null,
                otherNames: aliasItem?.value || null, // Form 'alias' maps to database 'otherNames'
                acronyms: null, // Required by domain schema but not used for buildings
              };
            }),
          };

          const buildingWithMetadata = {
            ...buildingData,
            modifiedBy: userId,
          };

          const updatedBuilding = yield* repo.updateBuilding({
            buildingId: id,
            building: buildingWithMetadata,
          });
          const t1 = performance.now();
          console.log(`Call to updateBuilding took ${t1 - t0} ms.`);
          return updatedBuilding;
        });
      };

      const deleteBuilding = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const result = yield* repo.deleteBuilding(id);
          const t1 = performance.now();
          console.log(`Call to deleteBuilding took ${t1 - t0} ms.`);
          return result.length > 0;
        });
      };

      return {
        getAllBuildings,
        getAllBuildingsSelect,
        getBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) { }