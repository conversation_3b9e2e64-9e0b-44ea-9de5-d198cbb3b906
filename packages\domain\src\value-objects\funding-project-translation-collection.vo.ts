import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type * as ParseResult from 'effect/ParseResult';
import { DuplicateLocaleError } from '../errors';
import type { FundingProjectTranslation } from './funding-project-translation.vo';

/**
 * A value object representing a collection of FundingProjectTranslations.
 */
export class FundingProjectTranslationCollection {
    private constructor(
        public readonly translations: readonly FundingProjectTranslation[],
    ) { }

    static create(
        translations: readonly FundingProjectTranslation[],
    ): Effect.Effect<FundingProjectTranslationCollection, DuplicateLocaleError> {
        return pipe(
            Effect.succeed(translations),
            Effect.flatMap(ensureNoDuplicateLocales),
            Effect.map(
                (validTranslations) =>
                    new FundingProjectTranslationCollection(validTranslations),
            ),
        );
    }

    /**
 * Converts the collection to an array format suitable for persistence.
 * Returns translations in the format expected by the database layer.
 */
    toPersistenceArray(): Effect.Effect<
        Array<{ locale: string; name: string; description: string | null }>,
        ParseResult.ParseError
    > {
        // Convert domain objects to raw data format
        const rawTranslations = this.translations.map((t) => t.toRaw());

        // Return the raw translations directly as they already match our target type
        return Effect.succeed(rawTranslations);
    }

    /**
     * Finds a single translation for a specific locale, without any fallbacks.
     */
    findByLocale(locale: string): FundingProjectTranslation | undefined {
        return this.translations.find((t) => t.locale === locale);
    }

    /**
     * Builds a composite object of translation fields with locale-based fallbacks.
     */
    getCompositeTranslations(locale: string, fallbackLocale: string) {
        const isValidValue = (
            value: string | null | undefined,
        ): value is string => {
            return value !== null && value !== undefined && value.trim() !== '';
        };

        const findField = (getter: (t: FundingProjectTranslation) => string | null | undefined) => {
            // Tier 1: Try primary locale first
            const primaryTranslation = this.findByLocale(locale);
            if (primaryTranslation) {
                const primaryValue = getter(primaryTranslation);
                if (isValidValue(primaryValue)) {
                    return { locale, value: primaryValue };
                }
            }

            // Tier 2: Try fallback locale if different from primary
            if (fallbackLocale !== locale) {
                const fallbackTranslation = this.findByLocale(fallbackLocale);
                if (fallbackTranslation) {
                    const fallbackValue = getter(fallbackTranslation);
                    if (isValidValue(fallbackValue)) {
                        return { locale: fallbackLocale, value: fallbackValue };
                    }
                }
            }

            // Tier 3: Search through all other available translations
            for (const translation of this.translations) {
                const translationLocale = translation.locale;
                // Skip locales we've already checked
                if (
                    translationLocale !== locale &&
                    translationLocale !== fallbackLocale
                ) {
                    const value = getter(translation);
                    if (isValidValue(value)) {
                        return { locale: translationLocale, value };
                    }
                }
            }

            // Tier 4: Graceful degradation - return requested locale with null value
            return { locale, value: null };
        };

        return {
            name: findField((t) => t.name),
            description: findField((t) => t.description),
        };
    }

    /**
     * Returns all FundingProjectTranslation objects in the collection.
     */
    getAll(): readonly FundingProjectTranslation[] {
        return this.translations;
    }

    /**
     * Checks if the collection is empty.
     */
    isEmpty(): boolean {
        return this.translations.length === 0;
    }

    get length(): number {
        return this.translations.length;
    }
}

// --- Private Invariant Enforcement Functions ---

const ensureNoDuplicateLocales = (
    translations: readonly FundingProjectTranslation[],
): Effect.Effect<readonly FundingProjectTranslation[], DuplicateLocaleError> => {
    const locales = translations.map((t) => t.locale);
    const uniqueLocales = new Set(locales);

    if (locales.length !== uniqueLocales.size) {
        const duplicates = locales.filter(
            (locale, index) => locales.indexOf(locale) !== index,
        );
        const uniqueDuplicates = [...new Set(duplicates)];

        return Effect.fail(DuplicateLocaleError.create(uniqueDuplicates));
    }

    return Effect.succeed(translations);
};