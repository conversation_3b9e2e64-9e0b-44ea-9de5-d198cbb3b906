/**
 * Translation-specific types for domain entities.
 * Provides consistent typing for all translation entities (units, equipments, infrastructures, etc.)
 */

import type {
  DbEquipmentI18N,
  DbInfrastructureI18N,
  DbUnitI18N,
} from '@rie/db-schema/entity-types';
import type { ValidationErrors } from './validation.types';

// Generic utility for translation field extraction
export type TranslationFields<TEntity> = Omit<TEntity, 'id'>;

// Generic validation errors for translation entities
export type TranslationValidationErrors<TEntity> = ValidationErrors<
  TranslationFields<TEntity>
>;

// Specific type aliases for common translation entities
export type EquipmentTranslationFields = TranslationFields<DbEquipmentI18N>;
export type InfrastructureTranslationFields =
  TranslationFields<DbInfrastructureI18N>;

export type UnitTranslationValidationErrors =
  TranslationValidationErrors<DbUnitI18N>;
export type EquipmentTranslationValidationErrors =
  TranslationValidationErrors<DbEquipmentI18N>;
export type InfrastructureTranslationValidationErrors =
  TranslationValidationErrors<DbInfrastructureI18N>;
