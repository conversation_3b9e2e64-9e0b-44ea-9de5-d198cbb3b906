import { RoomCategoryI18nMigrationConverter } from '../converters/08-02-convert-room-category-i18n-inserts';

async function convertRoomCategoryI18nData() {
  // Create converter and run conversion
  const converter = new RoomCategoryI18nMigrationConverter();
  await converter.convertFile();
}

// Run the conversion
convertRoomCategoryI18nData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
