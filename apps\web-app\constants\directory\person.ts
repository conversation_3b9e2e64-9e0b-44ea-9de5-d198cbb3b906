import { CAMPUS_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';

export const initialColumnVisibility = {
  email: true,
  firstName: true,
  lastName: true,
  lastUpdatedAt: true,
} as const;

export const personFormSections = {
  affiliations: {
    key: 'affiliations',
    order: 3,
  },
  contactDetails: {
    key: 'description.contactDetails',
    order: 2,
  },
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const personFormDefaultValues = (): PersonFormSchema => ({
  affiliatedField: {
    affiliationSection: [],
  },
  addresses: [CAMPUS_ADDRESS_DEFAULT_VALUE],
  emails: [
    {
      email: '',
    },
  ],
  firstName: '',
  lastName: '',
  phones: [],
});
