import * as Schema from 'effect/Schema';
import * as R from 'remeda';
import {
  createOptionalStringOfMaxLengthSchema,
  createRequiredStringOfMaxLengthSchema,
} from './base.schema';

type FieldConfig = {
  required: boolean;
  maxLength: number;
  customErrorMessage?: string;
};

type DbBaseTranslationEntity = {
  id: string;
  dataId: string;
  locale: string;
};

// Extract configurable fields (exclude base fields)
type DbConfigurableFields<TEntity extends DbBaseTranslationEntity> = Omit<
  TEntity,
  keyof DbBaseTranslationEntity
>;

type DbTranslationFieldConfig<TEntity extends DbBaseTranslationEntity> = {
  [K in keyof DbConfigurableFields<TEntity>]: FieldConfig;
};

const createDbFieldSchema = (fieldConfig: FieldConfig, fieldName: string) => {
  return fieldConfig.required
    ? createRequiredStringOfMaxLengthSchema({
        fieldMaxLength: fieldConfig.maxLength,
        errorMessages: {
          required: () => `${fieldName} is required`,
          maxLength: () =>
            fieldConfig.customErrorMessage ||
            `${fieldName} must be ${fieldConfig.maxLength} characters or less`,
        },
      })
    : createOptionalStringOfMaxLengthSchema({
        fieldMaxLength: fieldConfig.maxLength,
        maxLengthErrorMessage: () =>
          fieldConfig.customErrorMessage ||
          `${fieldName} must be ${fieldConfig.maxLength} characters or less`,
      });
};

export const createDbTranslationSchema = <
  TEntity extends DbBaseTranslationEntity,
>({
  fields,
}: {
  readonly fields: DbTranslationFieldConfig<TEntity>;
}) => {
  const schemaFields = R.pipe(
    fields,
    R.mapValues((fieldConfig, fieldName) =>
      createDbFieldSchema(fieldConfig, fieldName),
    ),
  );

  const allSchemaFields = {
    locale: Schema.String,
    ...schemaFields,
  };

  return Schema.Struct(allSchemaFields);
};
