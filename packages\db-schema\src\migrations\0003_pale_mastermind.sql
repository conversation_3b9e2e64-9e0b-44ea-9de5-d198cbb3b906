ALTER TABLE "units" ALTER COLUMN "is_active" SET NOT NULL;--> statement-breakpoint
CREATE INDEX "idx_equipment_categories_i18n_data_id" ON "equipment_categories_i18n" USING btree ("data_id");--> statement-breakpoint
CREATE INDEX "idx_equipment_statuses_i18n_data_id" ON "equipment_statuses_i18n" USING btree ("data_id");--> statement-breakpoint
CREATE INDEX "idx_equipment_types_i18n_data_id" ON "equipment_types_i18n" USING btree ("data_id");