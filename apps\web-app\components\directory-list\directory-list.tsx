'use client';

import { BottinTable } from '@/components/bottin-table/bottin-table';
import { useGetDirectoryList } from '@/hooks/directory/directory-list.hook';
import type { DirectoryListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import type { CollectionViewParamType } from '@rie/domain/types';
import type { ColumnDef, VisibilityState } from '@tanstack/react-table';

type DirectoryListProps<
  Key extends DirectoryListKey,
  View extends CollectionViewParamType['view'],
  T extends object,
  C extends object,
> = {
  directoryListKey: Key;
  view: View;
  locale: SupportedLocale;
  columns: ColumnDef<T, C>[];
  initialColumnVisibility: VisibilityState;
  resourceName: string;
};

export const DirectoryList = <
  Key extends DirectoryListKey,
  View extends CollectionViewParamType['view'],
  T extends object,
  C extends object,
>({
  directoryListKey,
  view,
  locale,
  columns,
  initialColumnVisibility,
  resourceName,
}: DirectoryListProps<Key, View, T, C>) => {
  const { data, isLoading } = useGetDirectoryList({
    directoryListKey,
    locale,
    view,
  });

  return (
    <BottinTable
      columns={columns}
      data={(data ?? []) as T[]}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      resourceName={resourceName}
    />
  );
};
