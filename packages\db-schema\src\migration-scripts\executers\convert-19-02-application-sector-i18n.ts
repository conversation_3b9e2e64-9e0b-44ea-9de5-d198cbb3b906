import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { ApplicationSectorI18nMigrationConverter } from '../converters/19-02-convert-application-sector-i18n-inserts';

async function convertApplicationSectorI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new ApplicationSectorI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertApplicationSectorI18nData().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
