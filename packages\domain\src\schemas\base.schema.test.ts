import * as Either from 'effect/Either';
import * as Schema from 'effect/Schema';
import { describe, expect, it } from 'vitest';
import { createSelectOptionSchema } from './base.schema';

describe('createNormalizedSelectOptionSchema', () => {
  describe('without validation message', () => {
    const schema = createSelectOptionSchema();

    it('should accept valid string values', () => {
      const input = { value: 'test-value', label: 'Test Label' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          value: 'test-value',
          label: 'Test Label',
        });
      }
    });

    it('should normalize empty strings to null', () => {
      const input = { value: '', label: '   ' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({ value: null, label: null });
      }
    });

    it('should accept null values', () => {
      const input = { value: null, label: null };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({ value: null, label: null });
      }
    });

    it('should accept undefined values and convert to null', () => {
      const input = { value: undefined, label: undefined };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({ value: null, label: null });
      }
    });

    it('should accept mixed null and string values', () => {
      const input = { value: 'test', label: null };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({ value: 'test', label: null });
      }
    });
  });

  describe('with validation message', () => {
    const validationMessage = 'Please select a valid option';
    const schema = createSelectOptionSchema(validationMessage);

    it('should accept valid string values', () => {
      const input = { value: 'test-value', label: 'Test Label' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isRight(result)).toBe(true);
      if (Either.isRight(result)) {
        expect(result.right).toEqual({
          value: 'test-value',
          label: 'Test Label',
        });
      }
    });

    it('should reject when value is null', () => {
      const input = { value: null, label: 'Test Label' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });

    it('should reject when label is null', () => {
      const input = { value: 'test-value', label: null };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });

    it('should reject when both value and label are null', () => {
      const input = { value: null, label: null };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });

    it('should reject empty strings (normalized to null)', () => {
      const input = { value: '', label: 'Test Label' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });

    it('should reject whitespace-only strings (normalized to null)', () => {
      const input = { value: 'test-value', label: '   ' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });

    it('should reject undefined values (normalized to null)', () => {
      const input = { value: undefined, label: 'Test Label' };
      const result = Schema.decodeEither(schema)(input);

      expect(Either.isLeft(result)).toBe(true);
      if (Either.isLeft(result)) {
        expect(result.left.message).toContain(validationMessage);
      }
    });
  });
});
