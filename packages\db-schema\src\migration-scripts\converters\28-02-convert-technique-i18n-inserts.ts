import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { type I18NColumnMapper, i18nColumnMapper } from '../constants';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';
import { BaseConverter } from './base-converter';

export class TechniqueI18nMigrationConverter extends BaseConverter {
  private techniqueI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    techniqueIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the technique
    const newTechniqueId = techniqueIdMappings[mysqlRecord.data_id.toString()];
    if (!newTechniqueId) {
      throw new Error(
        `No mapping found for technique_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.techniqueI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newTechniqueId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for technique_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'technique_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No technique_trad INSERT statements found.');
        return;
      }

      // Load technique ID mappings
      const techniqueIdMappings = await this.loadEntityIdMappings('technique');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(
          statement,
          sqlContent,
          'technique_trad',
        );
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, techniqueIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const createStatement = this.extractCreateStatement(
        sqlContent,
        'technique_trad',
      );
      const columns = this.extractColumnNames(createStatement).map(
        (column) => i18nColumnMapper[column as I18NColumnMapper] ?? column,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'techniques_i18n',
        'Technique I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(OUTPUT_PATH);
      await this.safeMkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.techniqueI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'technique_trad',
          postgres: 'techniques_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} techniques_i18n records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
