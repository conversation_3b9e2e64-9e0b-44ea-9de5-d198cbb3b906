import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    SelectOptionDtoSchema,
} from '../common';

/**
 * Describes the entire request body for creating or updating a funding project.
 * This is the shape of the data the client will send to the API.
 **/

export const FundingProjectFormRequestDtoSchema = Schema.Struct({
    id: Schema.optional(Schema.String),
    isActive: Schema.optional(Schema.Boolean),
    holderId: Schema.optional(Schema.NullOr(Schema.String)),
    typeId: Schema.optional(Schema.NullOr(Schema.String)),
    fciId: Schema.optional(Schema.NullOr(Schema.String)),
    synchroId: Schema.optional(Schema.NullOr(Schema.String)),
    obtainingYear: Schema.optional(Schema.NullOr(Schema.Number)),
    endDate: Schema.optional(Schema.NullOr(Schema.String)),
    holder: SelectOptionDtoSchema,
    fundingType: SelectOptionDtoSchema,
    name: Schema.Array(OptionalLocalizedFieldDtoSchema),
    description: Schema.Array(OptionalLocalizedFieldDtoSchema),
    purchasedEquipment: Schema.Array(SelectOptionDtoSchema),
    associateResearchers: Schema.Array(SelectOptionDtoSchema),
    financedInfrastructures: Schema.Array(SelectOptionDtoSchema),
});

export type FundingProjectFormRequestDTO = Schema.Schema.Type<
    typeof FundingProjectFormRequestDtoSchema
>;