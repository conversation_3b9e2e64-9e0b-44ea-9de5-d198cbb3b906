import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  InfrastructuresRepositoryLive,
  PeopleRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  PeopleServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PeopleServicesLayer = Layer.mergeAll(
  PeopleRepositoryLive.Default,
  PeopleServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const PeopleRuntime = ManagedRuntime.make(PeopleServicesLayer);
