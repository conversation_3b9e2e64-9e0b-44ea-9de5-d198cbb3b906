import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  BuildingsRepositoryLive,
  CampusesRepositoryLive,
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  InstitutionsRepositoryLive,
  RoomsRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  RoomsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const RoomsServicesLayer = Layer.mergeAll(
  RoomsRepositoryLive.Default,
  BuildingsRepositoryLive.Default,
  CampusesRepositoryLive.Default,
  InstitutionsRepositoryLive.Default,
  RoomsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const RoomsRuntime = ManagedRuntime.make(RoomsServicesLayer);
