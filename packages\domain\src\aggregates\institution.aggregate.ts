import type { DbInstitution } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import * as R from 'remeda';
import {
    type DuplicateLocaleError,
    InstitutionInvariantViolationError,
    TranslationValidationError,
} from '../errors';
import { DbInstitutionDataSchema } from '../schemas';
import type { InstitutionData } from '../types';
import {
    InstitutionTranslation,
    InstitutionTranslationCollection,
} from '../value-objects';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface InstitutionAggregateState
    extends Omit<DbInstitution, 'createdAt' | 'updatedAt'> {
    createdAt?: string;
    updatedAt?: string;
    translations: InstitutionTranslationCollection;
}

const InstitutionAggregateState = Data.case<InstitutionAggregateState>();

// Institution Aggregate Root
export class Institution {
    private constructor(private readonly data: InstitutionAggregateState) { }

    /**
     * Factory for creating a NEW Institution aggregate.
     * Use this when creating an institution from user input for the first time.
     */
    static create(props: {
        guidId?: string | null;
        typeId: string;
        modifiedBy: string;
        translations: InstitutionTranslationCollection;
    }): Effect.Effect<Institution, InstitutionInvariantViolationError> {
        const now = new Date().toISOString();
        const newInstitution = new Institution(
            InstitutionAggregateState({
                id: DbUtils.cuid2(),
                isActive: true,
                // Set temporary timestamps to satisfy toRaw() validation
                createdAt: now as string,
                updatedAt: now as string,
                guidId: props.guidId ?? null,
                typeId: props.typeId,
                modifiedBy: props.modifiedBy,
                translations: props.translations,
            }),
        );

        // Ensure the new institution is valid before returning it
        return pipe(
            newInstitution.validateInvariants(),
            Effect.map(() => newInstitution),
        );
    }

    /**
     * Factory for rehydrating an Institution aggregate from raw database data.
     */
    static fromDatabaseData(
        rawData: InstitutionData,
    ): Effect.Effect<
        Institution,
        | TranslationValidationError
        | ParseResult.ParseError
        | InstitutionInvariantViolationError
        | DuplicateLocaleError
    > {
        return pipe(
            Schema.decodeUnknown(DbInstitutionDataSchema)(rawData),
            Effect.mapError((parseError) =>
                TranslationValidationError.forField(
                    'Institution',
                    'database_validation',
                    `Institution database data validation failed: ${parseError.message}`,
                ),
            ),
            Effect.flatMap((validated) =>
                pipe(
                    validated.translations.map((t) => InstitutionTranslation.create(t)),
                    Effect.all,
                    Effect.flatMap(InstitutionTranslationCollection.create),
                    Effect.map((translations) => ({ ...validated, translations })),
                ),
            ),
            Effect.map(
                (hydratedData) =>
                    new Institution(
                        InstitutionAggregateState({
                            ...hydratedData,
                            id: rawData.id, // ensure original id is preserved
                        }),
                    ),
            ),
            Effect.flatMap((institution) =>
                pipe(
                    institution.validateInvariants(),
                    Effect.map(() => institution),
                ),
            ),
        );
    }

    // --- Business Methods ---

    activate(
        modifiedBy: string,
    ): Effect.Effect<Institution, InstitutionInvariantViolationError> {
        if (this.data.isActive) {
            return Effect.fail(
                new InstitutionInvariantViolationError({
                    institutionId: this.data.id,
                    reason: 'Cannot activate an institution that is already active',
                }),
            );
        }
        return Effect.succeed(
            new Institution(
                InstitutionAggregateState({
                    ...this.data,
                    isActive: true,
                    modifiedBy,
                    // `updatedAt` is no longer set here; the database trigger will handle it.
                }),
            ),
        );
    }

    deactivate(
        modifiedBy: string,
    ): Effect.Effect<Institution, InstitutionInvariantViolationError> {
        if (!this.data.isActive) {
            return Effect.fail(
                new InstitutionInvariantViolationError({
                    institutionId: this.data.id,
                    reason: 'Cannot deactivate an institution that is already inactive',
                }),
            );
        }
        return Effect.succeed(
            new Institution(
                InstitutionAggregateState({
                    ...this.data,
                    isActive: false,
                    modifiedBy,
                    // `updatedAt` is no longer set here; the database trigger will handle it.
                }),
            ),
        );
    }

    /**
     * Updates the institution with new data following the "replace all" pattern.
     * All translations are replaced atomically.
     */
    update(props: {
        guidId?: string | null;
        typeId: string;
        isActive: boolean;
        modifiedBy: string;
        translations: InstitutionTranslationCollection;
    }): Effect.Effect<Institution, InstitutionInvariantViolationError> {
        const newInstitution = new Institution(
            InstitutionAggregateState({
                ...this.data,
                guidId: props.guidId ?? null,
                typeId: props.typeId,
                isActive: props.isActive,
                modifiedBy: props.modifiedBy,
                translations: props.translations,
                // `updatedAt` is no longer set here; the database trigger will handle it.
            }),
        );

        return pipe(
            newInstitution.validateInvariants(),
            Effect.map(() => newInstitution),
        );
    }

    // --- Query Methods ---

    get id(): string {
        return this.data.id;
    }

    /**
     * Gets a composite view of all translatable fields, with locale fallbacks.
     * Delegates the complex logic to the InstitutionTranslationCollection.
     */
    getCompositeTranslations(locale: string, fallbackLocale = 'en') {
        return this.data.translations.getCompositeTranslations(
            locale,
            fallbackLocale,
        );
    }

    /**
     * Gets the entire collection of InstitutionTranslation value objects.
     * Useful for views that need to display all available translations, like an edit form.
     */
    getAllTranslations(): readonly InstitutionTranslation[] {
        return this.data.translations.getAll();
    }

    isActive(): boolean {
        return this.data.isActive ?? true;
    }

    toRaw(): Effect.Effect<InstitutionData, ParseResult.ParseError> {
        const SourceInstitutionSchema = Schema.extend(
            DbInstitutionDataSchema.omit('createdAt', 'updatedAt'),
            Schema.Struct({
                createdAt: Schema.optional(Schema.String),
                updatedAt: Schema.optional(Schema.String),
            }),
        );

        // 3. Create the failable transformer
        const transformer = Schema.transformOrFail(
            SourceInstitutionSchema,
            DbInstitutionDataSchema,
            {
                decode: (source, _, ast) => {
                    if (source.createdAt && source.updatedAt) {
                        // The cast is safe because we have checked the properties exist.
                        return ParseResult.succeed(source as InstitutionData);
                    }
                    return ParseResult.fail(
                        new ParseResult.Type(
                            ast,
                            source,
                            'Cannot serialize for persistence because createdAt or updatedAt is missing',
                        ),
                    );
                },
                encode: (val, _options, ast) =>
                    ParseResult.fail(
                        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                    ),
            },
        );

        const institutionData = R.omit(this.data, ['translations']);

        return pipe(
            this.data.translations.toPersistenceArray(),
            Effect.flatMap((persistableTranslations) =>
                Schema.decode(transformer)({
                    ...institutionData,
                    translations: persistableTranslations,
                }),
            ),
        );
    }

    // --- Invariant Validation ---

    private validateInvariants(): Effect.Effect<
        void,
        InstitutionInvariantViolationError
    > {
        // Check that institution has at least one translation
        if (this.data.translations.isEmpty()) {
            return Effect.fail(
                new InstitutionInvariantViolationError({
                    institutionId: this.data.id,
                    reason: 'Institution must have at least one translation',
                }),
            );
        }

        // Check that at least one translation has a name
        const hasValidName = this.data.translations.getAll().some((t) => {
            const name = t.getName();
            return name !== null && name !== undefined && name.trim() !== '';
        });

        if (!hasValidName) {
            return Effect.fail(
                new InstitutionInvariantViolationError({
                    institutionId: this.data.id,
                    reason: 'Institution must have at least one translation with a valid name',
                }),
            );
        }

        return Effect.succeed(void 0);
    }
}