import type { UserRoleFormSchema } from '@/app/[locale]/admin/roles-utilisateurs/(form)/user-role-form.schema';
import { useToast } from '@/components/hooks/use-toast';
import { getAllUsersOptions } from '@/hooks/admin/users/users.options';
import { assignRoleToUser } from '@/services/users/users.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const useGetAllUsers = () => {
  return useQuery(getAllUsersOptions());
};

export const useAssignRoleToUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (payload: UserRoleFormSchema) => {
      const userRole = {
        userId: payload.user.value,
        roleId: payload.role.value,
        resourceType:
          payload.resourceType.value !== '' ? payload.resourceType.value : null,
        resourceId:
          payload.resourceId.value !== '' ? payload.resourceId.value : null,
      };
      await assignRoleToUser(userRole);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role created successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to create permission group',
        variant: 'destructive',
      });
    },
  });
};
