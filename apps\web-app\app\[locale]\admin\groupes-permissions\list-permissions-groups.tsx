'use client';

import { permissionsGroupsColumns } from '@/app/[locale]/admin/groupes-permissions/columns';
import { PermissionsTable } from '@/components/permissions-table/permissions-table';
import { initialColumnVisibility } from '@/constants/infrastructures';
import { useGetAllPermissionsGroups } from '@/hooks/admin/permissions/permissions-groups.hook';
import type { SupportedLocale } from '@/types/locale';

type PermissionsGroupsListProps = {
  locale: SupportedLocale;
};

export const PermissionsGroupsList = ({
  locale,
}: PermissionsGroupsListProps) => {
  const { data: permissionsGroups, isLoading } =
    useGetAllPermissionsGroups<'list'>({
      view: 'list',
    });

  return (
    <PermissionsTable
      columns={permissionsGroupsColumns(locale)}
      data={permissionsGroups ?? []}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      resourceName="permissions"
    />
  );
};
