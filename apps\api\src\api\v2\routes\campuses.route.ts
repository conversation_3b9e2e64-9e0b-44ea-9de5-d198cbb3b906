import { handleEffectError } from '@/api/v2/utils/error-handler';
import { CampusesRuntime } from '@/infrastructure/runtimes/campuses.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  CampusDetailResponseDtoSchema,
  CampusEditResponseDtoSchema,
  CampusFormRequestDtoSchema,
  CampusListItemDtoSchema,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import {
  CampusSchema,
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { CampusesServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

export const getAllCampusesRoute = describeRoute({
  description: 'Lister tous les campus',
  operationId: 'getAllCampuses',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(CampusListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Campus retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const getCampusByIdRoute = describeRoute({
  description: 'Obtenir un campus par ID',
  operationId: 'getCampusById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue: detail ou edit',
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              CampusDetailResponseDtoSchema,
              CampusEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Campus trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Campus non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const createCampusRoute = describeRoute({
  description: 'Créer un campus',
  operationId: 'createCampus',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CampusFormRequestDtoSchema),
        example: {
          sadId: 'NEW_CAMPUS_001',
          isActive: true,
          institution: {
            value: 'sydsapwdh5p2m9z9tvnde2dp',
            label: 'University of Example',
          },
          names: [
            {
              locale: 'en',
              value: 'New Main Campus',
            },
            {
              locale: 'fr',
              value: 'Nouveau Campus Principal',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(CampusSchema),
        },
      },
      description: 'Campus créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - L'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const updateCampusRoute = describeRoute({
  description: 'Mettre à jour un campus',
  operationId: 'updateCampus',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CampusFormRequestDtoSchema),
        example: {
          sadId: 'UPDATED_CAMPUS_001',
          isActive: true,
          institution: {
            value: 'sydsapwdh5p2m9z9tvnde2dp',
            label: 'University of Example',
          },
          names: [
            {
              locale: 'en',
              value: 'Updated Main Campus',
            },
            {
              locale: 'fr',
              value: 'Campus Principal Mis à Jour',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(CampusSchema),
        },
      },
      description: 'Campus mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Campus non trouvé ou l'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const deleteCampusRoute = describeRoute({
  description: 'Supprimer un campus',
  operationId: 'deleteCampus',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Campus supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Campus non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

const campusesRoute = new Hono<{
  Variables: HonoVariables;
}>();

campusesRoute.get(
  '/',
  getAllCampusesRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.getAllCampuses({
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('campus:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Campuses][GET /] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Campuses][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Campuses][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.get(
  '/:id',
  getCampusByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');
    const { id } = ctx.req.valid('param');
    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.getCampusById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('campus:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.post(
  '/',
  createCampusRoute,
  // Debug incoming body before schema validation to investigate 400s  
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Campuses][POST] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Campuses][POST] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Campuses][POST] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Campuses][POST] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Campuses][POST] Calling campus service...');
      const campusService = yield* CampusesServiceLive;
      console.log('[Route][Campuses][POST] Campus service obtained, calling createCampus...');
      try {
        const result = yield* campusService.createCampus({
          campusDto: body,
          userId: user.id,
        });
        console.log('[Route][Campuses][POST] Service call completed successfully');
        return result;
      } catch (error) {
        console.error('[Route][Campuses][POST] Service call failed:', error);
        throw error;
      }
    }).pipe(
      withPolicy(checkPermission('campus:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Campuses][POST] Running program...');
    const result = await CampusesRuntime.runPromiseExit(program);
    console.log('[Route][Campuses][POST] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Campuses][POST] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Campuses][POST] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Campuses][POST] Success! Returning result');
      return ctx.json(result.value, 201);
    }

    console.log('[Route][Campuses][POST] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.put(
  '/:id',
  updateCampusRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  // Debug incoming body before schema validation to investigate 400s
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Campuses][PUT] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Campuses][PUT] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Campuses][PUT] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Campuses][PUT] Campus ID:', id);
    console.log('[Route][Campuses][PUT] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Campuses][PUT] Calling campus service...');
      const campusService = yield* CampusesServiceLive;
      console.log('[Route][Campuses][PUT] Campus service obtained, calling updateCampus...');
      const result = yield* campusService.updateCampus({
        id,
        campusDto: body,
        userId: user.id,
      });
      console.log('[Route][Campuses][PUT] Service call completed successfully');
      return result;
    }).pipe(
      withPolicy(checkPermission('campus:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Campuses][PUT] Running program...');
    const result = await CampusesRuntime.runPromiseExit(program);
    console.log('[Route][Campuses][PUT] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Campuses][PUT] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Campuses][PUT] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Campuses][PUT] Success! Returning result');
      return ctx.json(result.value);
    }

    console.log('[Route][Campuses][PUT] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.delete(
  '/:id',
  deleteCampusRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.deleteCampus(id);
    }).pipe(
      withPolicy(checkPermission('campus:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Campus deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { campusesRoute };

