# Getting Started with <PERSON>IE

Welcome to the RIE (Research Infrastructure and Equipment) management system! This guide will help you get up and running quickly.

## 🎯 What is RIE?

RIE is a comprehensive management system for research infrastructures and equipment at the University of Montreal. It helps researchers and administrators:

- Manage research infrastructure and equipment inventory
- Handle user permissions and access control
- Track equipment usage and maintenance
- Facilitate collaboration between research teams

## 🏗️ System Overview

RIE is built as a modern TypeScript monorepo with:

- **Frontend**: Next.js 15 with React, TypeScript, and Tailwind CSS
- **Backend**: Hono API with Effect-ts and PostgreSQL
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth with role-based permissions
- **Testing**: <PERSON><PERSON><PERSON>, <PERSON>wright, and Storybook
- **Deployment**: <PERSON><PERSON> and <PERSON>er Compose

## 🚀 Quick Start (5 minutes)

### Prerequisites

Make sure you have installed:

- **Node.js** (v20 or higher)
- **pnpm** (v10 or higher) - `npm install -g pnpm`
- **Bun** (latest) - for API development
- **Docker** and **<PERSON><PERSON> Compose** - for database
- **Git** - for version control

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
# Clone the repository
git clone <repository-url>
cd rie-fullstack

# Install dependencies
pnpm install --frozen-lockfile
```

### 2. Environment Setup

```bash
# Copy environment files
cp .env.example .env

# Edit the environment files with your settings
```
See [installation.md](./installation.md) for detailed environment configuration

### 3. MySQL to PostgreSQL Data Migration (Optional)

**⚠️ Only required if you need to convert existing MySQL database data to PostgreSQL format**

If you have existing MySQL data that needs to be migrated to PostgreSQL, complete this step **before** running `db:start`:

#### Prerequisites

- Place your MySQL dump file named exactly `mysqldump.sql` in the `packages/db-schema/src/migration-scripts/data/` directory

#### Migration Process

```bash
# Step 1: Clean the SQL dump file
cd packages/db-schema
bun src/migration-scripts/clean-sql.ts

# Step 2: Execute the migration conversion
pnpm migrate-mysql-pg
```

**Important Notes:**
- The MySQL dump file must be named exactly `mysqldump.sql`
- The cleaning script must be run first to prepare the SQL file
- This process converts MySQL data format to PostgreSQL-compatible format
- Complete this migration **before** proceeding to database setup

### 4. Database Setup

```bash
# Start PostgreSQL with Docker, apply migrations and seed data
cd packages/db-schema
pnpm db:start
```

### 5. Start Development

```bash
# From the root directory, start all services
pnpm dev --filter=api,web-app

# Or start services individually:
# API: cd apps/api && bun run --hot src/index.ts
# Web App: cd apps/web-app && pnpm dev
```

### 6. Verify Setup

- **Web App**: <http://localhost:3000>
- **API**: <http://localhost:4000>
- **API Docs**: <http://localhost:4000/api/doc> (Swagger UI)
- **Database Studio**: `cd packages/db-schema && pnpm run db:studio`

## 📚 Next Steps

Now that you have the system running:

1. **[Complete Installation](./installation.md)** - Detailed setup instructions
2. **[Development Environment](./development-environment.md)** - Configure your IDE and tools
3. **[Make Your First Contribution](./first-contribution.md)** - Submit your first change
4. **[Architecture Overview](../architecture/README.md)** - Understand the system design

## 🔧 Development Workflow

### Daily Development Commands

```bash
# Start development (all services)
pnpm dev

# Run tests
pnpm test

# Lint and format code
pnpm lint

# Build everything
pnpm build

# Type checking
pnpm type-check
```

### Working with the Database

```bash
cd apps/db-schema

# Start database (with migrations and seed data)
bun run db:start

# Generate new migration only
bun run db:generate

# Run migrations only
bun run db:migrate

# Open database studio
bun run db:studio
```

### Working with Packages

```bash
# Add dependency to specific package
cd apps/web-app
pnpm add some-package

# Add shared dependency to workspace root
pnpm add -w some-package

# Build only shared packages
pnpm build:packages
```

## 🆘 Common Issues

### Port Already in Use

```bash
# Kill processes on ports 3000 and 4000
lsof -ti:3000 | xargs kill -9
lsof -ti:4000 | xargs kill -9
```

### Database Connection Issues

```bash
# Restart PostgreSQL container
docker-compose restart postgres

# Check container logs
docker-compose logs postgres
```

### Package Installation Issues

```bash
# Clear pnpm cache
pnpm store prune

# Delete node_modules and reinstall
rm -rf node_modules
pnpm install --frozen-lockfile
```

### Dependency Version Mismatches

If you encounter issues with conflicting versions of packages like `effect-ts`, `pg`, `drizzle-orm`, or other dependencies across the monorepo:

```bash
# Check for version mismatches across packages
pnpm syncpack:list

# Automatically fix version mismatches
pnpm syncpack:fix

# Then reinstall dependencies
pnpm install --frozen-lockfile
```

**Common symptoms of version mismatches:**

- TypeScript errors about incompatible types between packages
- Runtime errors about missing methods or properties
- Build failures in specific packages
- Effect-ts schema validation errors

## 📖 Key Concepts

### Monorepo Structure

- **apps/**: Applications (web-app, api, e2e tests)
- **packages/**: Shared libraries and configurations
- **docs/**: Documentation (you are here!)

### Development Tools

- **Turborepo**: Build system and task runner
- **pnpm**: Package manager with workspace support
- **Biome**: Linting and formatting
- **Syncpack**: Keep dependencies in sync

### Architecture Patterns

- **Effect-ts**: Functional programming and dependency injection
- **Clean Architecture**: Separation of concerns
- **Domain-Driven Design**: Business logic organization

## 🎯 What's Next?

Choose your path based on what you want to work on:

- **Frontend Development**: [Frontend Guide](../guides/frontend-development.md)
- **Backend Development**: [API Guide](../guides/api-development.md)
- **Understanding the System**: [Architecture Overview](../architecture/README.md)
- **Contributing Code**: [Contributing Guide](../contributing/README.md)

---

**Need help?** Check the [troubleshooting guide](../guides/troubleshooting.md) or ask the team!
