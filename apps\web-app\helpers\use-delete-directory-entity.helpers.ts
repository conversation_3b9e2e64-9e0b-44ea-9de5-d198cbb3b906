import { useDeleteBuilding } from '@/hooks/directory/buildings.hook';
import { useDeleteCampus } from '@/hooks/directory/campuses.hook';
import { useDeleteEstablishment } from '@/hooks/directory/establishments.hook';
import { useDeleteFundingProject } from '@/hooks/directory/funding-projects.hook';
import { useDeleteRoom } from '@/hooks/directory/rooms.hook';
import { useDeleteUnit } from '@/hooks/directory/units.hook';
import type { DirectoryEntity } from '@rie/domain/types';
import { useDeleteVendor } from '../hooks/directory/vendors.hook';

export const useDeleteDirectoryEntityHelper = (directoryEntity: DirectoryEntity) => {
  if (directoryEntity === 'vendor') {
    return useDeleteVendor();
  }
  if (directoryEntity === 'people') {
    // return useDeletePerson();
  }
  if (directoryEntity === 'building') {
    return useDeleteBuilding();
  }
  if (directoryEntity === 'campus') {
    return useDeleteCampus();
  }
  if (directoryEntity === 'institution') {
    return useDeleteEstablishment();
  }
  if (directoryEntity === 'fundingProject') {
    return useDeleteFundingProject();
  }
  if (directoryEntity === 'room') {
    return useDeleteRoom();
  }
  if (directoryEntity === 'unit') {
    return useDeleteUnit();
  }
  return useDeleteVendor();
};
