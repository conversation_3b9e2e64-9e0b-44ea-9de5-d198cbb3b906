'use client';

import { campusColumns } from '@/app/[locale]/bottin/campus/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/campus';
import type { SupportedLocale } from '@/types/locale';
import type { CampusListItemDTO } from '../../../../../../packages/api-contracts/build/dts/campuses';

type CampusListProps = {
  locale: SupportedLocale;
};

export const CampusList = ({ locale }: CampusListProps) => {
  return (
    <DirectoryList<'campus', 'list', CampusListItemDTO, Record<string, unknown>>
      directoryListKey="campus"
      view="list"
      locale={locale}
      columns={campusColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="campus"
    />
  );
};
