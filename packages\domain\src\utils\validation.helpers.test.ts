import { describe, expect, it } from 'vitest';
import {
  type OptionalFieldName,
  type RequiredFieldName,
  all,
  createMaxLengthFieldValidator,
  createOptionalFieldValidator,
  createOptionalStringValidator,
  createRequiredFieldValidator,
  createRequiredStringValidator,
  createValidLocaleValidator,
  or,
  validateL<PERSON>aleCode,
  validateOptionalDescription,
  validateRequiredLocale,
} from './validation.helpers';

// Test interface
interface TestEntity {
  requiredField: string;
  optionalField: string | null;
  optionalUndefinedField: string | undefined;
  optionalBothField: string | null | undefined;
}

type RequiredFields = RequiredFieldName<TestEntity>;
type OptionalFields = OptionalFieldName<TestEntity>;

describe('Type extraction', () => {
  it('should correctly identify required fields', () => {
    // This is a compile-time test - if it compiles, the types work
    const requiredValidator = createRequiredStringValidator<
      TestEntity,
      RequiredFields
    >();
    expect(typeof requiredValidator).toBe('function');
  });

  it('should correctly identify optional fields', () => {
    // This is a compile-time test - if it compiles, the types work
    const optionalValidator = createOptionalStringValidator<
      TestEntity,
      OptionalFields
    >();
    expect(typeof optionalValidator).toBe('function');
  });
});

describe('createRequiredStringValidator', () => {
  const validator = createRequiredStringValidator<
    TestEntity,
    'requiredField'
  >();

  it('should pass validation for non-empty string', () => {
    const result = validator('valid string', 'requiredField');
    expect(result).toEqual({});
  });

  it('should fail validation for empty string', () => {
    const result = validator('', 'requiredField');
    expect(result).toEqual({
      requiredField: 'requiredField is required',
    });
  });

  it('should fail validation for whitespace-only string', () => {
    const result = validator('   ', 'requiredField');
    expect(result).toEqual({
      requiredField: 'requiredField is required',
    });
  });

  it('should use custom error message', () => {
    const customValidator = createRequiredStringValidator<
      TestEntity,
      'requiredField'
    >('Custom required message');
    const result = customValidator('', 'requiredField');
    expect(result).toEqual({
      requiredField: 'Custom required message',
    });
  });
});

describe('createMaxLengthFieldValidator', () => {
  const validator = createMaxLengthFieldValidator<TestEntity, 'requiredField'>(
    5,
  );

  it('should pass validation for string within limit', () => {
    const result = validator('hello', 'requiredField');
    expect(result).toEqual({});
  });

  it('should pass validation for string at exact limit', () => {
    const result = validator('hello', 'requiredField');
    expect(result).toEqual({});
  });

  it('should fail validation for string exceeding limit', () => {
    const result = validator('hello world', 'requiredField');
    expect(result).toEqual({
      requiredField: 'requiredField cannot be longer than 5 characters',
    });
  });

  it('should use custom error message', () => {
    const customValidator = createMaxLengthFieldValidator<
      TestEntity,
      'requiredField'
    >(5, 'Custom length message');
    const result = customValidator('too long', 'requiredField');
    expect(result).toEqual({
      requiredField: 'Custom length message',
    });
  });
});

describe('createOptionalStringValidator', () => {
  const validator = createOptionalStringValidator<
    TestEntity,
    'optionalField'
  >();

  it('should pass validation for null', () => {
    const result = validator(null, 'optionalField');
    expect(result).toEqual({});
  });

  it('should pass validation for non-empty string', () => {
    const result = validator('valid string', 'optionalField');
    expect(result).toEqual({});
  });

  it('should pass validation for empty string', () => {
    const result = validator('', 'optionalField');
    expect(result).toEqual({});
  });

  it('should pass validation for whitespace-only string', () => {
    const result = validator('   ', 'optionalField');
    expect(result).toEqual({});
  });
});

describe('all combinator', () => {
  const requiredValidator = createRequiredStringValidator<
    TestEntity,
    'requiredField'
  >();
  const maxLengthValidator = createMaxLengthFieldValidator<
    TestEntity,
    'requiredField'
  >(5);
  const combinedValidator = all([requiredValidator, maxLengthValidator]);

  it('should pass when all validators pass', () => {
    const result = combinedValidator('hello', 'requiredField');
    expect(result).toEqual({});
  });

  it('should fail with first error when first validator fails', () => {
    const result = combinedValidator('', 'requiredField');
    expect(result).toEqual({
      requiredField: 'requiredField is required',
    });
  });

  it('should fail with second error when first passes but second fails', () => {
    const result = combinedValidator('too long string', 'requiredField');
    expect(result).toEqual({
      requiredField: 'requiredField cannot be longer than 5 characters',
    });
  });

  it('should handle empty validator array', () => {
    const emptyValidator = all<TestEntity, 'requiredField'>([]);
    const result = emptyValidator('any value', 'requiredField');
    expect(result).toEqual({});
  });
});

describe('or combinator', () => {
  const optionalValidator = createOptionalStringValidator<
    TestEntity,
    'optionalField'
  >();
  const maxLengthValidator = createMaxLengthFieldValidator<
    TestEntity,
    'optionalField'
  >(5);
  const combinedValidator = or([optionalValidator, maxLengthValidator]);

  it('should pass when first validator passes (null value)', () => {
    const result = combinedValidator(null, 'optionalField');
    expect(result).toEqual({});
  });

  it('should pass when first validator passes (any string value)', () => {
    const result = combinedValidator('hello', 'optionalField');
    expect(result).toEqual({});
  });

  it('should pass when first validator passes (even long strings)', () => {
    const result = combinedValidator('too long string', 'optionalField');
    expect(result).toEqual({}); // Now passes because optional validator always passes
  });

  it('should handle empty validator array', () => {
    const emptyValidator = or<TestEntity, 'optionalField'>([]);
    const result = emptyValidator('any value', 'optionalField');
    expect(result).toEqual({});
  });

  it('should demonstrate or combinator with validators that can actually fail', () => {
    // Use two validators that can both fail on the same field type
    const shortLengthValidator = createMaxLengthFieldValidator<
      TestEntity,
      'optionalField'
    >(3);
    const veryShortLengthValidator = createMaxLengthFieldValidator<
      TestEntity,
      'optionalField'
    >(1);
    const testValidator = or([shortLengthValidator, veryShortLengthValidator]);

    // Both validators fail on a long string - should return last error
    const result = testValidator('toolong', 'optionalField');
    expect(result).toEqual({
      optionalField: 'optionalField cannot be longer than 1 characters',
    });
  });
});

describe('createRequiredFieldValidator (convenience function)', () => {
  it('should validate required field without max length', () => {
    const validator = createRequiredFieldValidator<
      TestEntity,
      'requiredField'
    >();

    expect(validator('valid', 'requiredField')).toEqual({});
    expect(validator('', 'requiredField')).toEqual({
      requiredField: 'requiredField is required',
    });
  });

  it('should validate required field with max length', () => {
    const validator = createRequiredFieldValidator<TestEntity, 'requiredField'>(
      5,
      {
        required: 'Custom required',
        maxLength: 'Custom max length',
      },
    );

    expect(validator('hello', 'requiredField')).toEqual({});
    expect(validator('', 'requiredField')).toEqual({
      requiredField: 'Custom required',
    });
    expect(validator('too long', 'requiredField')).toEqual({
      requiredField: 'Custom max length',
    });
  });
});

describe('createOptionalFieldValidator (convenience function)', () => {
  it('should validate optional field without max length', () => {
    const validator = createOptionalFieldValidator<
      TestEntity,
      'optionalField'
    >();

    expect(validator(null, 'optionalField')).toEqual({});
    expect(validator('valid', 'optionalField')).toEqual({});
    expect(validator('', 'optionalField')).toEqual({
      optionalField: 'optionalField cannot be empty',
    });
  });

  it('should validate optional field with max length', () => {
    const validator = createOptionalFieldValidator<TestEntity, 'optionalField'>(
      5,
      {
        maxLength: 'Custom max length',
      },
    );

    expect(validator(null, 'optionalField')).toEqual({});
    expect(validator('hello', 'optionalField')).toEqual({});
    expect(validator('too long', 'optionalField')).toEqual({
      optionalField: 'Custom max length',
    });
  });
});

describe('Pre-configured validators', () => {
  describe('validateRequiredName', () => {
    it('should validate required name with 150 character limit', () => {
      expect(validateRequiredLocale('Valid Name', 'locale')).toEqual({});
      expect(validateRequiredLocale('', 'locale')).toEqual({
        locale: 'Name is required',
      });

      const longName = 'a'.repeat(151);
      expect(validateRequiredLocale(longName, 'locale')).toEqual({
        locale: 'Name must be 150 characters or less',
      });
    });
  });

  describe('validateOptionalDescription', () => {
    it('should validate optional description with 1500 character limit', () => {
      expect(validateOptionalDescription(null, 'description')).toEqual({});
      expect(
        validateOptionalDescription('Valid description', 'description'),
      ).toEqual({});

      const longDesc = 'a'.repeat(1501);
      expect(validateOptionalDescription(longDesc, 'description')).toEqual({
        description: 'Description must be 1500 characters or less',
      });
    });
  });
});

describe('createValidLocaleValidator', () => {
  const localeValidator = createValidLocaleValidator();

  it('should pass validation for valid ISO 3166-1 alpha-2 codes', () => {
    const validCodes = ['US', 'CA', 'GB', 'FR', 'DE', 'JP', 'AU'];

    for (const code of validCodes) {
      expect(localeValidator(code)).toEqual({});
    }
  });

  it('should pass validation for valid codes in lowercase', () => {
    const validCodes = ['us', 'ca', 'gb', 'fr', 'de'];

    for (const code of validCodes) {
      expect(localeValidator(code)).toEqual({});
    }
  });

  it('should pass validation for valid codes with whitespace', () => {
    expect(localeValidator(' US ')).toEqual({});
    expect(localeValidator('\tCA\n')).toEqual({});
  });

  it('should fail validation for invalid codes', () => {
    const invalidCodes = ['XX', 'ZZ', 'ABC', '123', 'U', 'USA'];

    for (const code of invalidCodes) {
      expect(localeValidator(code)).toEqual({
        requiredField:
          'requiredField must be a valid ISO 3166-1 alpha-2 country code',
      });
    }
  });

  it('should pass validation for null/undefined values', () => {
    expect(localeValidator('')).toEqual({});
  });

  it('should use custom error message', () => {
    const customValidator = createValidLocaleValidator(
      'Invalid country code provided',
    );

    const result = customValidator('XX');
    expect(result).toEqual({
      requiredField: 'Invalid country code provided',
    });
  });

  it('should handle edge cases', () => {
    // Empty string after trim
    expect(localeValidator('   ')).toEqual({
      requiredField:
        'requiredField must be a valid ISO 3166-1 alpha-2 country code',
    });

    // Special characters
    expect(localeValidator('U$')).toEqual({
      requiredField:
        'requiredField must be a valid ISO 3166-1 alpha-2 country code',
    });
  });
});

describe('validateLocaleCode (pre-configured)', () => {
  it('should validate locale field with custom message', () => {
    expect(validateLocaleCode('US')).toEqual({});
    expect(validateLocaleCode('XX')).toEqual({
      locale: 'Locale must be a valid ISO 3166-1 alpha-2 country code',
    });
  });
});

// Integration test with other validators
describe('Locale validation integration', () => {
  it('should work with required field validation', () => {
    const validator = all([
      createRequiredStringValidator<TestEntity, 'requiredField'>(),
      createValidLocaleValidator(),
    ]);

    // Valid case
    expect(validator('US', 'requiredField')).toEqual({});

    // Required validation fails first
    expect(validator('', 'requiredField')).toEqual({
      requiredField: 'requiredField is required',
    });

    // Locale validation fails second
    expect(validator('XX', 'requiredField')).toEqual({
      locale: 'locale must be a valid ISO 3166-1 alpha-2 country code',
    });
  });

  it('should work with optional field validation', () => {
    const validator = or([
      createOptionalStringValidator<TestEntity, 'optionalField'>(),
      createMaxLengthFieldValidator<TestEntity, 'optionalField'>(10),
    ]);

    // Null passes
    expect(validator(null, 'optionalField')).toEqual({});

    // Valid locale passes
    expect(validator('CA', 'optionalField')).toEqual({});
  });
});

// Edge cases and integration tests
describe('Integration tests', () => {
  it('should work with complex validation scenarios', () => {
    const complexValidator = all([
      createRequiredStringValidator<TestEntity, 'requiredField'>(
        'Field is mandatory',
      ),
      createMaxLengthFieldValidator<TestEntity, 'requiredField'>(
        10,
        'Too long!',
      ),
    ]);

    // Valid case
    expect(complexValidator('valid', 'requiredField')).toEqual({});

    // Required validation fails first
    expect(complexValidator('', 'requiredField')).toEqual({
      requiredField: 'Field is mandatory',
    });

    // Max length validation fails second
    expect(complexValidator('this is too long', 'requiredField')).toEqual({
      requiredField: 'Too long!',
    });
  });

  it('should handle whitespace correctly', () => {
    const validator = createRequiredStringValidator<
      TestEntity,
      'requiredField'
    >();

    expect(validator('  valid  ', 'requiredField')).toEqual({});
    expect(validator('\t\n  \r', 'requiredField')).toEqual({
      requiredField: 'requiredField is required',
    });
  });
});
