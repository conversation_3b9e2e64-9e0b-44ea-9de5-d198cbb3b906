import type { PermissionFormSchema } from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type {
  PermissionResultType,
  ResourcePermissionResultType,
} from '@/types/permission.type';
import type { DbPermission } from '@rie/db-schema/entity-types';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export async function getAllPermissions<
  View extends CollectionViewParamType['view'],
>({ view }: CollectionViewParamType) {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .get<PermissionResultType<View>>('v2/permissions', {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to get permissions');
  }
}

export type GetPermissionByIdParams = {
  view: ResourceViewType;
  id: string;
};

export const getPermissionById = async <View extends ResourceViewType>({
  id,
  view,
}: GetPermissionByIdParams) => {
  try {
    const client = await getApiClient();

    return await client
      .get<ResourcePermissionResultType<View>>(`v2/permissions/${id}`, {
        searchParams: {
          view,
        },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch permission');
  }
};

export const createPermission = async (payload: PermissionFormSchema) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .post<DbPermission>('v2/permissions', {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to create permission');
  }
};

export const updatePermission = async ({
  id,
  payload,
}: { id: string; payload: PermissionFormSchema }) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .put<DbPermission>(`v2/permissions/${id}`, {
        json: payload,
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to update permission');
  }
};

export const deletePermission = async (id: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(`v2/permissions/${id}`).json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to delete permission');
  }
};
