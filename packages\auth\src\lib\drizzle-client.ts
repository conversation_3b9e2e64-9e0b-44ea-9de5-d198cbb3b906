import type { ConnectionOptions } from 'node:tls';
import { env } from '@rie/config';
import * as DbSchema from '@rie/db-schema/schemas';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as Redacted from 'effect/Redacted';
import { Pool } from 'pg';

const getSSLConfig = (
  connectionType: 'none' | 'verify-self' | 'verify-ca',
): boolean | ConnectionOptions => {
  switch (connectionType) {
    case 'verify-ca':
      return {
        rejectUnauthorized: true,
        ca: process.env.DB_SSL_CA,
        cert: process.env.DB_SSL_CERT,
        key: process.env.DB_SSL_KEY,
      };
    case 'verify-self':
      return {
        rejectUnauthorized: false,
      }; // Forces SSL but allows self-signed certificates
    case 'none':
      return false; // Only for local development
    default:
      return true;
  }
};

const pool = new Pool({
  connectionString: Redacted.value(env.PG_DATABASE_URL),
  ssl: getSSLConfig(env.DATABASE_CONNECTION_SECURITY),
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

export const db = drizzle({
  client: pool,
  casing: 'snake_case',
  schema: DbSchema,
});
