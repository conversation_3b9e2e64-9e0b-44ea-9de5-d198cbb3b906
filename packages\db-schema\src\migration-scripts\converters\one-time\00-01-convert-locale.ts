import { OUTPUT_PATH } from '../../constants';
import { BaseConverter } from '../base-converter';

export class LocaleMigrationConverter extends BaseConverter {
  async convertFile(): Promise<void> {
    // The SQL content to append
    const localeSQL = `
-- Locale Inserts
-- Generated: 2025-04-28T14:00:40.973Z

INSERT INTO "locales" ("code") VALUES
('fr'),
('en');

-- Locale I18n Inserts
-- Generated: 2025-04-28T14:00:46.533Z

INSERT INTO "locales_i18n" ("id", "data_id", "locale", "name") VALUES
-- French language names
('tht7r838ecuef4noje3z7chj', 'fr', 'fr', 'Français'),
('cncpfapv0sw9etn1r0pec82z', 'fr', 'en', 'French'),

-- English language names
('sho3d547cw660nt9zlf9hx1r', 'en', 'fr', 'Anglais'),
('r4d11r87llbx62kcgeyfsm8k', 'en', 'en', 'English');
`;

    // Append the SQL content to the output file
    await this.safeAppendFile(OUTPUT_PATH, localeSQL);
    console.log('Locale data added successfully!');
  }
}
