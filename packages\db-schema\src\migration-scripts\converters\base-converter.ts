import type { MakeDirectoryOptions } from 'node:fs';
import { DbUtils } from '@rie/utils';
import {
  getTableIdMappings,
  storeTableIdMappings,
} from '../id-mapping-storage';
import type {
  MySQLI18NDescription,
  PostgresI18NBaseReturn,
  PostgresI18NDescription,
} from '../types';

export abstract class BaseConverter {
  protected isDryRun = false;

  constructor(dryRun = false) {
    this.isDryRun = dryRun;
  }

  protected generateCuid2(): string {
    return DbUtils.cuid2();
  }

  protected async loadEntityIdMappings(
    entityName: string,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  ): Promise<Record<string, any>> {
    try {
      return await this.getTableIdMappings(entityName);
    } catch (error) {
      console.error(`Error loading ${entityName} ID mappings:`, error);
      throw error;
    }
  }

  protected formatSqlValue(value: string | number | null | undefined): string {
    if (value === undefined) {
      return 'NULL'; // Treat undefined as NULL
    }
    if (value === null) {
      return 'NULL';
    }
    if (typeof value === 'number') {
      return value.toString();
    }

    // Also replace any backslash-escaped quotes with properly escaped PostgreSQL quotes
    const fixedValue = value.replace(/\\'/g, "''");

    return `'${fixedValue}'`;
  }

  protected parseNullableString(value: string): string | null {
    if (value === 'NULL') {
      return null;
    }

    // Remove outer quotes if present
    let result = value.replace(/^'|'$/g, '');

    // Replace any backslash escapes with the actual character
    result = result.replace(/\\(.)/g, '$1');

    // Replace doubled single quotes with a single quote (SQL escaping)
    // result = result.replace(/''/g, "'");

    return result;
  }

  protected generateCommentHeader(description: string): string {
    return `-- ${description}\n-- Generated: ${new Date().toISOString()}\n\n`;
  }

  protected extractCreateStatement(
    sqlContent: string,
    tableName: string,
  ): string {
    // Clean up the SQL content first
    const cleanSqlContent = sqlContent.replace(/\/n/g, '\n').trim();

    // Regex to match CREATE TABLE statement for the specified table
    // Handles: CREATE TABLE `tableName` (...) with constraints, foreign keys, etc. until semicolon
    const createTableRegex = new RegExp(
      `CREATE\\s+TABLE\\s+(?:\`|")?${tableName}(?:\`|")?\\s*\\([\\s\\S]*?\\)\\s*[\\s\\S]*?;`,
      'gim',
    );

    const match = cleanSqlContent.match(createTableRegex);

    if (!match || match.length === 0) {
      throw new Error(
        `Could not find CREATE TABLE statement for table: ${tableName}`,
      );
    }

    return match[0]; // Return the complete CREATE TABLE statement
  }

  protected extractInsertStatements(
    sqlContent: string,
    tableName: string,
  ): string[] {
    // Clean up the SQL content first
    const cleanSqlContent = sqlContent
      .replace(/\/n/g, '\n') // Replace /n with actual newlines
      .trim();

    const allInserts: string[] = [];

    // Find INSERT statements using quote-aware parsing instead of regex
    // This prevents splitting at semicolons inside quoted strings
    // Use word boundary to ensure exact table name match (not partial)
    const insertPattern = new RegExp(
      `INSERT\\s+INTO\\s+(?:\`|")?${tableName}(?:\`|")?\\s`,
      'gim',
    );

    let match: RegExpExecArray | null;

    // biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
    while ((match = insertPattern.exec(cleanSqlContent)) !== null) {
      const startIndex = match.index;

      // Find the VALUES keyword
      const valuesIndex = cleanSqlContent
        .toUpperCase()
        .indexOf('VALUES', startIndex);
      if (valuesIndex === -1) {
        continue;
      }

      // Extract the complete INSERT statement using quote-aware parsing
      const completeStatement = this.extractCompleteInsertStatement(
        cleanSqlContent,
        startIndex,
      );

      if (completeStatement) {
        // Get the base INSERT part (everything up to VALUES)
        const baseInsert = completeStatement
          .substring(0, completeStatement.toUpperCase().indexOf('VALUES'))
          .trim();

        // Get the values part using quote-aware extraction
        const valuesMatch = completeStatement.match(/VALUES\s*(.+?);$/is);
        if (!valuesMatch) {
          console.log('Warning: Could not extract values from statement');
          continue;
        }

        // Get just the values content - DO NOT remove parentheses here
        // The splitValueRows method expects the full VALUES content with parentheses
        const valuesContent = valuesMatch[1]?.trim() ?? '';

        // Split the values content into rows using quote-aware parsing
        const valueRows = this.splitValueRows(valuesContent);

        // Create individual INSERT statements for each value set
        for (let i = 0; i < valueRows.length; i++) {
          const row = valueRows[i];
          const singleInsert = `${baseInsert} VALUES (${row});`;
          allInserts.push(singleInsert);
        }
      }

      // Reset lastIndex to continue searching (global regex issue)
      insertPattern.lastIndex = match.index + 1;
    }

    return allInserts;
  }

  /**
   * Extract a complete INSERT statement using quote-aware parsing
   * This prevents splitting at semicolons inside quoted strings
   */
  private extractCompleteInsertStatement(
    sqlContent: string,
    startIndex: number,
  ): string | null {
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let foundValues = false;

    for (let i = startIndex; i < sqlContent.length; i++) {
      const char = sqlContent[i];
      current += char;

      // Check if we've found the VALUES keyword
      if (!foundValues && current.toUpperCase().includes('VALUES')) {
        foundValues = true;
      }

      if (!inQuotes && (char === '"' || char === "'")) {
        // Starting a quoted string
        inQuotes = true;
        quoteChar = char;
      } else if (inQuotes && char === '\\') {
        // Handle backslash escaping
        if (i + 1 < sqlContent.length) {
          current += sqlContent[i + 1];
          i++; // Skip the next character
        }
      } else if (inQuotes && char === quoteChar) {
        // Check if this is an escaped quote (doubled quote)
        if (i + 1 < sqlContent.length && sqlContent[i + 1] === quoteChar) {
          // Escaped quote, add both characters
          current += char;
          i++; // Skip the next character
        } else {
          // End of quoted string
          inQuotes = false;
          quoteChar = '';
        }
      } else if (!inQuotes && char === ';' && foundValues) {
        // Found the end of the INSERT statement
        return current;
      }
    }

    return null; // No complete statement found
  }

  protected extractColumnNames(createTableStatement: string): string[] {
    // Extract the column definitions from CREATE TABLE statement
    // First, find the opening parenthesis after CREATE TABLE tablename
    const tableNameMatch = createTableStatement.match(
      /CREATE\s+TABLE\s+(?:`|")?[\w]+(?:`|")?\s*\(/i,
    );
    if (!tableNameMatch) {
      throw new Error(
        'Could not find table name and opening parenthesis in CREATE TABLE statement',
      );
    }

    // Find the content between the parentheses, handling nested parentheses
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    const startIndex = tableNameMatch.index! + tableNameMatch[0].length - 1; // Position of opening (
    let endIndex = -1;
    let depth = 0;
    let inQuotesForParsing = false;
    let quoteCharForParsing = '';

    for (let i = startIndex; i < createTableStatement.length; i++) {
      const char = createTableStatement[i];

      if (!inQuotesForParsing && (char === '"' || char === "'")) {
        inQuotesForParsing = true;
        quoteCharForParsing = char;
      } else if (inQuotesForParsing && char === quoteCharForParsing) {
        // Check if it's an escaped quote
        if (
          i + 1 < createTableStatement.length &&
          createTableStatement[i + 1] === quoteCharForParsing
        ) {
          i++; // Skip the next quote
        } else {
          inQuotesForParsing = false;
          quoteCharForParsing = '';
        }
      } else if (!inQuotesForParsing) {
        if (char === '(') {
          depth++;
        } else if (char === ')') {
          depth--;
          if (depth === 0) {
            endIndex = i;
            break;
          }
        }
      }
    }

    if (endIndex === -1) {
      throw new Error(
        'Could not find closing parenthesis in CREATE TABLE statement',
      );
    }

    const columnDefinitions = createTableStatement
      .substring(startIndex + 1, endIndex)
      .trim();

    // Split by comma, but respect parentheses (for things like ENUM('a','b'))
    const columns: string[] = [];
    let currentColumn = '';
    let parenthesesDepth = 0;
    let inQuotesForColumns = false;
    let quoteCharForColumns = '';

    for (let i = 0; i < columnDefinitions.length; i++) {
      const char = columnDefinitions[i];

      if ((char === '"' || char === "'") && !inQuotesForColumns) {
        inQuotesForColumns = true;
        quoteCharForColumns = char;
      } else if (char === quoteCharForColumns && inQuotesForColumns) {
        inQuotesForColumns = false;
        quoteCharForColumns = '';
      } else if (!inQuotesForColumns) {
        if (char === '(') {
          parenthesesDepth++;
        } else if (char === ')') {
          parenthesesDepth--;
        } else if (char === ',' && parenthesesDepth === 0) {
          // This is a column separator
          const trimmedColumn = currentColumn.trim();
          if (
            trimmedColumn &&
            !trimmedColumn.toUpperCase().startsWith('KEY') &&
            !trimmedColumn.toUpperCase().startsWith('INDEX') &&
            !trimmedColumn.toUpperCase().startsWith('CONSTRAINT') &&
            !trimmedColumn.toUpperCase().startsWith('PRIMARY') &&
            !trimmedColumn.toUpperCase().startsWith('FOREIGN') &&
            !trimmedColumn.toUpperCase().startsWith('UNIQUE')
          ) {
            // Extract just the column name (first word, removing backticks)
            const columnName = trimmedColumn.split(/\s+/)[0]?.replace(/`/g, '');
            if (columnName) {
              columns.push(columnName);
            }
          }
          currentColumn = '';
          continue;
        }
      }

      currentColumn += char;
    }

    // Handle the last column
    const trimmedColumn = currentColumn.trim();
    if (
      trimmedColumn &&
      !trimmedColumn.toUpperCase().startsWith('KEY') &&
      !trimmedColumn.toUpperCase().startsWith('INDEX') &&
      !trimmedColumn.toUpperCase().startsWith('CONSTRAINT') &&
      !trimmedColumn.toUpperCase().startsWith('PRIMARY') &&
      !trimmedColumn.toUpperCase().startsWith('FOREIGN') &&
      !trimmedColumn.toUpperCase().startsWith('UNIQUE')
    ) {
      const columnName = trimmedColumn.split(/\s+/)[0]?.replace(/`/g, '');
      if (columnName) {
        columns.push(columnName);
      }
    }

    if (columns.length === 0) {
      throw new Error('No column names found in CREATE TABLE statement');
    }

    return columns;
  }

  protected extractValuesFromInsertStatement(
    insertStatement: string,
    sqlContent: string,
    tableName: string,
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  ): Record<string, any> {
    // Get column names from CREATE TABLE statement
    const createStatement = this.extractCreateStatement(sqlContent, tableName);
    const columns = this.extractColumnNames(createStatement);

    // Extract the values portion - this is a single row INSERT statement
    // created by our extractInsertStatements function
    // Use 's' flag to make . match newlines for multiline strings
    const valuesMatch = insertStatement.match(/VALUES\s*\((.+?)\);$/is);

    if (!valuesMatch || !valuesMatch[1]) {
      throw new Error('Could not extract values from INSERT statement');
    }

    const valuesContent = valuesMatch[1];

    // Parse CSV-like values respecting quotes
    const valuesList = this.parseCSVValues(valuesContent);

    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const values: Record<string, any> = {};

    // Check if we have the right number of values
    if (valuesList.length < columns.length) {
      throw new Error(
        `Column-value mismatch: expected ${columns.length} values for columns [${columns.join(', ')}], but got ${valuesList.length} values: [${valuesList.join(', ')}]`,
      );
    }

    columns.forEach((col, index) => {
      let value = index < valuesList.length ? valuesList[index]?.trim() : null;

      // Remove quotes if present
      if (value && (value.startsWith("'") || value.startsWith('"'))) {
        // Make sure we have a closing quote
        if (
          (value.startsWith("'") && value.endsWith("'")) ||
          (value.startsWith('"') && value.endsWith('"'))
        ) {
          // Remove the outer quotes
          value = value.slice(1, -1);

          // Replace any backslash escapes with the actual character
          // This handles cases like: 'Centre hospitalier de l\'Université de Montréal'
          value = value.replace(/\\(.)/g, '$1');

          // Replace doubled single quotes with a single quote (SQL escaping)
          // This handles cases like: 'Centre hospitalier de l''Université de Montréal'
          // value = value.replace(/''/g, "'");
        } else {
          console.warn(
            `Warning: Value starts with quote but doesn't end with one: ${value}`,
          );
        }
      }

      // Convert 'NULL' string to null
      if (value === 'NULL') {
        value = null;
      }

      values[col] = value;
    });

    return values;
  }

  protected parseCSVValues(csvString: string): string[] {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';

    for (let i = 0; i < csvString.length; i++) {
      const char = csvString[i];

      if (!inQuotes && (char === '"' || char === "'")) {
        // Starting a quoted string
        inQuotes = true;
        quoteChar = char;
        current += char;
      } else if (inQuotes && char === '\\') {
        // Handle backslash escaping
        if (i + 1 < csvString.length) {
          // Add the backslash and the next character
          current += char + csvString[i + 1];
          i++; // Skip the next character
        } else {
          current += char;
        }
      } else if (inQuotes && char === quoteChar) {
        // Check if this is an escaped quote (doubled quote)
        if (i + 1 < csvString.length && csvString[i + 1] === quoteChar) {
          // Escaped quote, add both characters
          current += char + char;
          i++; // Skip the next character
        } else {
          // End of quoted string
          inQuotes = false;
          quoteChar = '';
          current += char;
        }
      } else if (!inQuotes && char === ',') {
        // Field separator
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last value
    if (current.trim()) {
      values.push(current.trim());
    }

    return values;
  }

  protected splitValueRows(valuesContent: string): string[] {
    const rows: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let parenDepth = 0;

    for (let i = 0; i < valuesContent.length; i++) {
      const char = valuesContent[i];

      if (!inQuotes && (char === '"' || char === "'")) {
        // Starting a quoted string
        inQuotes = true;
        quoteChar = char;
        current += char;
      } else if (inQuotes && char === '\\') {
        // Handle backslash escaping
        if (i + 1 < valuesContent.length) {
          current += char + valuesContent[i + 1];
          i++; // Skip the next character
        } else {
          current += char;
        }
      } else if (inQuotes && char === quoteChar) {
        // Check if this is an escaped quote (doubled quote)
        if (
          i + 1 < valuesContent.length &&
          valuesContent[i + 1] === quoteChar
        ) {
          // Escaped quote, add both characters
          current += char + char;
          i++; // Skip the next character
        } else {
          // End of quoted string
          inQuotes = false;
          quoteChar = '';
          current += char;
        }
      } else if (!inQuotes && char === '(') {
        parenDepth++;
        current += char;
      } else if (!inQuotes && char === ')') {
        parenDepth--;
        current += char;

        // If we're at depth 0, this completes a row
        if (parenDepth === 0) {
          // Remove the outer parentheses and add the row
          const cleanedRow = current.trim().replace(/^\(|\)$/g, '');

          rows.push(cleanedRow);
          current = '';

          // Skip the comma and any whitespace after this row
          let nextIndex = i + 1;
          while (
            nextIndex < valuesContent.length &&
            /[\s,]/.test(valuesContent[nextIndex] as string)
          ) {
            nextIndex++;
          }
          i = nextIndex - 1; // -1 because the loop will increment
        }
      } else {
        current += char;
      }
    }

    // Add the last row if there's content
    if (current.trim()) {
      const lastRow = current.trim().replace(/^\(|\)$/g, '');

      rows.push(lastRow);
    }

    const filteredRows = rows.filter((row) => row.length > 0);

    return filteredRows;
  }

  protected formatDate(dateStr: string | null): string | null {
    if (!dateStr || dateStr === 'NULL') {
      return null;
    }

    // Remove quotes if present
    const sanitizedDateStr = dateStr.replace(/^'|'$/g, '');

    try {
      // Parse the date string
      const date = new Date(sanitizedDateStr);
      if (Number.isNaN(date.getTime())) {
        console.warn('Invalid date:', sanitizedDateStr);
        return null;
      }

      // Format the date as ISO string
      return date.toISOString();
    } catch (error) {
      console.error('Error parsing date:', sanitizedDateStr, error);
      return null;
    }
  }

  protected async getTableIdMappings(
    tableName: string,
  ): Promise<Record<string, string>> {
    try {
      // Use the in-memory storage
      return await getTableIdMappings(tableName);
    } catch (error) {
      console.error(`Error reading mappings for ${tableName}:`, error);
      throw error;
    }
  }

  // Safe file operation methods for dry-run support
  protected async safeAppendFile(
    filePath: string,
    content: string,
  ): Promise<void> {
    if (this.isDryRun) {
      console.log(`[DRY-RUN] Would append to ${filePath}:`);
      console.log(`[DRY-RUN] Content length: ${content.length} characters`);
      if (content.length > 200) {
        console.log(`[DRY-RUN] Preview: ${content.substring(0, 200)}...`);
      } else {
        console.log(`[DRY-RUN] Content: ${content}`);
      }
      return;
    }

    const fs = await import('node:fs/promises');
    await fs.appendFile(filePath, content);
  }

  protected async safeMkdir(
    dirPath: string,
    options?: MakeDirectoryOptions & {
      recursive: true;
    },
  ): Promise<void> {
    if (this.isDryRun) {
      console.log(`[DRY-RUN] Would create directory: ${dirPath}`);
      return;
    }

    const fs = await import('node:fs/promises');
    await fs.mkdir(dirPath, options);
  }

  protected async writeMappingsToJson<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any> = Record<string, string>,
  >(
    outputDir: string,
    tables: { mysql: string; postgres: string },
    mappings: T,
  ): Promise<void> {
    try {
      if (this.isDryRun) {
        console.log(
          `[DRY-RUN] Would store ID mappings for: ${tables.mysql} -> ${tables.postgres}`,
        );
        console.log(`[DRY-RUN] Mapping count: ${Object.keys(mappings).length}`);
        // Still store in memory for dependency resolution
        await storeTableIdMappings(tables.mysql, mappings);
        return;
      }

      // Store mappings in memory for use by other converters
      await storeTableIdMappings(tables.mysql, mappings);
      console.log(
        `Stored ID mappings for table: ${tables.mysql} -> ${tables.postgres}`,
      );
    } catch (error) {
      console.error(`Error storing mappings for ${tables.mysql}:`, error);
      throw error;
    }
  }

  protected generatePostgresBaseTableInsertWithMappings(
    entities: PostgresI18NBaseReturn[],
    tableName: string,
    header: string,
  ): string {
    let output = this.generateCommentHeader(header);

    // Add the actual INSERT statement
    const values = entities
      .map((entity) => `(${this.formatSqlValue(entity.id)})`)
      .join(',\n');

    output += `INSERT INTO "${tableName}" ("id") VALUES\n${values};\n\n`;

    return output;
  }

  protected generatePostgresI18NInsert(
    records: PostgresI18NDescription[],
    tableName: string,
    header: string,
  ): string {
    let output = this.generateCommentHeader(header);

    const values = records
      .map((record) => {
        const formattedValues =
          `(${this.formatSqlValue(record.id)}, ${this.formatSqlValue(record.data_id)}, ` +
          `${this.formatSqlValue(record.locale)}, ${this.formatSqlValue(record.name)}, ` +
          `${this.formatSqlValue(record.description)})`;
        return formattedValues;
      })
      .join(',\n');

    output += `INSERT INTO "${tableName}" ("id", "data_id", "locale", "name", "description") VALUES\n${values};\n\n`;
    return output;
  }

  protected generatePostgresWithColumnsI18NInsert<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any>,
  >(
    records: T[],
    tableName: string,
    header: string,
    columns: string[],
  ): string {
    let output = this.generateCommentHeader(header);

    // If no records, return just the comment header without INSERT statement
    if (records.length === 0) {
      output += `-- No records to insert for ${tableName}\n\n`;
      return output;
    }

    const values = records
      .map((record) => {
        // Create a string array of values for each record
        const formattedValues = Object.entries(record)
          .map(([, value]) => {
            return this.formatSqlValue(value);
          })
          .join(', ');

        return `(${formattedValues})`;
        // return formattedValues;
      })
      .join(',\n');

    output += `INSERT INTO "${tableName}" (${columns.join(', ')}) VALUES\n${values};\n\n`;
    return output;
  }

  protected generateUpdatePostgresWithColumns<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    T extends Record<string, any>,
  >(
    records: T[],
    tableName: string,
    header: string,
    columns: string[],
  ): string {
    let output = this.generateCommentHeader(header);

    const values = records
      .map((record) => {
        // Create a string array of values for each record
        const formattedValues = Object.entries(record)
          .map(([, value]) => {
            return this.formatSqlValue(value);
          })
          .join(', ');

        return `(${formattedValues})`;
        // return formattedValues;
      })
      .join(',\n');

    output += `UPDATE INTO "${tableName}" (${columns.join(', ')}) VALUES\n${values};\n\n`;
    return output;
  }

  protected parseI18NInsertStatement(
    sqlStatement: string,
    sqlContent: string,
    tableName: string,
  ): MySQLI18NDescription[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      tableName,
    );

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
        description: values.description,
      },
    ];
  }
}
