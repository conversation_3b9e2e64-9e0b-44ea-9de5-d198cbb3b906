import { ConfigLive } from '@rie/config';
import { env as clientEnv } from '../env';
import { Effect } from 'effect';

// Server-side configuration (Effect-based)
export const getServerConfig = () => {
  return Effect.runSync(Effect.provide(ConfigLive, ConfigLive.Default));
};

// Client-side configuration (t3-env based)
export { clientEnv };

// Unified export for convenience
export const config = {
  server: getServerConfig,
  client: clientEnv,
};
