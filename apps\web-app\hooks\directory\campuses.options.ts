import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllCampusesOptions = <
  View extends CollectionViewParamType['view'],
>({
  locale,
  view,
}: CollectionOptionsParams<View>) =>
  getDirectoryListOptions({
    directoryListKey: 'campus' as const,
    locale,
    view,
  });

export const getCampusByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getDirectoryByIdOptions({ directoryListKey: 'campus', id, view } as const);
