import * as Data from 'effect/Data';

export class CampusNotFoundError extends Data.TaggedError(
  'CampusNotFoundError',
)<{ readonly id: string }> {}

export class CampusValidationError extends Data.TaggedError(
  'CampusValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> {}

export class CampusInvariantViolationError extends Data.TaggedError(
  'CampusInvariantViolationError',
)<{
  readonly campusId: string;
  readonly reason: string;
}> {}

export class CampusPersistenceError extends Data.TaggedError(
  'CampusPersistenceError',
)<{
  readonly campusId?: string;
  readonly reason: string;
}> {}
