/**
 * Migration Orchestrator
 *
 * Automatically runs all database migration converter scripts in the correct sequence.
 * This orchestrator discovers and executes all converters from the converters directory
 * in numerical order, ensuring proper dependency resolution.
 */

import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { INPUT_PATH, OUTPUT_PATH } from './constants';
import { clearAllIdMappings, idMappingStorage } from './id-mapping-storage';

/**
 * Interface for converter metadata
 */
interface ConverterInfo {
  filename: string;
  order: string;
  className: string;
  fullPath: string;
}

/**
 * Interface for converter classes that have a convertFile method
 */
interface ConverterClass {
  new (
    dryRun?: boolean,
  ): {
    convertFile?(): Promise<void>;
    updateUnitParents?(inputPath: string, outputPath: string): Promise<void>;
  };
}

/**
 * Migration Orchestrator class that manages the execution of all converters
 */
export class MigrationOrchestrator {
  private readonly convertersDir: string;
  private converters: ConverterInfo[] = [];
  private readonly isDryRun;

  constructor(dryRun = false) {
    this.convertersDir = path.join(__dirname, 'converters');
    this.isDryRun = dryRun;
  }

  /**
   * Discover all converter files in the converters directory
   */
  private async discoverConverters(): Promise<void> {
    console.log('🔍 Discovering converter files...');

    try {
      const files = await fs.readdir(this.convertersDir);

      // Filter and sort converter files
      const converterFiles = files
        .filter((file) => file.endsWith('.ts') && file.match(/^\d{2}-\d{2}-/))
        .sort(); // Natural sort will work for our XX-YY pattern

      console.log(`📁 Found ${converterFiles.length} converter files`);

      // Extract metadata for each converter
      for (const filename of converterFiles) {
        const fullPath = path.join(this.convertersDir, filename);
        const order = filename.substring(0, 5); // Extract XX-YY
        const className = await this.extractClassName(fullPath);

        if (className) {
          this.converters.push({
            filename,
            order,
            className,
            fullPath,
          });
          console.log(`  ✓ ${order}: ${className} (${filename})`);
        } else {
          console.warn(`  ⚠️  Could not extract class name from ${filename}`);
        }
      }

      console.log(
        `✅ Successfully discovered ${this.converters.length} converters\n`,
      );
    } catch (error) {
      throw new Error(`Failed to discover converters: ${error}`);
    }
  }

  /**
   * Extract the class name from a converter file
   */
  private async extractClassName(filePath: string): Promise<string | null> {
    try {
      const content = await fs.readFile(filePath, 'utf8');

      // Look for export class declarations
      const classMatch = content.match(/export\s+class\s+(\w+)/);

      if (classMatch?.[1]) {
        return classMatch[1];
      }

      return null;
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Dynamically import and instantiate a converter class
   */
  private async loadConverter(
    converterInfo: ConverterInfo,
  ): Promise<ConverterClass> {
    try {
      // Import the module dynamically
      const module = await import(converterInfo.fullPath);

      // Get the class from the module
      const ConverterClass = module[converterInfo.className];

      if (!ConverterClass) {
        throw new Error(
          `Class ${converterInfo.className} not found in ${converterInfo.filename}`,
        );
      }

      return ConverterClass as ConverterClass;
    } catch (error) {
      throw new Error(
        `Failed to load converter ${converterInfo.className}: ${error}`,
      );
    }
  }

  /**
   * Execute a single converter
   */
  private async executeConverter(converterInfo: ConverterInfo): Promise<void> {
    const startTime = Date.now();

    const dryRunPrefix = this.isDryRun ? '[DRY-RUN] ' : '';
    try {
      console.log(
        `🔄 [${converterInfo.order}] ${dryRunPrefix}Starting ${converterInfo.className}...`,
      );

      // Load and instantiate the converter with dry-run flag
      const ConverterClass = await this.loadConverter(converterInfo);
      const converter = new ConverterClass(this.isDryRun);

      // Always execute the converter methods - dry-run logic is handled internally by BaseConverter
      if (converter.convertFile) {
        await converter.convertFile();
      } else if (converter.updateUnitParents) {
        await converter.updateUnitParents(INPUT_PATH, OUTPUT_PATH);
      } else {
        throw new Error(
          `Converter ${converterInfo.className} does not have a convertFile or updateUnitParents method`,
        );
      }

      const duration = Date.now() - startTime;
      console.log(
        `✅ [${converterInfo.order}] ${dryRunPrefix}Completed ${converterInfo.className} (${duration}ms)\n`,
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(
        `❌ [${converterInfo.order}] ${dryRunPrefix}Failed ${converterInfo.className} (${duration}ms)`,
      );
      console.error(`   Error: ${error}`);
      throw new Error(`Converter ${converterInfo.className} failed: ${error}`);
    }
  }

  /**
   * Validate that input files exist and are readable
   */
  private async validateInputFiles(): Promise<void> {
    try {
      await fs.access(INPUT_PATH, fs.constants.R_OK);
      console.log(`✅ Input file accessible: ${INPUT_PATH}`);
    } catch (error) {
      console.log(`❌ Input file not accessible: ${INPUT_PATH}`);
      throw new Error(`Input file validation failed: ${error}`);
    }
  }

  /**
   * Clear the output file to start fresh
   */
  private async clearOutputFile(): Promise<void> {
    const dryRunPrefix = this.isDryRun ? '[DRY-RUN] ' : '';

    try {
      if (this.isDryRun) {
        console.log(
          `🧹 ${dryRunPrefix}Would clear output file: ${OUTPUT_PATH}`,
        );
        return;
      }

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(OUTPUT_PATH);
      await fs.mkdir(outputDir, { recursive: true });

      // Clear the output file by writing an empty string
      await fs.writeFile(OUTPUT_PATH, '');
      console.log(`🧹 ${dryRunPrefix}Output file cleared: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error(
        `❌ ${dryRunPrefix}Failed to clear output file: ${OUTPUT_PATH}`,
      );
      throw new Error(`Output file clearing failed: ${error}`);
    }
  }

  /**
   * Execute all converters in sequence
   */
  private async executeAllConverters(): Promise<void> {
    const dryRunPrefix = this.isDryRun ? '[DRY-RUN] ' : '';
    console.log(
      `🚀 ${dryRunPrefix}Starting migration of ${this.converters.length} converters...\n`,
    );

    const totalStartTime = Date.now();
    let completedCount = 0;

    try {
      for (const converterInfo of this.converters) {
        await this.executeConverter(converterInfo);
        completedCount++;

        // Show progress
        console.log(
          `📊 ${dryRunPrefix}Progress: ${completedCount}/${this.converters.length} converters completed\n`,
        );
      }

      const totalDuration = Date.now() - totalStartTime;
      console.log(
        `🎉 ${dryRunPrefix}All ${this.converters.length} converters completed successfully!`,
      );
      console.log(
        `⏱️  Total execution time: ${(totalDuration / 1000).toFixed(2)}s\n`,
      );
    } catch (error) {
      const totalDuration = Date.now() - totalStartTime;
      console.error(
        `💥 Migration failed after ${completedCount}/${this.converters.length} converters`,
      );
      console.error(
        `⏱️  Execution time before failure: ${(totalDuration / 1000).toFixed(2)}s`,
      );
      throw error;
    }
  }

  /**
   * Display final statistics about the migration
   */
  private async displayFinalStats(): Promise<void> {
    try {
      const tableCount = await idMappingStorage.getTableCount();
      const allMappings = await idMappingStorage.getAllMappings();

      let totalMappings = 0;
      for (const mappings of Object.values(allMappings)) {
        totalMappings += Object.keys(mappings).length;
      }

      const dryRunPrefix = this.isDryRun ? '[DRY-RUN] ' : '';
      console.log(`📈 ${dryRunPrefix}Migration Statistics:`);
      console.log(`   • Tables with ID mappings: ${tableCount}`);
      console.log(`   • Total ID mappings created: ${totalMappings}`);
      console.log(`   • Converters executed: ${this.converters.length}`);

      if (this.isDryRun) {
        console.log('   • Files actually modified: 0');
        console.log('   • ID mappings stored in memory for validation');
      }

      if (tableCount > 0) {
        console.log(`\n📋 ${dryRunPrefix}Tables with mappings:`);
        for (const [tableName, mappings] of Object.entries(allMappings)) {
          const count = Object.keys(mappings).length;
          console.log(`   • ${tableName}: ${count} mappings`);
        }
      }
    } catch (error) {
      console.warn('⚠️  Could not retrieve final statistics:', error);
    }
  }

  /**
   * Run the complete migration process
   */
  public async run(): Promise<void> {
    const overallStartTime = Date.now();

    try {
      if (this.isDryRun) {
        console.log('🔍 DRY-RUN MODE: No files will be modified');
        console.log('=====================================\n');
      }

      console.log('🎯 Database Migration Orchestrator Starting...\n');

      // Validate input files
      await this.validateInputFiles();
      console.log('');

      // Clear the output file to start fresh
      await this.clearOutputFile();
      console.log('');

      // Clear any existing ID mappings to start fresh
      const dryRunPrefix = this.isDryRun ? '[DRY-RUN] ' : '';
      console.log(`🧹 ${dryRunPrefix}Clearing existing ID mappings...`);
      if (!this.isDryRun) {
        await clearAllIdMappings();
      }
      console.log(`✅ ${dryRunPrefix}ID mappings cleared\n`);

      // Discover all converters
      await this.discoverConverters();

      // Execute all converters in sequence
      await this.executeAllConverters();

      // Display final statistics
      await this.displayFinalStats();

      const overallDuration = Date.now() - overallStartTime;
      console.log(
        `\n🏁 ${dryRunPrefix}Migration orchestration completed successfully!`,
      );
      console.log(
        `⏱️  Total orchestration time: ${(overallDuration / 1000).toFixed(2)}s`,
      );
    } catch (error) {
      const overallDuration = Date.now() - overallStartTime;
      console.error('\n💥 Migration orchestration failed!');
      console.error(
        `⏱️  Time before failure: ${(overallDuration / 1000).toFixed(2)}s`,
      );
      console.error(`❌ Error: ${error}`);

      // Still try to show some stats if possible
      try {
        await this.displayFinalStats();
      } catch (statsError) {
        console.warn(
          'Could not display final statistics due to error:',
          statsError,
        );
      }

      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const isDryRun = args.includes('--dry-run');

  if (isDryRun) {
    console.log('🔍 Dry-run mode enabled via --dry-run flag\n');
  }

  const orchestrator = new MigrationOrchestrator(isDryRun);

  try {
    await orchestrator.run();
    process.exit(0);
  } catch (error) {
    console.error('\n🚨 Migration orchestration terminated with errors');
    process.exit(1);
  }
}

// Run the orchestrator if this file is executed directly
if (require.main === module) {
  main();
}
