# Testing Guide

This guide covers the comprehensive testing strategy for the RIE project, including unit tests, integration tests, and end-to-end tests.

## 🎯 Testing Philosophy

### Testing Pyramid

```
        /\
       /  \
      / E2E \     ← Few, high-value user journey tests
     /______\
    /        \
   /Integration\ ← More tests, API endpoints, database
  /__________\
 /            \
/    Unit      \ ← Most tests, pure functions, components
/______________\
```

### Testing Principles

1. **Test Behavior, Not Implementation**: Focus on what the code does, not how it does it
2. **Write Tests First**: TDD approach for critical business logic
3. **Fast Feedback**: Unit tests should run quickly
4. **Reliable**: Tests should be deterministic and not flaky
5. **Maintainable**: Tests should be easy to understand and update

## 🛠️ Testing Stack

### Frontend Testing

- **Unit/Integration**: Vitest + React Testing Library
- **Component Development**: Storybook
- **E2E**: Playwright
- **Mocking**: MSW (Mock Service Worker)

### Backend Testing

- **Unit/Integration**: Vitest
- **API Testing**: Supertest-like testing with Hono
- **Database**: In-memory SQLite for fast tests
- **Mocking**: Effect-ts test utilities

## 🧪 Unit Testing

### Frontend Unit Tests

#### Testing Components

```typescript
// components/ui/__tests__/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })

  it('calls onClick handler when clicked', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies correct variant classes', () => {
    render(<Button variant="destructive">Delete</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
  })
})
```

#### Testing Custom Hooks

```typescript
// lib/hooks/__tests__/use-equipment.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useEquipment } from '../use-equipment'
import { equipmentApi } from '@/lib/api/equipment'

// Mock the API
vi.mock('@/lib/api/equipment')
const mockEquipmentApi = vi.mocked(equipmentApi)

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useEquipment', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('fetches equipment successfully', async () => {
    const mockData = [
      { id: '1', name: 'Microscope', type: 'MICROSCOPE' },
      { id: '2', name: 'Centrifuge', type: 'CENTRIFUGE' },
    ]
    
    mockEquipmentApi.getAll.mockResolvedValue(mockData)

    const { result } = renderHook(() => useEquipment(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isLoading).toBe(true)

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(result.current.data).toEqual(mockData)
    expect(mockEquipmentApi.getAll).toHaveBeenCalledTimes(1)
  })

  it('handles error state', async () => {
    const error = new Error('Failed to fetch')
    mockEquipmentApi.getAll.mockRejectedValue(error)

    const { result } = renderHook(() => useEquipment(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    expect(result.current.error).toEqual(error)
  })
})
```

#### Testing Utilities

```typescript
// lib/utils/__tests__/format-name.test.ts
import { describe, it, expect } from 'vitest'
import { formatName } from '../format-name'

describe('formatName', () => {
  it('formats name in first-last order by default', () => {
    expect(formatName('John', 'Doe')).toBe('John Doe')
  })

  it('formats name in last-first order when specified', () => {
    expect(formatName('John', 'Doe', { lastNameFirst: true })).toBe('Doe, John')
  })

  it('handles empty first name', () => {
    expect(formatName('', 'Doe')).toBe('Doe')
  })

  it('handles empty last name', () => {
    expect(formatName('John', '')).toBe('John')
  })

  it('handles both names empty', () => {
    expect(formatName('', '')).toBe('')
  })
})
```

### Backend Unit Tests

#### Testing Domain Models

```typescript
// packages/domain/src/__tests__/equipment.test.ts
import { describe, it, expect } from 'vitest'
import { Equipment, EquipmentStatus } from '../models/equipment'

describe('Equipment', () => {
  it('creates equipment with valid data', () => {
    const equipment = Equipment.make({
      id: '1',
      name: 'Microscope',
      type: 'MICROSCOPE',
      status: EquipmentStatus.Available,
      location: 'Lab A',
      createdAt: new Date(),
    })

    expect(equipment.name).toBe('Microscope')
    expect(equipment.isAvailable()).toBe(true)
  })

  it('determines availability correctly', () => {
    const availableEquipment = Equipment.make({
      id: '1',
      name: 'Microscope',
      type: 'MICROSCOPE',
      status: EquipmentStatus.Available,
      location: 'Lab A',
      createdAt: new Date(),
    })

    const inUseEquipment = Equipment.make({
      id: '2',
      name: 'Centrifuge',
      type: 'CENTRIFUGE',
      status: EquipmentStatus.InUse,
      location: 'Lab B',
      createdAt: new Date(),
    })

    expect(availableEquipment.isAvailable()).toBe(true)
    expect(inUseEquipment.isAvailable()).toBe(false)
  })
})
```

#### Testing Use Cases

```typescript
// apps/api/src/application/use-cases/__tests__/create-equipment.test.ts
import { describe, it, expect, vi } from 'vitest'
import { Effect, Layer } from 'effect'
import { createEquipment } from '../equipment/create-equipment'
import { EquipmentRepository } from '../../ports/repositories/equipment.repository'
import { PermissionService } from '../../ports/services/permission.service'
import { Equipment } from '@rie/domain'

describe('createEquipment', () => {
  const mockEquipmentRepository: EquipmentRepository = {
    create: vi.fn(),
    findById: vi.fn(),
    findAll: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  }

  const mockPermissionService: PermissionService = {
    canCreateEquipment: vi.fn(),
    canUpdateEquipment: vi.fn(),
    canDeleteEquipment: vi.fn(),
  }

  const mockUser = { id: 'user-1', email: '<EMAIL>' }

  const testLayer = Layer.succeed({
    equipmentRepository: mockEquipmentRepository,
    permissionService: mockPermissionService,
    user: mockUser,
  })

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('creates equipment successfully', async () => {
    const equipmentData = {
      name: 'Test Microscope',
      type: 'MICROSCOPE' as const,
      location: 'Lab A',
    }

    const createdEquipment = Equipment.make({
      id: 'equipment-1',
      ...equipmentData,
      status: 'AVAILABLE' as const,
      createdAt: new Date(),
    })

    vi.mocked(mockPermissionService.canCreateEquipment).mockReturnValue(
      Effect.succeed(true)
    )
    vi.mocked(mockEquipmentRepository.create).mockReturnValue(
      Effect.succeed(createdEquipment)
    )

    const result = await Effect.runPromise(
      createEquipment(equipmentData).pipe(
        Effect.provide(testLayer)
      )
    )

    expect(result).toEqual(createdEquipment)
    expect(mockPermissionService.canCreateEquipment).toHaveBeenCalledWith('user-1')
    expect(mockEquipmentRepository.create).toHaveBeenCalledWith(equipmentData)
  })

  it('fails when user lacks permission', async () => {
    const equipmentData = {
      name: 'Test Microscope',
      type: 'MICROSCOPE' as const,
      location: 'Lab A',
    }

    vi.mocked(mockPermissionService.canCreateEquipment).mockReturnValue(
      Effect.succeed(false)
    )

    await expect(
      Effect.runPromise(
        createEquipment(equipmentData).pipe(
          Effect.provide(testLayer)
        )
      )
    ).rejects.toThrow('Unauthorized')

    expect(mockEquipmentRepository.create).not.toHaveBeenCalled()
  })
})
```

## 🔗 Integration Testing

### API Integration Tests

```typescript
// apps/api/src/api/v2/routes/__tests__/equipment.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { app } from '../../../app'
import { testDb, clearDatabase, seedTestData } from '../../../test-utils/database'

describe('Equipment API', () => {
  beforeEach(async () => {
    await clearDatabase()
    await seedTestData()
  })

  afterEach(async () => {
    await clearDatabase()
  })

  describe('GET /api/v2/equipment', () => {
    it('returns list of equipment', async () => {
      const response = await app.request('/api/v2/equipment', {
        headers: {
          'Authorization': 'Bearer valid-test-token',
        },
      })

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data.length).toBeGreaterThan(0)
    })

    it('requires authentication', async () => {
      const response = await app.request('/api/v2/equipment')
      expect(response.status).toBe(401)
    })
  })

  describe('POST /api/v2/equipment', () => {
    it('creates new equipment', async () => {
      const equipmentData = {
        name: 'Test Microscope',
        type: 'MICROSCOPE',
        location: 'Lab A',
      }

      const response = await app.request('/api/v2/equipment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer valid-test-token',
        },
        body: JSON.stringify(equipmentData),
      })

      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data).toMatchObject(equipmentData)
      expect(data.id).toBeDefined()
    })

    it('validates required fields', async () => {
      const response = await app.request('/api/v2/equipment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer valid-test-token',
        },
        body: JSON.stringify({}),
      })

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error).toBeDefined()
    })
  })
})
```

### Database Integration Tests

```typescript
// apps/api/src/infrastructure/repositories/__tests__/equipment.repository.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { Effect } from 'effect'
import { EquipmentRepositoryImpl } from '../equipment.repository.impl'
import { testDb, clearDatabase } from '../../../test-utils/database'

describe('EquipmentRepository', () => {
  let repository: EquipmentRepositoryImpl

  beforeEach(async () => {
    await clearDatabase()
    repository = new EquipmentRepositoryImpl(testDb)
  })

  afterEach(async () => {
    await clearDatabase()
  })

  it('creates and retrieves equipment', async () => {
    const equipmentData = {
      name: 'Test Microscope',
      type: 'MICROSCOPE' as const,
      location: 'Lab A',
    }

    // Create equipment
    const created = await Effect.runPromise(
      repository.create(equipmentData)
    )

    expect(created.name).toBe(equipmentData.name)
    expect(created.id).toBeDefined()

    // Retrieve equipment
    const retrieved = await Effect.runPromise(
      repository.findById(created.id)
    )

    expect(retrieved).toEqual(created)
  })

  it('returns null for non-existent equipment', async () => {
    const result = await Effect.runPromise(
      repository.findById('non-existent-id')
    )

    expect(result).toBeNull()
  })

  it('filters equipment by type', async () => {
    // Create different types of equipment
    await Effect.runPromise(
      repository.create({
        name: 'Microscope',
        type: 'MICROSCOPE',
        location: 'Lab A',
      })
    )

    await Effect.runPromise(
      repository.create({
        name: 'Centrifuge',
        type: 'CENTRIFUGE',
        location: 'Lab B',
      })
    )

    // Filter by type
    const microscopes = await Effect.runPromise(
      repository.findAll({ type: 'MICROSCOPE' })
    )

    expect(microscopes).toHaveLength(1)
    expect(microscopes[0].type).toBe('MICROSCOPE')
  })
})
```

## 🎭 End-to-End Testing

### Playwright E2E Tests

```typescript
// apps/web-app-e2e/tests/equipment.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Equipment Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('should display equipment list', async ({ page }) => {
    await page.goto('/equipment')
    
    // Check page title
    await expect(page.locator('h1')).toContainText('Equipment')
    
    // Check table is visible
    await expect(page.locator('[data-testid="equipment-table"]')).toBeVisible()
    
    // Check at least one row exists
    await expect(page.locator('tbody tr')).toHaveCount({ min: 1 })
  })

  test('should create new equipment', async ({ page }) => {
    await page.goto('/equipment')
    
    // Click add button
    await page.click('[data-testid="add-equipment-button"]')
    
    // Fill form
    await page.fill('[data-testid="equipment-name"]', 'Test Microscope')
    await page.selectOption('[data-testid="equipment-type"]', 'MICROSCOPE')
    await page.fill('[data-testid="equipment-location"]', 'Lab A')
    
    // Submit form
    await page.click('[data-testid="submit-button"]')
    
    // Check success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    
    // Check equipment appears in list
    await expect(page.locator('text=Test Microscope')).toBeVisible()
  })

  test('should edit equipment', async ({ page }) => {
    await page.goto('/equipment')
    
    // Click edit button for first equipment
    await page.click('[data-testid="equipment-row"]:first-child [data-testid="edit-button"]')
    
    // Update name
    await page.fill('[data-testid="equipment-name"]', 'Updated Microscope')
    
    // Submit form
    await page.click('[data-testid="submit-button"]')
    
    // Check success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    
    // Check updated name appears
    await expect(page.locator('text=Updated Microscope')).toBeVisible()
  })

  test('should delete equipment', async ({ page }) => {
    await page.goto('/equipment')
    
    // Get initial count
    const initialCount = await page.locator('tbody tr').count()
    
    // Click delete button for first equipment
    await page.click('[data-testid="equipment-row"]:first-child [data-testid="delete-button"]')
    
    // Confirm deletion
    await page.click('[data-testid="confirm-delete"]')
    
    // Check success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    
    // Check count decreased
    await expect(page.locator('tbody tr')).toHaveCount(initialCount - 1)
  })

  test('should filter equipment', async ({ page }) => {
    await page.goto('/equipment')
    
    // Apply type filter
    await page.selectOption('[data-testid="type-filter"]', 'MICROSCOPE')
    
    // Check only microscopes are shown
    const rows = page.locator('tbody tr')
    const count = await rows.count()
    
    for (let i = 0; i < count; i++) {
      await expect(rows.nth(i).locator('[data-testid="equipment-type"]')).toContainText('MICROSCOPE')
    }
  })
})
```

### Visual Testing with Playwright

```typescript
// apps/web-app-e2e/tests/visual.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Visual Tests', () => {
  test('equipment page visual regression', async ({ page }) => {
    await page.goto('/equipment')
    
    // Wait for content to load
    await page.waitForSelector('[data-testid="equipment-table"]')
    
    // Take screenshot
    await expect(page).toHaveScreenshot('equipment-page.png')
  })

  test('equipment form visual regression', async ({ page }) => {
    await page.goto('/equipment/new')
    
    // Wait for form to load
    await page.waitForSelector('[data-testid="equipment-form"]')
    
    // Take screenshot
    await expect(page).toHaveScreenshot('equipment-form.png')
  })
})
```

## 🎨 Component Testing with Storybook

### Writing Stories

```typescript
// components/features/equipment/equipment-card.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { EquipmentCard } from './equipment-card'

const meta: Meta<typeof EquipmentCard> = {
  title: 'Features/Equipment/EquipmentCard',
  component: EquipmentCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

const mockEquipment = {
  id: '1',
  name: 'Advanced Microscope',
  type: 'MICROSCOPE' as const,
  status: 'AVAILABLE' as const,
  location: 'Lab A',
  createdAt: new Date('2024-01-01'),
}

export const Default: Story = {
  args: {
    equipment: mockEquipment,
  },
}

export const InUse: Story = {
  args: {
    equipment: {
      ...mockEquipment,
      status: 'IN_USE' as const,
    },
  },
}

export const Maintenance: Story = {
  args: {
    equipment: {
      ...mockEquipment,
      status: 'MAINTENANCE' as const,
    },
  },
}

export const LongName: Story = {
  args: {
    equipment: {
      ...mockEquipment,
      name: 'Very Long Equipment Name That Should Wrap Properly',
    },
  },
}
```

### Interaction Testing

```typescript
// components/features/equipment/equipment-form.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { userEvent, within, expect } from '@storybook/test'
import { EquipmentForm } from './equipment-form'

const meta: Meta<typeof EquipmentForm> = {
  title: 'Features/Equipment/EquipmentForm',
  component: EquipmentForm,
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const FilledForm: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    
    // Fill out the form
    await userEvent.type(canvas.getByLabelText(/name/i), 'Test Microscope')
    await userEvent.selectOptions(canvas.getByLabelText(/type/i), 'MICROSCOPE')
    await userEvent.type(canvas.getByLabelText(/location/i), 'Lab A')
    
    // Check form is filled
    await expect(canvas.getByDisplayValue('Test Microscope')).toBeInTheDocument()
    await expect(canvas.getByDisplayValue('MICROSCOPE')).toBeInTheDocument()
    await expect(canvas.getByDisplayValue('Lab A')).toBeInTheDocument()
  },
}

export const ValidationErrors: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    
    // Try to submit empty form
    await userEvent.click(canvas.getByRole('button', { name: /create/i }))
    
    // Check validation errors appear
    await expect(canvas.getByText(/name is required/i)).toBeInTheDocument()
    await expect(canvas.getByText(/location is required/i)).toBeInTheDocument()
  },
}
```

## 🔧 Test Configuration

### Vitest Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test-utils/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test-utils/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

### Test Setup

```typescript
// src/test-utils/setup.ts
import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock next-intl
vi.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
  useLocale: () => 'en',
}))

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))
```

### Test Utilities

```typescript
// src/test-utils/render.tsx
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactElement } from 'react'

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Re-export everything
export * from '@testing-library/react'
export { renderWithProviders as render }
```

## 🚀 Running Tests

### Development Commands

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run specific test file
pnpm test equipment.test.ts

# Run E2E tests
cd apps/web-app-e2e
pnpm e2e:test

# Run E2E tests headlessly
pnpm e2e:headless

# Run Storybook tests
pnpm test-storybook
```

### CI/CD Integration

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run unit tests
        run: pnpm test:coverage
      
      - name: Run E2E tests
        run: pnpm e2e:ci
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 📊 Test Coverage

### Coverage Goals

- **Unit Tests**: 80%+ coverage for business logic
- **Integration Tests**: Cover all API endpoints
- **E2E Tests**: Cover critical user journeys

### Monitoring Coverage

```bash
# Generate coverage report
pnpm test:coverage

# View coverage report
open coverage/index.html
```

## 🆘 Troubleshooting

### Common Issues

#### Tests Timing Out

- Increase timeout in test configuration
- Use `waitFor` for async operations
- Mock slow external dependencies

#### Flaky Tests

- Avoid testing implementation details
- Use proper async/await patterns
- Mock time-dependent code

#### Memory Leaks in Tests

- Clear mocks between tests
- Properly cleanup event listeners
- Use `afterEach` hooks for cleanup

---

**Next**: [Deployment Guide](./deployment.md) | [Troubleshooting Guide](./troubleshooting.md)
