import {
    type BuildingEditResponseDTO,
    BuildingEditResponseDtoSchema,
} from '@rie/api-contracts';
import { DbBuildingSchema } from '@rie/domain/schemas';
import type { DbBuilding } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes database building data into the shape required for an edit form.
 *
 * This function transforms raw database building data (including all translations)
 * into the format expected by the frontend edit form.
 */
export function serializeBuildingToFormEdit(
    buildingData: DbBuilding,
): Effect.Effect<BuildingEditResponseDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbBuildingSchema,
        BuildingEditResponseDtoSchema,
        {
            strict: true,
            decode: (data, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // Transform database translations to the format expected by the edit form
                        const transformedTranslations = data.translations.map(
                            (translation) => ({
                                name: {
                                    locale: translation.locale,
                                    value: translation.name,
                                },
                                alias: {
                                    locale: translation.locale,
                                    value: translation.otherNames,
                                },
                            }),
                        );

                        return {
                            id: data.id,
                            isActive: data.isActive ?? true,
                            sadId: data.sadId ?? null,
                            campusId: data.campusId ?? null,
                            translations: transformedTranslations,
                        };
                    },
                    catch(error) {
                        return new ParseResult.Type(
                            ast,
                            data,
                            error instanceof Error
                                ? error.message
                                : 'Failed to serialize building to edit form',
                        );
                    },
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return Schema.decode(transformer)(buildingData);
}

/**
 * Serializes a building's raw data into the client edit form shape expected by the frontend.
 * Includes resolved campus and jurisdiction select options with value/label.
 */
export function serializeBuildingToEditClient(
    buildingData: DbBuilding,
    campusLabel: string | null,
    jurisdictionLabel: string | null,
    jurisdictionId: string | null,
): Effect.Effect<
    {
        id: string;
        name: Array<{ locale: string; value: string }>;
        alias: Array<{ locale: string; value: string }>;
        campus: { value: string | null; label: string | null };
        jurisdiction: { value: string | null; label: string | null };
        civicAddresses: Array<{
            city: string;
            countryCode: string;
            fullAddress: string;
            lat: string;
            lon: string;
            placeId: string;
            postalCode: string;
            state: string;
            streetName: string;
            streetNumber: string;
        }>;
    },
    never
> {
    return Effect.succeed({
        id: buildingData.id,
        name: buildingData.translations.map((translation) => ({
            locale: translation.locale,
            value: translation.name ?? '',
        })),
        alias: buildingData.translations.map((translation) => ({
            locale: translation.locale,
            value: translation.otherNames ?? '',
        })),
        campus: {
            value: buildingData.campusId ?? null,
            label: campusLabel && campusLabel.trim() !== '' ? campusLabel : buildingData.campusId ?? null,
        },
        jurisdiction: {
            value: jurisdictionId,
            label: jurisdictionLabel && jurisdictionLabel.trim() !== '' ? jurisdictionLabel : jurisdictionId,
        },
        civicAddresses: [], // TODO: Add civic addresses when available
    });
}