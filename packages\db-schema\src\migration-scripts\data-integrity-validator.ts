import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from './constants';
import { BaseConverter } from './converters/base-converter';

interface TableValidationResult {
  tableName: string;
  mysqlRecords: number;
  postgresRecords: number;
  match: boolean;
  status: 'PASS' | 'FAIL' | 'WARNING';
}

export class DataIntegrityValidator extends BaseConverter {
  async validateDataIntegrity(): Promise<TableValidationResult[]> {
    console.log('🔍 Starting data integrity validation...');
    const mysqlContent = await fs.readFile(INPUT_PATH, 'utf8');
    const postgresContent = await fs.readFile(OUTPUT_PATH, 'utf8');

    // Extract all table names from MySQL dump
    const tableNames = this.extractAllTableNames(mysqlContent);
    const results: TableValidationResult[] = [];

    console.log(`📊 Found ${tableNames.length} tables to validate`);

    for (const tableName of tableNames) {
      console.log(`  Validating table: ${tableName}...`);

      const mysqlCount = this.countMySQLRecords(mysqlContent, tableName);
      const postgresCount = this.countPostgreSQLRecords(
        postgresContent,
        tableName,
      );

      const status =
        mysqlCount === postgresCount
          ? 'PASS'
          : mysqlCount === 0
            ? 'WARNING'
            : 'FAIL';

      results.push({
        tableName,
        mysqlRecords: mysqlCount,
        postgresRecords: postgresCount,
        match: mysqlCount === postgresCount,
        status,
      });
    }

    return results;
  }

  async validateSpecificTables(
    tableNames: string[],
  ): Promise<TableValidationResult[]> {
    console.log(`🎯 Validating specific tables: ${tableNames.join(', ')}`);
    const mysqlContent = await fs.readFile(INPUT_PATH, 'utf8');
    const postgresContent = await fs.readFile(OUTPUT_PATH, 'utf8');
    const results: TableValidationResult[] = [];

    for (const tableName of tableNames) {
      const mysqlCount = this.countMySQLRecords(mysqlContent, tableName);
      const postgresCount = this.countPostgreSQLRecords(
        postgresContent,
        tableName,
      );

      results.push({
        tableName,
        mysqlRecords: mysqlCount,
        postgresRecords: postgresCount,
        match: mysqlCount === postgresCount,
        status: mysqlCount === postgresCount ? 'PASS' : 'FAIL',
      });
    }

    return results;
  }

  printValidationReport(results: TableValidationResult[]): void {
    console.log('\n📋 DATA INTEGRITY VALIDATION REPORT');
    console.log('=====================================');

    const passed = results.filter((r) => r.status === 'PASS').length;
    const failed = results.filter((r) => r.status === 'FAIL').length;
    const warnings = results.filter((r) => r.status === 'WARNING').length;

    console.log(`✅ PASSED: ${passed}`);
    console.log(`❌ FAILED: ${failed}`);
    console.log(`⚠️  WARNINGS: ${warnings}`);
    console.log(`📊 TOTAL: ${results.length}\n`);

    // Show detailed results
    for (const result of results) {
      const icon =
        result.status === 'PASS'
          ? '✅'
          : result.status === 'WARNING'
            ? '⚠️'
            : '❌';

      console.log(
        `${icon} ${result.tableName.padEnd(25)} | MySQL: ${result.mysqlRecords.toString().padStart(6)} | PostgreSQL: ${result.postgresRecords.toString().padStart(6)} | ${result.status}`,
      );
    }

    if (failed > 0) {
      console.log('\n🚨 CRITICAL ISSUES FOUND:');
      // biome-ignore lint/complexity/noForEach: <explanation>
      results
        .filter((r) => r.status === 'FAIL')
        .forEach((result) => {
          console.log(
            `   - ${result.tableName}: Expected ${result.mysqlRecords} records, got ${result.postgresRecords}`,
          );
        });
    }
  }

  private extractAllTableNames(sqlContent: string): string[] {
    const createTableRegex = /CREATE\s+TABLE\s+(?:`|")?(\w+)(?:`|")?/gim;
    const matches = sqlContent.match(createTableRegex) || [];
    return matches
      .map((match) => {
        const nameMatch = match.match(
          /CREATE\s+TABLE\s+(?:`|")?(\w+)(?:`|")?/i,
        );
        return nameMatch?.[1] || '';
      })
      .filter((name) => name.length > 0);
  }

  private countMySQLRecords(sqlContent: string, tableName: string): number {
    const insertStatements = this.extractInsertStatements(
      sqlContent,
      tableName,
    );
    let totalRecords = 0;

    for (const statement of insertStatements) {
      const valuesMatch = statement.match(/VALUES\s*(.+?);$/i);
      if (valuesMatch) {
        const rowCount = this.splitValueRows(valuesMatch[1] as string).length;
        totalRecords += rowCount;
      }
    }

    return totalRecords;
  }

  private countPostgreSQLRecords(
    sqlContent: string,
    tableName: string,
  ): number {
    // Count INSERT statements for PostgreSQL tables
    const insertRegex = new RegExp(`INSERT\\s+INTO\\s+"${tableName}"`, 'gim');
    const matches = sqlContent.match(insertRegex) || [];
    return matches.length;
  }
}

// Main execution function
async function runValidation() {
  try {
    const validator = new DataIntegrityValidator();

    // Check if we should validate specific tables or all tables
    const args = process.argv.slice(2);
    let results: TableValidationResult[];

    if (args.length > 0 && args[0] !== '--all') {
      // Validate specific tables
      results = await validator.validateSpecificTables(args);
    } else {
      // Validate all tables
      results = await validator.validateDataIntegrity();
    }

    // Print the report
    validator.printValidationReport(results);

    // Exit with error code if there are failures
    const failures = results.filter((r) => r.status === 'FAIL').length;
    if (failures > 0) {
      console.log(
        `\n💥 Validation failed with ${failures} table(s) having data mismatches.`,
      );
      process.exit(1);
    } else {
      console.log('\n🎉 All validations passed! Data integrity confirmed.');
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Validation failed with error:', error);
    process.exit(1);
  }
}

// Run validation if this file is executed directly
const isMainModule =
  process.argv[1]?.includes('data-integrity-validator.ts') ||
  process.argv[1]?.includes('data-integrity-validator');

if (isMainModule) {
  runValidation();
}
