'use client';

import { <PERSON><PERSON><PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { TableActionButtons } from '@/components/table-action-buttons/table-action-buttons';
import dayjs from '@/lib/dayjs';
import type { SupportedLocale } from '@/types/locale';
import { Badge } from '@/ui/badge';
import type { PermissionDetail } from '@rie/domain/types';
import type { ColumnDef } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';

const namespace = 'permissions';
export const permissionsColumns = (
  locale: SupportedLocale,
): ColumnDef<PermissionDetail>[] => {
  const tPermissions = useTranslations('permissions');

  return [
    {
      accessorKey: 'domain',
      cell: (cell) => tPermissions(`domains.${cell.getValue()}`),
      enableHiding: false,
      enablePinning: true,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'domain',
      size: 280,
    },
    {
      accessorKey: 'action',
      cell: ({ row }) => {
        return <Badge>{row.original.action}</Badge>;
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'action',
      size: 280,
    },
    {
      accessorKey: 'createdAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'createdAt',
      maxSize: 100,
    },
    {
      accessorKey: 'updatedAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'updatedAt',
      maxSize: 100,
    },
    {
      accessorKey: 'permissionActions',
      cell: ({ row }) => (
        <TableActionButtons
          id={row.original.id}
          name={`${row.original.domain}:${row.original.action}`}
          pathname="/admin/permissions/[id]/editer"
        />
      ),
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={namespace}
          translationKey="table.columns.actions"
        />
      ),
      id: 'permissionActions',
      size: 80,
      maxSize: 80,
    },
  ];
};
