'use client';

import { BaseTable } from '@/components/base-table/base-table';
import { DataTable } from '@/components/data-table/data-table';
import type {
  ColumnDef,
  ColumnPinningState,
  VisibilityState,
} from '@tanstack/react-table';
import { useCallback, useState } from 'react';

type PermissionsTableProps<T extends object, C extends object> = {
  columns: ColumnDef<T, C>[];
  data: T[];
  initialColumnVisibility: VisibilityState;
  isLoading?: boolean;
  resourceName: string;
};

export const PermissionsTable = <T extends object, C extends object>({
  columns,
  data,
  initialColumnVisibility,
  isLoading,
  resourceName,
}: PermissionsTableProps<T, C>) => {
  const [pinnedColumns, setPinnedColumns] = useState<ColumnPinningState>({
    left: [],
  });

  const onColumnPin = useCallback(() => {
    setPinnedColumns((prev) => ({ ...prev, left: ['name'] }));
  }, []);

  const onColumnUnPin = useCallback(() => {
    setPinnedColumns((prev) => ({ ...prev, left: [] }));
  }, []);

  return (
    <BaseTable
      columns={columns}
      data={data}
      enablePagination={false}
      initialColumnVisibility={initialColumnVisibility}
      pinnedColumns={pinnedColumns}
      resourceName={resourceName}
      render={(table) => (
        <DataTable
          columns={columns}
          isLoading={Boolean(isLoading)}
          onColumnPinAction={onColumnPin}
          onColumnUnPinAction={onColumnUnPin}
          table={table}
        />
      )}
    />
  );
};
