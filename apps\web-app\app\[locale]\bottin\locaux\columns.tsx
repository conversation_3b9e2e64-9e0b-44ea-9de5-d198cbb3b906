'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { useDeleteRoom } from '@/hooks/directory/rooms.hook';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import { Button } from '@/ui/button';
import type { RoomListItemDTO } from '@rie/api-contracts';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const roomsColumns = (
  _locale: SupportedLocale,
): ColumnDef<RoomListItemDTO>[] => {
  const directoryEntity = 'room' as const;

  return [
    {
      accessorKey: 'number',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.numero"
        />
      ),
      id: 'number',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'building',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.building"
        />
      ),
      id: 'building',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'jurisdiction',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.jurisdiction"
        />
      ),
      id: 'jurisdiction',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'area',
      cell: (cell) => {
        const area = cell.getValue() as number | null;
        return area ? `${area} m²` : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.area"
        />
      ),
      id: 'area',
      maxSize: 150,
      minSize: 100,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <RoomActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const RoomActions = ({ row }: { row: Row<RoomListItemDTO> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteRoom } = useDeleteRoom();
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.room;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-local"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteRoom(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.number })}
        trigger={
          <Button size="icon" variant="destructive" data-testid="delete-local">
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
