import type { projectFormSections } from '@/constants/directory/financing-project';
import type { EntityLocaleDictionary } from '@/types/common';

export type ProjectFormSectionKey = keyof typeof projectFormSections;

export type ControlledListEntity = {
  id: number | string;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: null | string;
  text: string;
  uid: null | string;
};

export type FundingProjectNumbers = {
  typeNumero: {
    id: string;
    text: string;
  };
  projetFinancement: {
    id: string;
    text: string;
  };
  numero: string;
};

export type IdType = {
  id: string;
};

export type ProjectPostPayload = {
  persons: string[];
  equipements: string[];
  infrastructures: string[];
  titres: EntityLocaleDictionary;
  titulaire: IdType;
  fciId: string;
  descriptions: EntityLocaleDictionary;
  anneeObtention?: number;
  dateFin?: Date;
  typeProjet: IdType;
  synchroId: string;
  projetFinancementNumeros: FundingProjectNumbers[];
};

export type ProjectPutFormSchema = ProjectPostPayload;
