'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { useDeleteEstablishment } from '@/hooks/directory/establishments.hook';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import { Button } from '@/ui/button';
import type { InstitutionList } from '@rie/domain/types';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const establishmentsColumns = (
  _locale: SupportedLocale,
): ColumnDef<InstitutionList>[] => {
  const directoryEntity = 'institution' as const;

  return [
    {
      accessorKey: 'text',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'acronym',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.acronym"
        />
      ),
      id: 'acronym',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'establishmentType',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.establishmentType"
        />
      ),
      id: 'establishmentType',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <EstablishmentActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const EstablishmentActions = ({ row }: { row: Row<InstitutionList> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteEstablishment } = useDeleteEstablishment();
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.institution;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-establishment"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteEstablishment(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.text })}
        trigger={
          <Button
            size="icon"
            variant="destructive"
            data-testid="delete-establishment"
          >
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
