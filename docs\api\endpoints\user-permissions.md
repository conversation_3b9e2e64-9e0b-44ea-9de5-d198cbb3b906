# User Permissions API

This document describes the User Permissions API endpoints for managing user roles and checking permissions within the RIE system.

## Base URL

```
/api/v2/users
```

**Note**: User permissions functionality follows REST conventions with user-centric endpoints. All permission-related operations are nested under the users resource.

## Authentication

All endpoints require session-based authentication. Users must be logged in with a valid session. Authentication is handled automatically via session cookies when making requests from the web application.

For API testing or external integrations, ensure you have established a valid session through the authentication endpoints before making requests to user permissions endpoints.

## Endpoints Overview

| Method | Endpoint | Description |
|:-------|:---------|:------------|
| POST | `/:userId/roles` | Assign role to user |
| DELETE | `/:userId/roles/:roleAssignmentId` | Remove role from user |
| GET | `/:userId/roles` | Get user's roles |
| POST | `/:userId/permissions/check` | Check user permission |
| GET | `/:userId/permissions` | Get user's effective permissions |
| POST | `/:userId/access/check` | Check user access to resource |
| GET | `/:userId/access-tree` | Get user's access tree |
| GET | `/:userId/permissions/:resourceType/:resourceId` | Get user permissions for specific resource |

## Role Management

### Assign Role to User

**POST** `/:userId/roles`

Assigns a role to a user with optional context (institution, unit, infrastructure, or equipment).

#### Request Body

```json
{
  "roleId": "string",
  "resourceType": "institution" | "unit" | "infrastructure" | "equipment" | null,
  "resourceId": "string", // required if resourceType is specified
  "grantedBy": "string" // optional, ID of user granting the role
}
```

#### Response (201 Created)

```json
{
  "id": "user-role-assignment-id",
  "userId": "user-123",
  "roleId": "equipment-manager",
  "resourceType": "infrastructure",
  "resourceId": "lab-001",
  "grantedBy": "admin-456",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### Examples

**Assign global administrator role:**

```bash
curl -X POST /api/v2/users/user-123/roles \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "roleId": "administrator",
    "grantedBy": "super-admin-789"
  }'
```

**Assign lab manager role for specific infrastructure:**

```bash
curl -X POST /api/v2/users/user-123/roles \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "roleId": "lab-manager",
    "resourceType": "infrastructure",
    "resourceId": "biochemistry-lab-001",
    "grantedBy": "department-head-456"
  }'
```

**Assign equipment operator role for specific equipment:**

```bash
curl -X POST /api/v2/users/user-123/roles \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "roleId": "equipment-operator",
    "resourceType": "equipment",
    "resourceId": "microscope-001",
    "grantedBy": "lab-manager-789"
  }'
```

### Remove Role from User

**DELETE** `/:userId/roles/:roleAssignmentId`

Removes a specific role assignment from a user.

#### Path Parameters

- `userId`: ID of the user
- `roleAssignmentId`: ID of the role assignment to remove

#### Response (200 OK)

```json
{
  "success": true,
  "message": "Role assignment removed successfully",
  "removedAssignment": {
    "id": "assignment-123",
    "userId": "user-123",
    "roleId": "equipment-manager",
    "resourceType": "infrastructure",
    "resourceId": "lab-001"
  }
}
```

#### Example

```bash
curl -X DELETE /api/v2/users/user-123/roles/assignment-456 \
  --cookie "session=your-session-cookie"
```

### Get User Roles

**GET** `/:userId/roles`

Retrieves all role assignments for a specific user.

#### Query Parameters

- `resourceType` (optional): Filter by resource type
- `resourceId` (optional): Filter by specific resource
- `includeInherited` (optional): Include inherited roles (default: true)

#### Response (200 OK)

```json
{
  "userId": "user-123",
  "roles": [
    {
      "id": "assignment-123",
      "roleId": "lab-manager",
      "roleName": "Lab Manager",
      "resourceType": "infrastructure",
      "resourceId": "biochemistry-lab-001",
      "resourceName": "Biochemistry Lab",
      "grantedBy": "department-head-456",
      "grantedByName": "Dr. Smith",
      "createdAt": "2024-01-01T00:00:00Z",
      "inherited": false
    },
    {
      "id": "assignment-456",
      "roleId": "researcher",
      "roleName": "Researcher",
      "resourceType": "unit",
      "resourceId": "biology-department",
      "resourceName": "Biology Department",
      "grantedBy": "admin-789",
      "grantedByName": "System Admin",
      "createdAt": "2024-01-01T00:00:00Z",
      "inherited": false
    }
  ],
  "totalCount": 2
}
```

#### Example

```bash
curl -X GET /api/v2/users/user-123/roles?resourceType=infrastructure \
  --cookie "session=your-session-cookie"
```

## Permission Checking

### Check User Permission

**POST** `/:userId/permissions/check`

Checks if a user has a specific permission for a domain/action combination.

#### Request Body

```json
{
  "domain": "equipment" | "infrastructure" | "institution" | "unit" | "user",
  "action": "create" | "read" | "update" | "delete" | "manage",
  "resourceId": "string" // optional, specific resource ID
}
```

#### Response (200 OK)

```json
{
  "hasPermission": true,
  "userId": "user-123",
  "domain": "equipment",
  "action": "update",
  "resourceId": "microscope-001",
  "grantingRoles": [
    {
      "roleId": "lab-manager",
      "roleName": "Lab Manager",
      "resourceType": "infrastructure",
      "resourceId": "biochemistry-lab-001"
    }
  ],
  "checkedAt": "2024-01-01T00:00:00Z"
}
```

#### Examples

**Check if user can create equipment:**

```bash
curl -X POST /api/v2/users/user-123/permissions/check \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "domain": "equipment",
    "action": "create"
  }'
```

**Check if user can update specific equipment:**

```bash
curl -X POST /api/v2/users/user-123/permissions/check \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "domain": "equipment",
    "action": "update",
    "resourceId": "microscope-001"
  }'
```

### Get User Permissions

**GET** `/:userId/permissions`

Retrieves all effective permissions for a user, including inherited permissions from roles and contexts.

#### Query Parameters

- `domain` (optional): Filter by specific domain
- `resourceType` (optional): Filter by resource type
- `resourceId` (optional): Filter by specific resource
- `grouped` (optional): Group permissions by domain (default: false)

#### Response (200 OK)

```json
{
  "userId": "user-123",
  "permissions": [
    {
      "domain": "equipment",
      "action": "read",
      "resourceType": "infrastructure",
      "resourceId": "biochemistry-lab-001",
      "grantedBy": {
        "roleId": "lab-manager",
        "roleName": "Lab Manager",
        "assignmentId": "assignment-123"
      }
    },
    {
      "domain": "equipment",
      "action": "create",
      "resourceType": "infrastructure",
      "resourceId": "biochemistry-lab-001",
      "grantedBy": {
        "roleId": "lab-manager",
        "roleName": "Lab Manager",
        "assignmentId": "assignment-123"
      }
    }
  ],
  "groupedPermissions": {
    "equipment": {
      "read": ["biochemistry-lab-001"],
      "create": ["biochemistry-lab-001"],
      "update": ["biochemistry-lab-001"]
    },
    "infrastructure": {
      "read": ["biochemistry-lab-001"]
    }
  },
  "totalCount": 15
}
```

#### Example

```bash
curl -X GET /api/v2/users/user-123/permissions?domain=equipment&grouped=true \
  --cookie "session=your-session-cookie"
```

## Access Control

### Check User Access to Resource

**POST** `/:userId/access/check`

Checks if a user has hierarchical access to a specific resource through the access tree system. This performs the "WHERE" check in the two-step verification process.

#### Request Body

```json
{
  "resourceType": "institution" | "unit" | "infrastructure" | "equipment",
  "resourceId": "string"
}
```

#### Response (200 OK)

```json
{
  "hasAccess": true,
  "userId": "user-123",
  "resourceType": "equipment",
  "resourceId": "microscope-001",
  "checkedAt": "2024-01-01T00:00:00Z"
}
```

#### Example

```bash
curl -X POST /api/v2/users/user-123/access/check \
  -H "Content-Type: application/json" \
  --cookie "session=your-session-cookie" \
  -d '{
    "resourceType": "equipment",
    "resourceId": "microscope-001"
  }'
```

### Get User Access Tree

**GET** `/:userId/access-tree`

Returns the complete hierarchical access tree for a user, showing all resources they have access to across the institution hierarchy.

#### Response (200 OK)

```json
{
  "userId": "user-123",
  "accessTree": {
    "institutions": ["university-001", "research-institute-002"],
    "units": ["biology-dept", "chemistry-dept", "physics-dept"],
    "infrastructures": ["biochemistry-lab", "molecular-lab", "physics-lab"],
    "equipments": ["microscope-001", "centrifuge-002", "spectrometer-003"]
  },
  "globalPermissions": [
    {
      "domain": "user",
      "action": "read"
    }
  ],
  "generatedAt": "2024-01-01T00:00:00Z",
  "cacheExpiresAt": "2024-01-01T00:30:00Z"
}
```

#### Example

```bash
curl -X GET /api/v2/users/user-123/access-tree \
  --cookie "session=your-session-cookie"
```

### Get User Permissions for Resource

**GET** `/:userId/permissions/:resourceType/:resourceId`

Returns all permissions a user has for a specific resource, combining both direct permissions and inherited permissions through the hierarchy.

#### Path Parameters

- `userId`: User ID
- `resourceType`: Type of resource (institution, unit, infrastructure, equipment)
- `resourceId`: ID of the specific resource

#### Response (200 OK)

```json
{
  "userId": "user-123",
  "resourceType": "equipment",
  "resourceId": "microscope-001",
  "permissions": [
    {
      "id": "perm-001",
      "domain": "equipment",
      "action": "read",
      "source": {
        "type": "role",
        "roleId": "researcher",
        "roleName": "Researcher",
        "assignmentId": "assignment-123"
      }
    },
    {
      "id": "perm-002",
      "domain": "equipment",
      "action": "update",
      "source": {
        "type": "role",
        "roleId": "lab-manager",
        "roleName": "Lab Manager",
        "assignmentId": "assignment-456"
      }
    }
  ],
  "totalCount": 2,
  "checkedAt": "2024-01-01T00:00:00Z"
}
```

#### Example

```bash
curl -X GET /api/v2/users/user-123/permissions/equipment/microscope-001 \
  --cookie "session=your-session-cookie"
```

## Error Responses

### Common Error Codes

#### 400 Bad Request

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "roleId",
        "message": "Role ID is required"
      }
    ]
  }
}
```

#### 401 Unauthorized

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}
```

#### 403 Forbidden

```json
{
  "error": {
    "code": "FORBIDDEN",
    "message": "Insufficient permissions to assign roles"
  }
}
```

#### 404 Not Found

```json
{
  "error": {
    "code": "NOT_FOUND",
    "message": "User not found",
    "details": {
      "userId": "user-123"
    }
  }
}
```

#### 409 Conflict

```json
{
  "error": {
    "code": "ROLE_ALREADY_ASSIGNED",
    "message": "User already has this role for the specified context",
    "details": {
      "userId": "user-123",
      "roleId": "lab-manager",
      "resourceType": "infrastructure",
      "resourceId": "lab-001"
    }
  }
}
```

## Permission Domains and Actions

### Available Domains

- `address` - Address management
- `applicationSector` - Application sector management
- `building` - Building management
- `campus` - Campus management
- `equipment` - Equipment management
- `excellenceHub` - Excellence hub management
- `fundingProject` - Funding project management
- `infrastructure` - Research infrastructure management
- `innovationLab` - Innovation lab management
- `institution` - Institution management
- `media` - Media management
- `people` - People management
- `researchField` - Research field management
- `room` - Room management
- `serviceContract` - Service contract management
- `serviceOffer` - Service offer management
- `technique` - Technique management
- `unit` - Unit/department management
- `user` - User management
- `vendor` - Vendor management
- `visibility` - Visibility management
- `documentationCategory` - Documentation category management
- `equipmentCategory` - Equipment category management
- `equipmentStatus` - Equipment status management
- `equipmentType` - Equipment type management
- `fundingProjectType` - Funding project type management
- `fundingProjectIdentifierType` - Funding project identifier type management
- `infrastructureStatus` - Infrastructure status management
- `infrastructureType` - Infrastructure type management
- `institutionType` - Institution type management
- `mediaType` - Media type management
- `peopleRoleType` - People role type management
- `roomCategory` - Room category management
- `unitType` - Unit type management

### Available Actions

- `create` - Create new resources
- `read` - View/list resources
- `update` - Modify existing resources
- `delete` - Remove resources
- `manage` - Full management (includes all actions)

## Hierarchical Access Model

The system implements hierarchical access where permissions cascade down through the organizational structure:

- **Institution role** → Access to all units, infrastructures, and equipment within that institution
- **Unit role** → Access to all infrastructures and equipment within that unit
- **Infrastructure role** → Access to all equipment within that infrastructure
- **Equipment role** → Access to that specific equipment only

### Two-Step Verification

The permission system uses a two-step verification process:

1. **WHERE (Hierarchical Access)**: Does the user have access to this resource through the hierarchy?
   - Checked via `/access/check` endpoint or automatically when `resourceId` is provided to permission checks
   - Admin users automatically pass this check

2. **WHAT (Permission Validation)**: Does the user have the required `domain:action` permission?
   - Checked via `/permissions/check` endpoint
   - Admin users must still have the specific permission through their role assignments

Both checks must pass for resource-specific operations. See the [main permissions documentation](../permissions.md#permission-checking-execution-flow) for detailed execution flow.

### Context Types

- `institution` - University/organization level
- `unit` - Department/faculty level
- `infrastructure` - Research lab/facility level
- `equipment` - Individual equipment level
- `null` - Global/system level

## Best Practices

### Role Assignment

1. **Principle of Least Privilege**: Assign minimal necessary permissions
2. **Context-Specific**: Use appropriate resource context when possible
3. **Audit Trail**: Always specify `grantedBy` for accountability
4. **Regular Review**: Periodically review and clean up role assignments

### Permission Checking

1. **Cache Results**: Cache permission checks for performance
2. **Batch Operations**: Use bulk permission checks when possible
3. **Fail Secure**: Default to deny access when in doubt
4. **Log Access**: Log permission checks for security auditing

### Error Handling

1. **Graceful Degradation**: Handle permission errors gracefully
2. **User Feedback**: Provide clear error messages
3. **Security**: Don't expose sensitive information in errors
4. **Monitoring**: Monitor permission-related errors

---

**Related Documentation**:

- [Permissions System Overview](../permissions.md)
- [Authentication Guide](../authentication.md)
- [API Overview](../README.md)
