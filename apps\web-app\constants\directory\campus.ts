import { NULLABLE_SELECT_OPTION } from '@/constants/common';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import type { SupportedLocale } from '@/types/locale';
import type { VisibilityState } from '@tanstack/react-table';

export const initialColumnVisibility: VisibilityState = {
  name: true,
  jurisdiction: true,
  lastUpdatedAt: true,
  actions: true,
};

export const campusFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const campusFormDefaultValues = (
  locale: SupportedLocale,
): CampusFormSchema => ({
  jurisdiction: NULLABLE_SELECT_OPTION,
  pseudonym: '',
  name: [{ locale, value: '' }],
});
