import {
  type RieServiceParams,
  defaultRieServiceParams,
} from '@/constants/rie-client';
import type { DirectoryEntity } from '@rie/domain/types';
import { rieClient } from '@/services/client/client';
import { directoryEntityPathMap } from '@/services/mappers/bottin/entities';
import type {
  DirectoryFull,
  EntityPayload,
  EntityResponse,
} from '@/types/directory/directory';
import type { ApiResponse, ApiReturnType } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';

export const getAllEntities = async (
  locale: SupportedLocale,
  directoryEntity: DirectoryEntity,
): Promise<ApiReturnType<DirectoryFull[]>> => {
  const route = directoryEntityPathMap(directoryEntity);
  const response = await rieClient.get<ApiResponse<DirectoryFull[]>>(
    `search/${route}`,
    {
      params: defaultRieServiceParams(locale),
    },
  );
  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
  };
};

export const getEntityById = async (
  id: string,
  directoryEntity: DirectoryEntity,
  locale: SupportedLocale,
): Promise<{
  entity: DirectoryFull;
}> => {
  const route = directoryEntityPathMap(directoryEntity);
  const entityResponse = await rieClient.get<{ data: DirectoryFull }>(
    `search/${route}/${id}`,
    {
      params: defaultRieServiceParams(locale),
    },
  );

  return {
    entity: entityResponse.data.data,
  };
};

export const getEntitiesList = async ({
  directoryEntity,
  pageParam,
  params,
  queryParams,
}: {
  directoryEntity: DirectoryEntity;
  pageParam: number;
  params: RieServiceParams;
  queryParams: string;
}): Promise<ApiReturnType<DirectoryFull[]>> => {
  const route = directoryEntityPathMap(directoryEntity);
  const response = await rieClient.get<ApiResponse<DirectoryFull[]>>(
    `search/${route}?start=${pageParam}${queryParams.length ? `&${queryParams}` : ''}`,
    {
      params,
    },
  );
  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
  };
};

export const createEntity = async <K extends DirectoryEntity>(
  directoryEntity: K,
  payload: EntityPayload<K>,
): Promise<EntityResponse<K>> => {
  const route = directoryEntityPathMap(directoryEntity);
  const response = await rieClient.post<EntityResponse<K>>(
    `rest/${route}/?format=json`,
    payload,
  );
  return response.data;
};

export const updateEntity = async <K extends DirectoryEntity>(
  directoryEntity: K,
  id: string,
  payload: EntityPayload<K>,
): Promise<EntityResponse<K>> => {
  const route = directoryEntityPathMap(directoryEntity);
  const response = await rieClient.put<EntityResponse<K>>(
    `rest/${route}/${id}/?format=json`,
    payload,
  );
  return response.data;
};

export const deleteEntity = async <K extends DirectoryEntity>(
  directoryEntity: DirectoryEntity,
  id: string,
): Promise<EntityResponse<K>> => {
  const route = directoryEntityPathMap(directoryEntity);
  const response = await rieClient.delete<EntityResponse<K>>(
    `rest/${route}/${id}/?format=json`,
  );
  return response.data;
};
