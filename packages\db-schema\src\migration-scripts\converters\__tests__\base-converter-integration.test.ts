/**
 * BaseConverter Integration Tests
 *
 * Tests the complete flow from CREATE TABLE → INSERT statements → parsed values
 * using real data from the MySQL dump that has caused parsing failures.
 */

import { beforeEach, describe, expect, test, vi } from 'vitest';
import {
  BATIMENT_INSERT_STATEMENT,
  COMPLEX_INSERT_WITH_QUOTES,
  FULL_SQL_SAMPLE,
} from './test-data';
import { TestableBaseConverter } from './testable-base-converter';

describe('BaseConverter Integration Tests', () => {
  let converter: TestableBaseConverter;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  let consoleSpy: any;

  beforeEach(() => {
    converter = new TestableBaseConverter();
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('Complete parsing flow integration', () => {
    test('should handle complete batiment table processing', () => {
      // 1. Extract CREATE TABLE statement
      const createStatement = converter.testExtractCreateStatement(
        FULL_SQL_SAMPLE,
        'batiment',
      );
      expect(createStatement).toContain('CREATE TABLE `batiment`');

      // 2. Extract column names
      const columns = converter.testExtractColumnNames(createStatement);
      expect(columns).toEqual([
        'id',
        'uid',
        'pseudonyme',
        'juridiction_id',
        'campus_id',
        'slug',
        'removed',
        'validated',
      ]);

      // 3. Extract INSERT statements (splits multi-row into individual statements)
      const insertStatements = converter.testExtractInsertStatements(
        FULL_SQL_SAMPLE,
        'batiment',
      );
      expect(insertStatements.length).toBeGreaterThan(1); // Should split multi-row INSERT

      // 4. The INSERT statements are already individual rows (no need to split)
      // 5. Parse individual row values from the first statement
      const parsedValues = converter.testExtractValuesFromInsertStatement(
        insertStatements[0] as string,
        FULL_SQL_SAMPLE,
        'batiment',
      );

      expect(parsedValues).toEqual({
        id: '1',
        uid: '504A',
        pseudonyme: null,
        juridiction_id: '1',
        campus_id: '1',
        slug: null,
        removed: '0',
        validated: '0',
      });
    });

    test('should handle complete batiment_trad table processing with French characters', () => {
      // 1. Extract CREATE TABLE statement
      const createStatement = converter.testExtractCreateStatement(
        FULL_SQL_SAMPLE,
        'batiment_trad',
      );
      expect(createStatement).toContain('CREATE TABLE `batiment_trad`');

      // 2. Extract column names
      const columns = converter.testExtractColumnNames(createStatement);
      expect(columns).toEqual(['id', 'data_id', 'language_id', 'nom']);

      // 3. Extract INSERT statements (splits multi-row into individual statements)
      const insertStatements = converter.testExtractInsertStatements(
        FULL_SQL_SAMPLE,
        'batiment_trad',
      );
      expect(insertStatements.length).toBeGreaterThan(1); // Should split multi-row INSERT

      // 4. Find a statement with French characters for testing
      const frenchStatement = insertStatements.find((stmt) =>
        stmt.includes("Centre d''éducation physique"),
      );
      expect(frenchStatement).toBeDefined();

      // 5. Parse row with French characters and quotes
      const parsedValues = converter.testExtractValuesFromInsertStatement(
        frenchStatement as string,
        FULL_SQL_SAMPLE,
        'batiment_trad',
      );

      expect(parsedValues.nom).toBe(
        "Centre d'éducation physique et des sports (CEPSUM)",
      );
    });

    test('should handle complex INSERT with mixed quotes and commas', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL,
          \`description\` text DEFAULT NULL,
          \`value\` int(11) DEFAULT NULL
        );
        ${COMPLEX_INSERT_WITH_QUOTES}
      `;

      // Extract and process the complex INSERT (should split into individual statements)
      const insertStatements = converter.testExtractInsertStatements(
        sqlContent,
        'test_table',
      );
      expect(insertStatements).toHaveLength(2); // Should split multi-row INSERT into 2 statements

      // Parse first row with complex quotes
      const parsedValues = converter.testExtractValuesFromInsertStatement(
        insertStatements[0] as string,
        sqlContent,
        'test_table',
      );

      expect(parsedValues.name).toBe(
        "Centre hospitalier de l'Université de Montréal",
      );
      expect(parsedValues.description).toBe('Description with "double quotes"');
      expect(parsedValues.value).toBeNull();
    });
  });

  describe('Error scenarios and edge cases', () => {
    test('should warn about column count mismatch - not enough values', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL,
          \`description\` text DEFAULT NULL,
          \`status\` int(11) DEFAULT NULL,
          \`created_at\` timestamp DEFAULT NULL,
          \`updated_at\` timestamp DEFAULT NULL,
          \`removed\` tinyint(1) DEFAULT 0,
          \`validated\` tinyint(1) DEFAULT 0
        );
      `;

      const insertStatement = "INSERT INTO `test_table` VALUES (1,'test');"; // Only 2 values for 8 columns

      const result = converter.testExtractValuesFromInsertStatement(
        insertStatement,
        sqlContent,
        'test_table',
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Warning: Not enough values (2) for columns (8)',
      );

      // Should still return partial data
      expect(result.id).toBe('1');
      expect(result.name).toBe('test');
      // Missing columns are set to null, not undefined
      expect(result.description).toBeNull();
    });

    test('should handle too many values gracefully', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL
        );
      `;

      const insertStatement =
        "INSERT INTO `test_table` VALUES (1,'test','extra','values');";

      const result = converter.testExtractValuesFromInsertStatement(
        insertStatement,
        sqlContent,
        'test_table',
      );

      // Should use only the first values that match columns
      expect(result.id).toBe('1');
      expect(result.name).toBe('test');
      expect(Object.keys(result)).toHaveLength(2);
    });

    test('should handle missing CREATE TABLE statement', () => {
      const insertStatement = "INSERT INTO `nonexistent` VALUES (1,'test');";

      expect(() => {
        converter.testExtractValuesFromInsertStatement(
          insertStatement,
          'Some SQL without CREATE TABLE',
          'nonexistent',
        );
      }).toThrow(
        'Could not find CREATE TABLE statement for table: nonexistent',
      );
    });

    test('should handle malformed INSERT statements', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL
        );
      `;

      expect(() => {
        converter.testExtractValuesFromInsertStatement(
          'INSERT INTO test_table;', // No VALUES clause
          sqlContent,
          'test_table',
        );
      }).toThrow('Could not extract values from INSERT statement');
    });

    test('should handle empty VALUES clause', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL
        );
      `;

      expect(() => {
        converter.testExtractValuesFromInsertStatement(
          'INSERT INTO test_table VALUES ();',
          sqlContent,
          'test_table',
        );
      }).toThrow('Could not extract values from INSERT statement');
    });

    test('should handle unmatched quotes in values', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL
        );
      `;

      // This should not crash but may produce unexpected results
      const insertStatement =
        "INSERT INTO `test_table` VALUES (1,'unmatched quote);";

      // The behavior here depends on the implementation - it might throw or handle gracefully
      try {
        const result = converter.testExtractValuesFromInsertStatement(
          insertStatement,
          sqlContent,
          'test_table',
        );
        // If it doesn't throw, verify it at least extracts the ID
        expect(result.id).toBe('1');
      } catch (error) {
        // If it throws, that's also acceptable behavior for malformed input
        expect(error).toBeDefined();
      }
    });
  });

  describe('Real-world failure scenarios', () => {
    test('should reproduce and fix the "Not enough values (2) for columns (8)" error', () => {
      // This test reproduces the exact error scenario mentioned in the requirements
      const sqlContent = `
        CREATE TABLE \`batiment\` (
          \`id\` smallint(6) NOT NULL AUTO_INCREMENT,
          \`uid\` varchar(36) DEFAULT NULL,
          \`pseudonyme\` varchar(150) DEFAULT NULL,
          \`juridiction_id\` int(11) NOT NULL,
          \`campus_id\` smallint(6) NOT NULL,
          \`slug\` varchar(255) DEFAULT NULL,
          \`removed\` tinyint(1) NOT NULL DEFAULT '0',
          \`validated\` tinyint(1) DEFAULT '0'
        );
      `;

      const problematicInsert = "INSERT INTO `batiment` VALUES (1,'504A');"; // Only 2 values

      converter.testExtractValuesFromInsertStatement(
        problematicInsert,
        sqlContent,
        'batiment',
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Warning: Not enough values (2) for columns (8)',
      );
    });

    test('should handle BuildingMigrationConverter data patterns', () => {
      // Test the specific patterns that are causing issues in BuildingMigrationConverter
      const realBatimentData = BATIMENT_INSERT_STATEMENT;

      // Extract the first few rows to test
      const valuesMatch = realBatimentData.match(/VALUES\s+(.+)$/i);
      expect(valuesMatch).toBeTruthy();

      const valueRows = converter.testSplitValueRows(
        valuesMatch?.[1] as string,
      );
      expect(valueRows.length).toBeGreaterThan(0);

      // Test each row can be parsed without errors
      valueRows.slice(0, 3).forEach((row, index) => {
        const insertStatement = `INSERT INTO \`batiment\` VALUES (${row});`;
        const result = converter.testExtractValuesFromInsertStatement(
          insertStatement,
          FULL_SQL_SAMPLE,
          'batiment',
        );

        expect(result.id).toBeDefined();
        expect(result.uid).toBeDefined();
        expect(typeof result.juridiction_id).toBe('string');
        expect(typeof result.campus_id).toBe('string');
      });
    });
  });
});
