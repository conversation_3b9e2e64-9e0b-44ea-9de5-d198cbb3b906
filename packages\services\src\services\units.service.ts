import type { UnitFormRequestDTO } from '@rie/api-contracts';
import { Unit } from '@rie/domain/aggregates';
import { UnitNotFoundError } from '@rie/domain/errors';
import type { CollectionViewType, ResourceViewType } from '@rie/domain/types';
import {
  UnitTranslation,
  UnitTranslationCollection,
} from '@rie/domain/value-objects';
import { UnitsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as Schema from 'effect/Schema';
import { UnitFormToDomainInput } from '../mappers';
import {
  serializeUnitToDetail,
  serializeUnitToFormEdit,
  serializeUnitToListItem,
  serializeUnitToSelectOption,
} from '../serializers';

// Type helper for unit domain input to avoid circular references
type UnitDomainInput = {
  isActive: boolean;
  guidId: string | null;
  typeId: string;
  parentId: string | null;
  modifiedBy: string | null;
  translations: ReadonlyArray<{
    readonly locale: string;
    readonly name: string | null;
    readonly description: string | null;
    readonly otherNames: string | null;
    readonly acronyms: string | null;
  }>;
};

export class UnitsServiceLive extends Effect.Service<UnitsServiceLive>()(
  'UnitsServiceLive',
  {
    dependencies: [UnitsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllUnits = (params: {
        locale: string;
        fallbackLocale: string;
        view: CollectionViewType;
      }) =>
        Effect.gen(function* () {
          const tStart = performance.now();
          const repo = yield* UnitsRepositoryLive;
          const units = yield* repo.findAllUnits();
          const afterFetch = performance.now();
          console.log(
            `[Service][Units] fetched ${units.length} units in ${afterFetch - tStart} ms (view=${params.view}, locale=${params.locale}, fallback=${params.fallbackLocale})`,
          );

          // Serialize based on view type
          switch (params.view) {
            case 'list': {
              const result = yield* Effect.all(
                units.map((unit) =>
                  serializeUnitToListItem(
                    unit,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Units] serialized list ${result.length} items in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'select': {
              const result = yield* Effect.all(
                units.map((unit) =>
                  serializeUnitToSelectOption(
                    unit,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Units] serialized select ${result.length} options in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'grid':
              return units;
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const getUnitById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          const unit = yield* repo.findUnitById(params.id);
          if (!unit) {
            return yield* Effect.fail(new UnitNotFoundError({ id: params.id }));
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              return yield* serializeUnitToDetail(
                unit,
                params.locale,
                params.fallbackLocale,
              );
            }
            case 'edit': {
              const unitData = yield* unit.toRaw();
              return yield* serializeUnitToFormEdit(unitData);
            }
            default: {
              // TypeScript exhaustiveness check
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const createUnit = (params: {
        unitDto: UnitFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Map the incoming DTO to a clean domain input shape
          const domainInput = yield* Schema.decode(UnitFormToDomainInput)(
            params.unitDto,
          );

          // 2. For new unit creation, create translation VOs from input data
          // We create minimal translation objects that will get proper IDs during save
          const translationVOs = yield* Effect.all(
            domainInput.translations.map((t) => {
              // Create a translation object with minimal required fields
              const translationData = {
                id: '', // Empty string, will be filled by DB
                dataId: '', // Empty string, will be filled by repository
                locale: t.locale,
                name: t.name,
                description: t.description,
                otherNames: t.otherNames,
                acronyms: t.acronyms,
              };
              return UnitTranslation.create(translationData);
            }),
          );

          const translationCollection =
            yield* UnitTranslationCollection.create(translationVOs);

          // 3. Create the Unit aggregate
          const unit = yield* Unit.create({
            ...domainInput,
            modifiedBy: params.userId,
            translations: translationCollection,
          });

          // 4. Save the new aggregate
          const repo = yield* UnitsRepositoryLive;
          const savedUnit = yield* repo.save(unit);

          // 5. Serialize the result for the API response
          return yield* pipe(
            savedUnit.toRaw(),
            Effect.flatMap((raw) => serializeUnitToFormEdit(raw)),
          );
        });

      const updateUnit = ({
        id,
        unitDto,
        userId,
      }: {
        id: string;
        unitDto: UnitFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          console.log('[Service][Units][Update] Incoming payload received');

          // 1. Fetch the existing aggregate
          const existingUnit = yield* repo.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(new UnitNotFoundError({ id }));
          }

          // 2. Map the DTO to domain input
          const domainInputRaw = yield* Schema.decode(UnitFormToDomainInput)(
            unitDto,
          );

          // Normalize fields to avoid undefined
          const domainInput: UnitDomainInput = {
            isActive: domainInputRaw.isActive ?? true,
            guidId: domainInputRaw.guidId ?? null,
            typeId: domainInputRaw.typeId,
            parentId: domainInputRaw.parentId ?? null,
            modifiedBy: domainInputRaw.modifiedBy ?? null,
            translations: domainInputRaw.translations,
          };
          console.log('[Service][Units][Update] domainInput mapped');

          // 3. Merge existing translations with new translations
          const existingUnitData = yield* existingUnit.toRaw();
          const existingTranslations = existingUnitData.translations;

          // Create a map of existing translations by locale for efficient lookup
          const existingTranslationsMap = new Map(
            existingTranslations.map(t => [t.locale, t])
          );

          // Start with a copy of existing translations
          const mergedTranslations = new Map(existingTranslationsMap);

          // Update or add new translations
          for (const newTranslation of domainInput.translations) {
            if (existingTranslationsMap.has(newTranslation.locale)) {
              // Update existing translation, preserving id and dataId
              const existing = existingTranslationsMap.get(newTranslation.locale);
              if (existing) {
                mergedTranslations.set(newTranslation.locale, {
                  ...existing,
                  name: newTranslation.name,
                  description: newTranslation.description,
                  otherNames: newTranslation.otherNames,
                  acronyms: newTranslation.acronyms,
                });
              }
            } else {
              // Add new translation - note: id will be generated by the database
              // so we only include it in the domain object for type compatibility
              mergedTranslations.set(newTranslation.locale, {
                id: '', // Temporary id, will be generated by DB
                locale: newTranslation.locale,
                name: newTranslation.name,
                description: newTranslation.description,
                otherNames: newTranslation.otherNames,
                acronyms: newTranslation.acronyms,
                dataId: existingUnitData.id,
              });
            }
          }

          // Convert to array and create translation VOs
          const allTranslationsData = Array.from(mergedTranslations.values());
          const translationVOs = yield* Effect.all(
            allTranslationsData.map((t) => UnitTranslation.create(t)),
          );
          const translationCollection =
            yield* UnitTranslationCollection.create(translationVOs);

          // 4. Call the update method on the existing aggregate
          const updatedDomainUnit = yield* existingUnit.update({
            typeId: domainInput.typeId,
            parentId: domainInput.parentId,
            guidId: domainInput.guidId ?? null,
            isActive: domainInput.isActive,
            modifiedBy: userId,
            translations: translationCollection,
          });

          // 5. Save the updated aggregate
          const savedUnit = yield* repo.save(updatedDomainUnit);
          console.log('[Service][Units][Update] Saved unit id:', (yield* savedUnit.toRaw()).id);

          // 6. Serialize the result for the API response
          return yield* pipe(
            savedUnit.toRaw(),
            Effect.flatMap((raw) => serializeUnitToFormEdit(raw)),
          );
        });

      const deleteUnit = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* UnitsRepositoryLive;
          const existingUnit = yield* repo.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(new UnitNotFoundError({ id }));
          }
          const result = yield* repo.deleteUnit(id);
          return result.length > 0;
        });

      return {
        getAllUnits,
        getUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
      } as const;
    }),
  },
) { }
