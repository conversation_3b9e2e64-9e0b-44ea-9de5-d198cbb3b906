import type { VendorFormRequestDTO } from '@rie/api-contracts';
import { Vendor } from '@rie/domain/aggregates';
import { VendorNotFoundError } from '@rie/domain/errors';
import type { CollectionViewType, ResourceViewType } from '@rie/domain/types';
import {
  VendorTranslation,
  VendorTranslationCollection,
} from '@rie/domain/value-objects';
import { VendorsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as Schema from 'effect/Schema';
import { VendorFormToDomainInput } from '../mappers';
import {
  serializeVendorToDetail,
  serializeVendorToFormEdit,
  serializeVendorToListItem,
  serializeVendorToSelectOption,
} from '../serializers';

export class VendorsServiceLive extends Effect.Service<VendorsServiceLive>()(
  'VendorsServiceLive',
  {
    dependencies: [VendorsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllVendors = (params: {
        locale: string;
        fallbackLocale: string;
        view: CollectionViewType;
      }) =>
        Effect.gen(function* () {
          const tStart = performance.now();
          const repo = yield* VendorsRepositoryLive;
          const vendors = yield* repo.findAllVendors();
          const afterFetch = performance.now();
          console.log(
            `[Service][Vendors] fetched ${vendors.length} vendors in ${afterFetch - tStart} ms (view=${params.view}, locale=${params.locale}, fallback=${params.fallbackLocale})`,
          );

          // Serialize based on view type
          switch (params.view) {
            case 'list': {
              const result = yield* Effect.all(
                vendors.map((vendor) =>
                  serializeVendorToListItem(
                    vendor,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Vendors] serialized list ${result.length} items in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'select': {
              const result = yield* Effect.all(
                vendors.map((vendor) =>
                  serializeVendorToSelectOption(
                    vendor,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Vendors] serialized select ${result.length} options in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'grid':
              return vendors;
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const getVendorById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const vendor = yield* repo.findVendorById(params.id);
          if (!vendor) {
            return yield* Effect.fail(
              new VendorNotFoundError({ id: params.id }),
            );
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              return yield* serializeVendorToDetail(
                vendor,
                params.locale,
                params.fallbackLocale,
              );
            }
            case 'edit': {
              return yield* pipe(
                vendor.toRaw(),
                Effect.flatMap((raw) => serializeVendorToFormEdit(raw)),
              );
            }
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const createVendor = ({
        vendorDto,
        userId,
      }: {
        vendorDto: VendorFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Transform DTO to domain input
          const domainInput = yield* Schema.decode(VendorFormToDomainInput)(vendorDto);

          // 2. Create translation value objects
          const translations = yield* Effect.all(
            domainInput.translations.map((t) => VendorTranslation.create(t)),
          );
          const translationCollection = yield* VendorTranslationCollection.create(translations);

          // 3. Create the aggregate
          const vendor = yield* Vendor.create({
            startDate: domainInput.startDate,
            endDate: domainInput.endDate,
            modifiedBy: userId,
            translations: translationCollection,
          });

          // 4. Save the new aggregate
          const repo = yield* VendorsRepositoryLive;
          const savedVendor = yield* repo.save(vendor);

          // 5. Serialize the result for the API response
          return yield* pipe(
            savedVendor.toRaw(),
            Effect.flatMap((raw) => serializeVendorToFormEdit(raw)),
          );
        });

      const updateVendor = ({
        id,
        vendorDto,
        userId,
      }: {
        id: string;
        vendorDto: VendorFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Find existing vendor
          const repo = yield* VendorsRepositoryLive;
          const existingVendor = yield* repo.findVendorById(id);
          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }

          // 2. Transform DTO to domain input
          const domainInput = yield* Schema.decode(VendorFormToDomainInput)(vendorDto);

          // 3. Create translation value objects
          const translations = yield* Effect.all(
            domainInput.translations.map((t) => VendorTranslation.create(t)),
          );
          const translationCollection = yield* VendorTranslationCollection.create(translations);

          // 4. Update the aggregate
          const updatedDomainVendor = yield* existingVendor.update({
            startDate: domainInput.startDate,
            endDate: domainInput.endDate,
            isActive: domainInput.isActive ?? true,
            modifiedBy: userId,
            translations: translationCollection,
          });

          // 4. Save the updated aggregate
          const savedVendor = yield* repo.save(updatedDomainVendor);
          console.log('[Service][Vendors][Update] Saved vendor id:', (yield* savedVendor.toRaw()).id);

          // 5. Serialize the result for the API response
          return yield* pipe(
            savedVendor.toRaw(),
            Effect.flatMap((raw) => serializeVendorToFormEdit(raw)),
          );
        });

      const deleteVendor = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* VendorsRepositoryLive;
          const existingVendor = yield* repo.findVendorById(id);
          if (!existingVendor) {
            return yield* Effect.fail(new VendorNotFoundError({ id }));
          }
          const result = yield* repo.deleteVendor(id);
          return result.length > 0;
        });

      return {
        getAllVendors,
        getVendorById,
        createVendor,
        updateVendor,
        deleteVendor,
      } as const;
    }),
  },
) { }
