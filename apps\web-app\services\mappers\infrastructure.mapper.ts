import { CAMPUS_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import type { SelectOption } from '@/types/common';
import type { EquipmentStructure } from '@/types/equipment';
import type {
  InfrastructureDescription,
  InfrastructureDetails,
  ResourceStatusType,
} from '@/types/infrastructure';
import type { SupportedLocale } from '@/types/locale';
import type {
  InfrastructureData,
  InfrastructureInput,
} from '@rie/domain/types';
import type { InfrastructureWithRelatedEquipments } from '@rie/domain/types';
import { getLocalizedField } from '@rie/domain/utils';

/**
 * Transforms translatable field array to translation array format
 * @param translatableField - Array of objects with locale and value properties
 * @param fieldName - The field name to extract (e.g., 'name', 'description')
 * @returns Array of translation objects with locale and field value
 */
const transformTranslatableFieldToTranslations = (
  fieldName: 'name' | 'description' | 'otherNames' | 'acronyms',
  translatableField?: Array<{ locale: string; value: string }>,
): Array<{ locale: SupportedLocale; [key: string]: string | null }> => {
  if (!translatableField || translatableField.length === 0) {
    return [];
  }

  const translations: Array<{
    locale: SupportedLocale;
    [key: string]: string | null;
  }> = [];

  // Process each translatable field entry
  for (const entry of translatableField) {
    translations.push({
      locale: entry.locale as SupportedLocale,
      [fieldName]: entry.value.trim() || null,
    });
  }

  return translations;
};

/**
 * Transforms translations array to translatable field array format (reverse of above)
 * @param translations - Array of translation objects from domain schema
 * @param fieldName - The field name to extract (e.g., 'name', 'description')
 * @returns Array of objects with locale and value properties for form
 */
const transformTranslationsToTranslatableField = (
  translations: InfrastructureData['translations'],
  fieldName: 'name' | 'description' | 'otherNames' | 'acronyms',
): Array<{ locale: string; value: string }> => {
  if (!translations || translations.length === 0) return [];

  const translatableField: Array<{ locale: string; value: string }> = [];

  // Process each translation entry
  for (const translation of translations) {
    const fieldValue = translation[fieldName];
    if (fieldValue?.trim()) {
      translatableField.push({
        locale: translation.locale,
        value: fieldValue.trim(),
      });
    }
  }

  return translatableField;
};

/**
 * Merges translation arrays by locale to create complete translation objects
 * @param translationArrays - Arrays of partial translation objects
 * @returns Array of complete translation objects with all fields
 */
const mergeTranslationsByLocale = (
  ...translationArrays: Array<
    Array<{ locale: SupportedLocale; [key: string]: string | null }>
  >
): Array<{
  locale: SupportedLocale;
  name: string | null;
  description: string | null;
  otherNames: string | null;
  acronyms: string | null;
}> => {
  const translationMap = new Map<
    string,
    {
      locale: SupportedLocale;
      name: string | null;
      description: string | null;
      otherNames: string | null;
      acronyms: string | null;
    }
  >();

  // Initialize with all possible locales
  for (const locale of ['en', 'fr'] as SupportedLocale[]) {
    translationMap.set(locale, {
      locale,
      name: null,
      description: null,
      otherNames: null,
      acronyms: null,
    });
  }

  // Merge all translation arrays
  for (const translationArray of translationArrays) {
    for (const translation of translationArray) {
      const existing = translationMap.get(translation.locale);
      if (existing) {
        translationMap.set(translation.locale, {
          ...existing,
          ...translation,
        });
      }
    }
  }

  // Return only locales that have at least one non-null value
  return Array.from(translationMap.values()).filter(
    (translation) =>
      translation.name ||
      translation.description ||
      translation.otherNames ||
      translation.acronyms,
  );
};

/**
 * Extracts string ID from SelectOption object
 * @param selectOption - Object with value property or string
 * @returns String ID or undefined
 */
const extractId = (
  selectOption: SelectOption | { value: string | null; label: string | null },
): string | undefined => {
  if (selectOption.value === null) {
    return undefined;
  }

  return selectOption.value;
};

/**
 * Maps InfrastructureFormSchema to InfrastructureInput (domain schema)
 * @param formData - Frontend form data
 * @returns Domain input object for API
 */
export const mapInfrastructurePostPayloadToInput = (
  formData: InfrastructureFormSchema,
): InfrastructureInput => {
  // Transform translatable fields to translation arrays
  const nameTranslations = transformTranslatableFieldToTranslations(
    'name',
    formData.name,
  );
  const descriptionTranslations = transformTranslatableFieldToTranslations(
    'description',
    formData.description,
  );
  const acronymTranslations = transformTranslatableFieldToTranslations(
    'acronyms',
    formData.acronym,
  );

  // Handle alias field as otherNames
  const otherNamesTranslations: Array<{
    locale: SupportedLocale;
    otherNames: string | null;
  }> = [];

  // If alias exists, create a translation entry for it
  if (formData.alias?.trim()) {
    // Since alias is a single string, we'll add it for both locales
    // This might need adjustment based on business requirements
    otherNamesTranslations.push(
      { locale: 'en', otherNames: formData.alias.trim() },
      { locale: 'fr', otherNames: formData.alias.trim() },
    );
  }

  // Merge all translations by locale
  const translations = mergeTranslationsByLocale(
    nameTranslations,
    descriptionTranslations,
    acronymTranslations,
    otherNamesTranslations,
  );

  // Map the main infrastructure fields
  return {
    // Required fields
    guidId: '',
    typeId: formData.infoType,
    statusId: formData.status,
    visibilityId: formData.visibility,

    // Optional fields
    unitId: extractId(formData.parentUnit),
    addressId: '', // Address handling would need separate logic
    website: formData.website || null,
    isFeatured: formData.highlight || false,
    modifiedBy: null,
    isActive: true, // Default to active for new infrastructures

    // Translations array
    translations,
  } satisfies InfrastructureInput;
};

/**
 * Extracts a single string value from translations for a specific field
 * Used for fields like alias that are stored as single strings in the form
 * @param translations - Array of translation objects from domain schema
 * @param fieldName - The field name to extract
 * @param preferredLocale - Preferred locale to extract (defaults to 'en')
 * @returns Single string value or empty string if not found
 */
const extractSingleValueFromTranslations = (
  translations: InfrastructureData['translations'],
  fieldName: 'name' | 'description' | 'otherNames' | 'acronyms',
  preferredLocale: SupportedLocale = 'en',
): string => {
  if (!translations || translations.length === 0) return '';

  // First try to find the preferred locale
  const preferredTranslation = translations.find(
    (t) => t.locale === preferredLocale,
  );
  if (preferredTranslation?.[fieldName]?.trim()) {
    return preferredTranslation[fieldName].trim();
  }

  // Fall back to any available translation
  for (const translation of translations) {
    const fieldValue = translation[fieldName];
    if (fieldValue?.trim()) {
      return fieldValue.trim();
    }
  }

  return '';
};

/**
 * Maps InfrastructureSchema (domain) to InfrastructureFormSchema (form)
 * @param infrastructure - Domain infrastructure data from API
 * @param currentLocale - Current locale for form initialization
 * @returns Form-compatible infrastructure data
 */
export const mapInfrastructureSchemaToForm = (
  infrastructure: InfrastructureData,
  currentLocale: SupportedLocale,
): InfrastructureFormSchema => {
  // Transform translations array to translatable field arrays
  const nameTranslations = transformTranslationsToTranslatableField(
    infrastructure.translations,
    'name',
  );
  const descriptionTranslations = transformTranslationsToTranslatableField(
    infrastructure.translations,
    'description',
  );
  const acronymTranslations = transformTranslationsToTranslatableField(
    infrastructure.translations,
    'acronyms',
  );

  // Extract alias from otherNames (single string field in form)
  const alias = extractSingleValueFromTranslations(
    infrastructure.translations,
    'otherNames',
    currentLocale,
  );

  // Ensure we have at least empty entries for the current locale if no translations exist
  const ensureCurrentLocaleEntry = (
    translations: Array<{ locale: string; value: string }>,
  ): Array<{ locale: string; value: string }> => {
    if (translations.length === 0) {
      return [{ locale: currentLocale, value: '' }];
    }

    // Check if current locale exists
    const hasCurrentLocale = translations.some(
      (t) => t.locale === currentLocale,
    );
    if (!hasCurrentLocale) {
      return [{ locale: currentLocale, value: '' }, ...translations];
    }

    return translations;
  };

  return {
    // Translatable fields
    name: ensureCurrentLocaleEntry(nameTranslations),
    description: ensureCurrentLocaleEntry(descriptionTranslations),
    acronym: ensureCurrentLocaleEntry(acronymTranslations),
    alias,

    // Basic infrastructure fields - now accessing from related entities
    infoType: infrastructure.type.id,
    status: infrastructure.status.id,
    visibility: infrastructure.visibility.id,
    website: infrastructure.website ?? '',
    highlight: infrastructure.isFeatured,

    // Optional fields with SelectOption format
    parentUnit: {
      value: infrastructure.unit ? infrastructure.unit.id : null,
      label: infrastructure.unit
        ? (infrastructure.unit.translations.find(
            (t) => t.locale === currentLocale,
          )?.name ?? infrastructure.unit.id)
        : null,
    },
    // Address handling - would need additional logic for full address mapping
    // For now, using default campus address
    address: CAMPUS_ADDRESS_DEFAULT_VALUE,

    // Arrays that would need additional data from related entities
    // These would typically be populated by separate API calls or joins
    affiliatedPersons: [],
    associatedFinancialProject: [],
    innovationLab: [],
    locationBuilding: [],
    sstManager: infrastructure.sstManagers.map((manager) => ({
      value: manager.id,
      label: `${manager.firstName} ${manager.lastName} `,
    })),

    jurisdiction: { value: null, label: null },
    scientificAndTechnicalManager: {
      scientificManager: infrastructure.scientificManagers.map((manager) => ({
        value: manager.id,
        label: `${manager.firstName} ${manager.lastName} `,
      })),
      technicalManager: infrastructure.operationalManagers.map((manager) => ({
        value: manager.id,
        label: `${manager.firstName} ${manager.lastName} `,
      })),
    },
  } satisfies InfrastructureFormSchema;
};

/**
 * Maps InfrastructureWithRelatedEquipments to infrastructure description header
 * @param infrastructure - Domain infrastructure data
 * @param locale - Current locale
 * @returns Infrastructure description header for display
 */
export const mapInfrastructureWithEquipmentsToDescriptionHeader = (
  infrastructure: InfrastructureWithRelatedEquipments,
  locale: SupportedLocale,
): InfrastructureDescription => {
  return {
    // Get localized name from translations
    nom: getLocalizedField(infrastructure.translations, 'name', locale),

    // Status information
    status: infrastructure.status.id as ResourceStatusType,
    statusText: getLocalizedField(
      infrastructure.status.translations,
      'name',
      locale,
    ),

    // Technical directors (operational managers in new schema)
    directeursTechnique: infrastructure.operationalManagers.map(
      (manager) => `${manager.firstName} ${manager.lastName}`,
    ),

    // Technical managers (scientific managers in new schema)
    responsablesTechnique: infrastructure.scientificManagers.map(
      (manager) => `${manager.firstName} ${manager.lastName}`,
    ),

    // Website URL
    url: infrastructure.website,

    // Address - simplified for now, would need address relation data
    address: infrastructure.addressId || undefined,

    // Other fields that would need additional data
    emplacement: null, // Would need address/location data
    pseudonyme: null, // Would need to be added to schema if needed
    telephone: null, // Would need to be added to schema if needed
  };
};

/**
 * Maps InfrastructureWithRelatedEquipments to infrastructure description details
 * @param infrastructure - Domain infrastructure data
 * @param locale - Current locale
 * @returns Infrastructure description details for display
 */
export const mapInfrastructureWithEquipmentsToDescriptionDetails = (
  infrastructure: InfrastructureWithRelatedEquipments,
  locale: SupportedLocale,
): InfrastructureDetails => {
  // Create EntityLocaleDictionary-like object from translations
  const descriptions: Record<string, string> = {};
  for (const translation of infrastructure.translations) {
    if (translation.description) {
      descriptions[translation.locale] = translation.description;
    }
  }

  return {
    // Localized descriptions
    descriptions:
      Object.keys(descriptions).length > 0 ? descriptions : undefined,

    // Unit information
    parentUnit: infrastructure.unit
      ? getLocalizedField(infrastructure.unit.translations, 'name', locale)
      : undefined,

    // SST managers
    responsablesSST: infrastructure.sstManagers.map(
      (manager) => `${manager.firstName} ${manager.lastName}`,
    ),

    // Fields that would need additional data or are not available in current schema
    jurisdiction: undefined, // Would need institution/establishment data
    emplacement: undefined, // Would need address/location data
    affiliated: [], // Would need affiliated persons data
    researcher: [], // Would need researcher data
    projects: '', // Would need project data
  };
};

/**
 * Maps minimal equipment data to EquipmentStructure for display in grid
 * @param equipment - Minimal equipment data from domain
 * @param locale - Current locale
 * @returns Equipment structure for display
 */
const mapMinimalEquipmentToStructure = (
  equipment: InfrastructureWithRelatedEquipments['equipments'][number],
  locale: SupportedLocale,
): EquipmentStructure => {
  const equipmentName =
    getLocalizedField(equipment.translations, 'name', locale) ??
    'Unknown Equipment';

  return {
    id: equipment.id,
    name: equipmentName,
    equipmentName: equipmentName,

    // Required fields with default values since minimal data doesn't include them
    infrastructureId: '', // Would need to be passed from parent infrastructure
    status: 'active' as ResourceStatusType, // Default status
    technicalManager: [], // Not available in minimal data

    // Optional fields that are not available in minimal equipment data
    decommissionDate: null,
    equipmentCategories: null,
    equipmentType: null,
    installationDate: null,
    location: null,
    manufacturer: null,
    model: null,
    parentInfrastructure: null,
    purchaseDate: null,
    statusText: null,
    type: null,
    updatedAt: null,
  };
};

/**
 * Maps InfrastructureWithRelatedEquipments to the format expected by Details component
 * @param infrastructure - Domain infrastructure data
 * @param locale - Current locale
 * @returns Mapped data for Details component
 */
export const mapInfrastructureWithEquipmentsToDetails = (
  infrastructure: InfrastructureWithRelatedEquipments,
  locale: SupportedLocale,
) => {
  return {
    descriptionHeader: mapInfrastructureWithEquipmentsToDescriptionHeader(
      infrastructure,
      locale,
    ),
    description: mapInfrastructureWithEquipmentsToDescriptionDetails(
      infrastructure,
      locale,
    ),
    equipments: infrastructure.equipments.map((equipment) => ({
      ...mapMinimalEquipmentToStructure(equipment, locale),
      infrastructureId: infrastructure.id, // Set the infrastructure ID from parent
      parentInfrastructure: getLocalizedField(
        infrastructure.translations,
        'name',
        locale,
      ),
    })),
  };
};
