import { EditPermission } from '@/app/[locale]/admin/permissions/[id]/editer/edit-permission';
import { getQueryClientOptions } from '@/constants/query-client';
import { getPermissionByIdOptions } from '@/hooks/admin/permissions/permissions.options';
import { redirect } from '@/lib/navigation';
import type { ResourcePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import { QueryClient } from '@tanstack/react-query';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export default async function EditPermissionPage({
  params,
}: ResourcePageParams) {
  const { id, locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: `/admin/permissions/${id}/editer` },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const permission = await queryClient.fetchQuery(
    getPermissionByIdOptions<'edit'>({ id, view: 'edit' }),
  );

  if (!permission) {
    return notFound();
  }

  return (
    <div className="container mx-auto py-10">
      <EditPermission id={id} initialData={permission} />
    </div>
  );
}
