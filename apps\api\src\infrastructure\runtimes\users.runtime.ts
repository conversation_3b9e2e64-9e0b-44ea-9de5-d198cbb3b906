import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  RolesRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  RolesServiceLive,
  UserPermissionsServiceLive,
  UsersServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const UserServicesLayer = Layer.mergeAll(
  // Core user services
  UsersRepositoryLive.Default,
  UsersServiceLive.Default,

  // User permissions dependencies
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  RolesRepositoryLive.Default,
  AccessTreeServiceLive.Default,
  RolesServiceLive.Default,
  UserPermissionsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const UsersRuntime = ManagedRuntime.make(UserServicesLayer);
