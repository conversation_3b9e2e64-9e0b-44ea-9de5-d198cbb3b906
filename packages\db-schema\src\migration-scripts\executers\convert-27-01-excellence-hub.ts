import { ExcellenceHubMigrationConverter } from '../converters/27-01-convert-excellence-hub-inserts';

async function convertExcellenceHubData() {
  try {
    // Create converter instance
    const converter = new ExcellenceHubMigrationConverter();

    // Convert the file
    await converter.convertFile();
  } catch (error) {
    console.error('Error converting excellence hub data:', error);
    process.exit(1);
  }
}

// Run the conversion
convertExcellenceHubData();
