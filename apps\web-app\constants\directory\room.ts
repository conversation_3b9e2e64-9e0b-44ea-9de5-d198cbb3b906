import { NULLABLE_SELECT_OPTION } from '@/constants/common';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';

export const initialColumnVisibility = {
  building: true,
  capacity: true,
  jurisdiction: true,
  localNumber: true,
  superficiy: true,
} as const;

export const roomFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const roomFormDefaultValues = (): RoomFormSchema => ({
  area: 0,
  building: NULLABLE_SELECT_OPTION,
  capacity: 0,
  categories: [],
  jurisdiction: NULLABLE_SELECT_OPTION,
  roomNumber: '',
  alias: '',
});
