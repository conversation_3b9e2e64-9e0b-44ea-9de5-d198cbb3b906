import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllRoomsOptions = <
  View extends CollectionViewParamType['view'],
>({
  locale,
  view,
}: CollectionOptionsParams<View>) =>
  getDirectoryListOptions({
    directoryListKey: 'local' as const,
    locale,
    view,
  });

export const getRoomByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) => getDirectoryByIdOptions({ directoryListKey: 'local', id, view } as const);
