import { isProtectedRoute } from '@/helpers/auth';
import { describe, expect, it } from 'vitest';

describe('isProtectedRoute', () => {
  describe('infrastructure routes', () => {
    it('should return true for infrastructure edit routes with ID', () => {
      expect(isProtectedRoute('/infrastructures/123/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/abc-def/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/I000001/edit')).toBe(true);
    });

    it('should return true for infrastructure editer routes with ID (French)', () => {
      expect(isProtectedRoute('/infrastructures/123/editer')).toBe(true);
      expect(isProtectedRoute('/infrastructures/abc-def/editer')).toBe(true);
      expect(isProtectedRoute('/infrastructures/I000001/editer')).toBe(true);
    });

    it('should return true for infrastructure add routes', () => {
      expect(isProtectedRoute('/infrastructures/add')).toBe(true);
    });

    it('should return true for infrastructure ajouter routes (French)', () => {
      expect(isProtectedRoute('/infrastructures/ajouter')).toBe(true);
    });

    it('should return false for infrastructure list routes', () => {
      expect(isProtectedRoute('/infrastructures')).toBe(false);
      expect(isProtectedRoute('/infrastructures/')).toBe(false);
    });

    it('should return false for infrastructure detail routes', () => {
      expect(isProtectedRoute('/infrastructures/123')).toBe(false);
      expect(isProtectedRoute('/infrastructures/I000001')).toBe(false);
    });
  });

  describe('equipment routes (equipements)', () => {
    it('should return true for equipements edit routes with ID', () => {
      expect(isProtectedRoute('/equipements/123/edit')).toBe(true);
      expect(isProtectedRoute('/equipements/E000001/edit')).toBe(true);
      expect(isProtectedRoute('/equipements/abc-def/edit')).toBe(true);
    });

    it('should return true for equipements editer routes with ID (French)', () => {
      expect(isProtectedRoute('/equipements/123/editer')).toBe(true);
      expect(isProtectedRoute('/equipements/E000001/editer')).toBe(true);
      expect(isProtectedRoute('/equipements/abc-def/editer')).toBe(true);
    });

    it('should return true for equipements add routes with ID', () => {
      expect(isProtectedRoute('/equipements/123/add')).toBe(true);
      expect(isProtectedRoute('/equipements/E000001/add')).toBe(true);
      expect(isProtectedRoute('/equipements/abc-def/add')).toBe(true);
    });

    it('should return true for equipements ajouter routes with ID (French)', () => {
      expect(isProtectedRoute('/equipements/123/ajouter')).toBe(true);
      expect(isProtectedRoute('/equipements/E000001/ajouter')).toBe(true);
      expect(isProtectedRoute('/equipements/abc-def/ajouter')).toBe(true);
    });

    it('should return false for equipements list routes', () => {
      expect(isProtectedRoute('/equipements')).toBe(false);
      expect(isProtectedRoute('/equipements/')).toBe(false);
    });

    it('should return false for equipements detail routes', () => {
      expect(isProtectedRoute('/equipements/123')).toBe(false);
      expect(isProtectedRoute('/equipements/E000001')).toBe(false);
    });
  });

  describe('equipment routes (equipments)', () => {
    it('should return true for equipments edit routes with ID', () => {
      expect(isProtectedRoute('/equipments/123/edit')).toBe(true);
      expect(isProtectedRoute('/equipments/E000001/edit')).toBe(true);
      expect(isProtectedRoute('/equipments/abc-def/edit')).toBe(true);
    });

    it('should return true for equipments editer routes with ID (French)', () => {
      expect(isProtectedRoute('/equipments/123/editer')).toBe(true);
      expect(isProtectedRoute('/equipments/E000001/editer')).toBe(true);
      expect(isProtectedRoute('/equipments/abc-def/editer')).toBe(true);
    });

    it('should return true for equipments add routes with ID', () => {
      expect(isProtectedRoute('/equipments/123/add')).toBe(true);
      expect(isProtectedRoute('/equipments/E000001/add')).toBe(true);
      expect(isProtectedRoute('/equipments/abc-def/add')).toBe(true);
    });

    it('should return true for equipments ajouter routes with ID (French)', () => {
      expect(isProtectedRoute('/equipments/123/ajouter')).toBe(true);
      expect(isProtectedRoute('/equipments/E000001/ajouter')).toBe(true);
      expect(isProtectedRoute('/equipments/abc-def/ajouter')).toBe(true);
    });

    it('should return false for equipments list routes', () => {
      expect(isProtectedRoute('/equipments')).toBe(false);
      expect(isProtectedRoute('/equipments/')).toBe(false);
    });

    it('should return false for equipments detail routes', () => {
      expect(isProtectedRoute('/equipments/123')).toBe(false);
      expect(isProtectedRoute('/equipments/E000001')).toBe(false);
    });
  });

  describe('bottin and directory routes', () => {
    it('should return true for all bottin routes', () => {
      // Root bottin routes
      expect(isProtectedRoute('/bottin')).toBe(true);
      expect(isProtectedRoute('/bottin/')).toBe(true);

      // Entity list routes
      expect(isProtectedRoute('/bottin/batiments')).toBe(true);
      expect(isProtectedRoute('/bottin/buildings')).toBe(true);
      expect(isProtectedRoute('/bottin/campus')).toBe(true);
      expect(isProtectedRoute('/bottin/etablissements')).toBe(true);
      expect(isProtectedRoute('/bottin/establishments')).toBe(true);
      expect(isProtectedRoute('/bottin/locaux')).toBe(true);
      expect(isProtectedRoute('/bottin/rooms')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturiers')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturers')).toBe(true);
      expect(isProtectedRoute('/bottin/personnes')).toBe(true);
      expect(isProtectedRoute('/bottin/people')).toBe(true);
      expect(isProtectedRoute('/bottin/projets-financement')).toBe(true);
      expect(isProtectedRoute('/bottin/financing-projects')).toBe(true);
      expect(isProtectedRoute('/bottin/unites')).toBe(true);
      expect(isProtectedRoute('/bottin/units')).toBe(true);

      // Entity detail routes
      expect(isProtectedRoute('/bottin/batiments/123')).toBe(true);
      expect(isProtectedRoute('/bottin/buildings/B000001')).toBe(true);
      expect(isProtectedRoute('/bottin/campus/C000001')).toBe(true);
      expect(isProtectedRoute('/bottin/etablissements/E000001')).toBe(true);
      expect(isProtectedRoute('/bottin/establishments/abc-def')).toBe(true);
      expect(isProtectedRoute('/bottin/locaux/L000001')).toBe(true);
      expect(isProtectedRoute('/bottin/rooms/R000001')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturiers/M000001')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturers/abc-def')).toBe(true);
      expect(isProtectedRoute('/bottin/personnes/P000001')).toBe(true);
      expect(isProtectedRoute('/bottin/people/abc-def')).toBe(true);
      expect(isProtectedRoute('/bottin/projets-financement/F000001')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/financing-projects/abc-def')).toBe(true);
      expect(isProtectedRoute('/bottin/unites/U000001')).toBe(true);
      expect(isProtectedRoute('/bottin/units/abc-def')).toBe(true);

      // Entity edit routes
      expect(isProtectedRoute('/bottin/batiments/123/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/batiments/123/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/buildings/B000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/buildings/B000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/campus/C000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/campus/C000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/etablissements/E000001/edit')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/etablissements/E000001/editer')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/establishments/abc-def/edit')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/establishments/abc-def/editer')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/locaux/L000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/locaux/L000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/rooms/R000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/rooms/R000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturiers/M000001/edit')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/manufacturiers/M000001/editer')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/manufacturers/abc-def/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/manufacturers/abc-def/editer')).toBe(
        true,
      );
      expect(isProtectedRoute('/bottin/personnes/P000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/personnes/P000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/people/abc-def/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/people/abc-def/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/projets-financement/F000001/edit')).toBe(
        true,
      );
      expect(
        isProtectedRoute('/bottin/projets-financement/F000001/editer'),
      ).toBe(true);
      expect(isProtectedRoute('/bottin/financing-projects/abc-def/edit')).toBe(
        true,
      );
      expect(
        isProtectedRoute('/bottin/financing-projects/abc-def/editer'),
      ).toBe(true);
      expect(isProtectedRoute('/bottin/unites/U000001/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/unites/U000001/editer')).toBe(true);
      expect(isProtectedRoute('/bottin/units/abc-def/edit')).toBe(true);
      expect(isProtectedRoute('/bottin/units/abc-def/editer')).toBe(true);

      // Non-existent bottin routes (should still be protected)
      expect(isProtectedRoute('/bottin/non-existent')).toBe(true);
      expect(isProtectedRoute('/bottin/invalid-entity/123')).toBe(true);
      expect(
        isProtectedRoute('/bottin/some-random-path/with/multiple/segments'),
      ).toBe(true);
    });

    it('should return true for all directory routes', () => {
      // Root directory routes
      expect(isProtectedRoute('/directory')).toBe(true);
      expect(isProtectedRoute('/directory/')).toBe(true);

      // Directory entity routes
      expect(isProtectedRoute('/directory/batiments')).toBe(true);
      expect(isProtectedRoute('/directory/buildings')).toBe(true);
      expect(isProtectedRoute('/directory/campus')).toBe(true);
      expect(isProtectedRoute('/directory/etablissements')).toBe(true);
      expect(isProtectedRoute('/directory/establishments')).toBe(true);
      expect(isProtectedRoute('/directory/locaux')).toBe(true);
      expect(isProtectedRoute('/directory/rooms')).toBe(true);
      expect(isProtectedRoute('/directory/manufacturiers')).toBe(true);
      expect(isProtectedRoute('/directory/manufacturers')).toBe(true);
      expect(isProtectedRoute('/directory/personnes')).toBe(true);
      expect(isProtectedRoute('/directory/people')).toBe(true);
      expect(isProtectedRoute('/directory/projets-financement')).toBe(true);
      expect(isProtectedRoute('/directory/financing-projects')).toBe(true);
      expect(isProtectedRoute('/directory/unites')).toBe(true);
      expect(isProtectedRoute('/directory/units')).toBe(true);

      // Directory detail and edit routes
      expect(isProtectedRoute('/directory/batiments/123')).toBe(true);
      expect(isProtectedRoute('/directory/batiments/123/edit')).toBe(true);
      expect(isProtectedRoute('/directory/batiments/123/editer')).toBe(true);
      expect(isProtectedRoute('/directory/buildings/B000001/edit')).toBe(true);
      expect(isProtectedRoute('/directory/buildings/B000001/editer')).toBe(
        true,
      );

      // Non-existent directory routes (should still be protected)
      expect(isProtectedRoute('/directory/non-existent')).toBe(true);
      expect(isProtectedRoute('/directory/invalid-entity/123')).toBe(true);
      expect(
        isProtectedRoute('/directory/some-random-path/with/multiple/segments'),
      ).toBe(true);
    });
  });

  describe('edge cases and non-protected routes', () => {
    it('should return false for root route', () => {
      expect(isProtectedRoute('/')).toBe(false);
    });

    it('should return false for empty string', () => {
      expect(isProtectedRoute('')).toBe(false);
    });

    it('should return false for similar but non-matching routes', () => {
      expect(isProtectedRoute('/infrastructure/123/edit')).toBe(false); // missing 's'
      expect(isProtectedRoute('/infrastructures/edit')).toBe(false); // missing ID
      expect(isProtectedRoute('/infrastructures/123/editing')).toBe(false); // wrong action
      expect(isProtectedRoute('/equipement/123/edit')).toBe(false); // missing 's'
      expect(isProtectedRoute('/equipment/123/edit')).toBe(false); // missing 's'
      // Note: All bottin routes are now protected, so these would return true
      expect(isProtectedRoute('/bottin/edit')).toBe(true); // all bottin routes protected
      expect(isProtectedRoute('/bottin/123/edit')).toBe(true); // all bottin routes protected
      expect(isProtectedRoute('/bottin/invalid-entity/123/edit')).toBe(true); // all bottin routes protected
      expect(isProtectedRoute('/bottin/batiment/123/edit')).toBe(true); // all bottin routes protected
      expect(isProtectedRoute('/bottin/building/123/edit')).toBe(true); // all bottin routes protected
    });

    it('should return false for routes with extra segments', () => {
      expect(isProtectedRoute('/infrastructures/123/edit/extra')).toBe(false);
      expect(isProtectedRoute('/equipements/123/edit/extra')).toBe(false);
      expect(isProtectedRoute('/equipments/123/edit/extra')).toBe(false);
      // Note: All bottin routes are now protected, so these would return true
      expect(isProtectedRoute('/bottin/batiments/123/edit/extra')).toBe(true);
      expect(isProtectedRoute('/bottin/buildings/123/edit/extra')).toBe(true);
      expect(isProtectedRoute('/bottin/personnes/123/edit/extra')).toBe(true);
    });

    it('should return true for protected routes with query parameters', () => {
      expect(isProtectedRoute('/infrastructures/123/edit?param=value')).toBe(
        true,
      );
      expect(isProtectedRoute('/equipements/123/edit?param=value')).toBe(true);
      expect(isProtectedRoute('/equipments/123/edit?param=value')).toBe(true);
      expect(isProtectedRoute('/bottin/batiments/123/edit?param=value')).toBe(
        true,
      );
    });

    it('should return true for protected routes with hash fragments', () => {
      expect(isProtectedRoute('/infrastructures/123/edit#section')).toBe(true);
      expect(isProtectedRoute('/equipements/123/edit#section')).toBe(true);
      expect(isProtectedRoute('/equipments/123/edit#section')).toBe(true);
      expect(isProtectedRoute('/bottin/batiments/123/edit#section')).toBe(true);
    });

    it('should return true for protected routes with both query parameters and hash fragments', () => {
      expect(
        isProtectedRoute('/infrastructures/123/edit?param=value#section'),
      ).toBe(true);
      expect(
        isProtectedRoute('/equipements/123/edit?param=value#section'),
      ).toBe(true);
      expect(isProtectedRoute('/equipments/123/edit?param=value#section')).toBe(
        true,
      );
      expect(
        isProtectedRoute('/bottin/batiments/123/edit?param=value#section'),
      ).toBe(true);
    });

    it('should return false for non-protected routes with query parameters', () => {
      expect(isProtectedRoute('/infrastructures?param=value')).toBe(false);
      expect(isProtectedRoute('/equipements?param=value')).toBe(false);
      // Note: All bottin routes are now protected, so this would return true
      expect(isProtectedRoute('/bottin/batiments?param=value')).toBe(true);
    });

    it('should return false for other common routes', () => {
      expect(isProtectedRoute('/login')).toBe(false);
      expect(isProtectedRoute('/logout')).toBe(false);
      expect(isProtectedRoute('/profile')).toBe(false);
      expect(isProtectedRoute('/settings')).toBe(false);
      expect(isProtectedRoute('/about')).toBe(false);
      expect(isProtectedRoute('/contact')).toBe(false);
    });
  });

  describe('pattern matching behavior', () => {
    it('should handle different ID formats correctly', () => {
      // Numeric IDs
      expect(isProtectedRoute('/infrastructures/123/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/999999/edit')).toBe(true);

      // Alphanumeric IDs
      expect(isProtectedRoute('/infrastructures/abc123/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/I000001/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/E000001/edit')).toBe(true);

      // IDs with hyphens and underscores
      expect(isProtectedRoute('/infrastructures/abc-def/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/abc_def/edit')).toBe(true);

      // Should not match IDs with forward slashes (as they would create additional path segments)
      expect(isProtectedRoute('/infrastructures/abc/def/edit')).toBe(false);
    });

    it('should be case insensitive', () => {
      expect(isProtectedRoute('/Infrastructures/123/edit')).toBe(true);
      expect(isProtectedRoute('/infrastructures/123/Edit')).toBe(true);
      expect(isProtectedRoute('/INFRASTRUCTURES/123/EDIT')).toBe(true);
      expect(isProtectedRoute('/BOTTIN/BATIMENTS/123/EDIT')).toBe(true);
      expect(isProtectedRoute('/Equipements/123/Editer')).toBe(true);
    });
  });
});
