import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { MediaTypeMigrationConverter } from '../converters/02-01-convert-media-type-inserts';

async function convertMediaTypeData() {
  // Input file is in the data directory
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new MediaTypeMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertMediaTypeData().catch(console.error);
