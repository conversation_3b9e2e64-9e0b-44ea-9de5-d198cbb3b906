'use client';
import { useToast } from '@/components/hooks/use-toast';
import { useEditEntity } from '@/hooks/directory/useEditEntity';
import { useRouter } from '@/lib/navigation';
import { mapEntitiesToRouteWithLocale } from '@/services/mappers/bottin/entities';
import type {
  EntityPayload,
  EntitySchemaFull,
} from '@/types/directory/directory';
import type { DirectoryEntity } from '@rie/domain/types';
import { useTranslations } from 'next-intl';

export const editEntityMutation = <
  K extends DirectoryEntity,
  T extends EntitySchemaFull,
>(
  directoryEntity: K,
  id: string,
  mapper: (data: T) => EntityPayload<K>,
) => {
  const tCommon = useTranslations('common');
  const router = useRouter();
  const { toast } = useToast();
  const route = mapEntitiesToRouteWithLocale(directoryEntity);
  const editMutation = useEditEntity(directoryEntity, id, (id) => {
    router.push({
      params: { id },
      pathname: route,
    });
  });

  return async (data: T) => {
    try {
      const payload = mapper(data);
      await editMutation.mutateAsync(payload);
      toast({
        description: tCommon('notifications.success', {
          action: tCommon('actions.updated'),
          resource: tCommon(`resources.${directoryEntity as DirectoryEntity}`),
        }),
        title: tCommon('notifications.successTitle'),
        variant: 'success',
      });
    } catch (error) {
      toast({
        description: tCommon('notifications.errors.onUpdateResource', {
          resource: tCommon(`resources.${directoryEntity as DirectoryEntity}`),
        }),
        title: tCommon('notifications.errorTitle'),
        variant: 'destructive',
      });
    }
  };
};
