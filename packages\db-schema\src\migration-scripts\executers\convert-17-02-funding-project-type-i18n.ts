import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { FundingProjectTypeI18nMigrationConverter } from '../converters/17-02-convert-funding-project-type-i18n-inserts';

async function convertFundingProjectTypeI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new FundingProjectTypeI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertFundingProjectTypeI18nData().catch(console.error);
