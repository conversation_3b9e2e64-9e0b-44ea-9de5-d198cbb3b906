import type { CampusFormRequestDTO } from '@rie/api-contracts';
import { Campus } from '@rie/domain/aggregates';
import { CampusNotFoundError } from '@rie/domain/errors';
import type { CollectionViewType, ResourceViewType } from '@rie/domain/types';
import {
  CampusTranslation,
  CampusTranslationCollection,
} from '@rie/domain/value-objects';
import { CampusesRepositoryLive, InstitutionsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as Schema from 'effect/Schema';
import { CampusFormToDomainInput } from '../mappers';
import {
  CampusEditClientToDomainInput,
  serializeCampusToDetail,
  serializeCampusToEditClient,
  serializeCampusToFormEdit,
  serializeCampusToListItem,
  serializeCampusToSelectOption,
} from '../serializers';

// Type helper for campus domain input to avoid circular references
type CampusDomainInput = {
  sadId: string | null;
  institutionId: string;
  translations: Array<{ locale: string; name: string | null }>;
};

export class CampusesServiceLive extends Effect.Service<CampusesServiceLive>()(
  'CampusesServiceLive',
  {
    dependencies: [CampusesRepositoryLive.Default, InstitutionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllCampuses = (params: {
        locale: string;
        fallbackLocale: string;
        view: CollectionViewType;
      }) =>
        Effect.gen(function* () {
          const tStart = performance.now();
          const repo = yield* CampusesRepositoryLive;
          const campuses = yield* repo.findAllCampuses();
          const afterFetch = performance.now();
          console.log(
            `[Service][Campuses] fetched ${campuses.length} campuses in ${afterFetch - tStart} ms (view=${params.view}, locale=${params.locale}, fallback=${params.fallbackLocale})`,
          );

          // Serialize based on view type
          switch (params.view) {
            case 'list': {
              // Build institutionId -> localized name map from institutions_i18n
              const institutionsRepo = yield* InstitutionsRepositoryLive;
              const institutions = yield* institutionsRepo.findAllInstitutions();
              type Institution = {
                id: string;
                translations?: Array<{ locale: string; name: string | null }>;
              };
              const institutionNameById = new Map<string, string>();
              for (const inst of institutions as unknown as Institution[]) {
                const translations = inst.translations ?? [];
                const preferred = translations.find(
                  (t) => t.locale === params.locale && !!t.name && t.name.trim() !== '',
                );
                const fallback = translations.find(
                  (t) => t.locale === params.fallbackLocale && !!t.name && t.name.trim() !== '',
                );
                const anyName = translations.find((t) => !!t.name && t.name.trim() !== '');
                const resolved = preferred?.name ?? fallback?.name ?? anyName?.name;
                if (resolved) {
                  institutionNameById.set(inst.id, resolved);
                }
              }

              const result = yield* Effect.all(
                campuses.map((campus) =>
                  Effect.all([
                    serializeCampusToListItem(
                      campus,
                      params.locale,
                      params.fallbackLocale,
                    ),
                    campus.toRaw(),
                  ]).pipe(
                    Effect.map(([dto, raw]) => ({
                      ...dto,
                      jurisdiction:
                        institutionNameById.get(raw.institutionId) ?? null,
                    })),
                  ),
                ),
              );
              console.log(
                `[Service][Campuses] serialized list ${result.length} items in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'select': {
              const result = yield* Effect.all(
                campuses.map((campus) =>
                  serializeCampusToSelectOption(
                    campus,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Campuses] serialized select ${result.length} options in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'grid':
              return campuses;
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const getCampusById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) =>
        Effect.gen(function* () {
          const repo = yield* CampusesRepositoryLive;
          const campus = yield* repo.findCampusById(params.id);
          if (!campus) {
            return yield* Effect.fail(
              new CampusNotFoundError({ id: params.id }),
            );
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              return yield* serializeCampusToDetail(
                campus,
                params.locale,
                params.fallbackLocale,
              );
            }
            case 'edit': {
              const campusData = yield* campus.toRaw();
              // Resolve jurisdiction label from institutions_i18n
              let jurisdictionLabel: string | null = null;
              if (campusData.institutionId) {
                const institutionsRepo = yield* InstitutionsRepositoryLive;
                const institution = yield* institutionsRepo.findInstitutionById(
                  campusData.institutionId,
                );
                if (institution && (institution as unknown as { translations?: Array<{ locale: string; name: string | null }> }).translations) {
                  const translations = (institution as unknown as {
                    translations: Array<{
                      locale: string;
                      name: string | null;
                    }>
                  }).translations;
                  const preferred = translations.find(
                    (t) => t.locale === params.locale && !!t.name && t.name.trim() !== '',
                  );
                  const fallback = translations.find(
                    (t) => t.locale === params.fallbackLocale && !!t.name && t.name.trim() !== '',
                  );
                  const anyName = translations.find(
                    (t) => !!t.name && t.name.trim() !== '',
                  );
                  jurisdictionLabel = preferred?.name ?? fallback?.name ?? anyName?.name ?? null;
                }
              }

              return yield* serializeCampusToEditClient(campus, jurisdictionLabel);
            }
            default: {
              // TypeScript exhaustiveness check
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const createCampus = (params: {
        campusDto: CampusFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Map the incoming DTO to a clean domain input shape
          const domainInput = yield* Schema.decode(CampusFormToDomainInput)(
            params.campusDto,
          );

          // 2. For new campus creation, create translation VOs from input data
          // We create minimal translation objects that will get proper IDs during save
          const translationVOs = yield* Effect.all(
            domainInput.translations.map((t) => {
              // Create a translation object with minimal required fields
              const translationData = {
                id: '', // Empty string, will be filled by DB
                dataId: '', // Empty string, will be filled by repository
                locale: t.locale,
                name: t.name,
              };
              return CampusTranslation.create(translationData);
            }),
          );

          const translationCollection =
            yield* CampusTranslationCollection.create(translationVOs);

          // 3. Create the Campus aggregate
          const campus = yield* Campus.create({
            ...domainInput,
            modifiedBy: params.userId,
            translations: translationCollection,
          });

          // 4. Save the new aggregate
          const repo = yield* CampusesRepositoryLive;
          const savedCampus = yield* repo.save(campus);

          // 5. Serialize the result for the API response
          return yield* pipe(
            savedCampus.toRaw(),
            Effect.flatMap((raw) => serializeCampusToFormEdit(raw)),
          );
        });

      const updateCampus = ({
        id,
        campusDto,
        userId,
      }: {
        id: string;
        campusDto: CampusFormRequestDTO | {
          id?: string;
          jurisdiction: { value: string | null; label: string | null };
          pseudonym: string;
          name: Array<{ locale: string; value: string }>;
        };
        userId: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* CampusesRepositoryLive;
          console.log('[Service][Campuses][Update] Incoming payload received');

          // 1. Fetch the existing aggregate
          const existingCampus = yield* repo.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }

          // 2. Map the DTO via serializer (supports both API contract and client edit payload)
          const domainInputRaw =
            'jurisdiction' in campusDto
              ? (yield* Schema.decode(CampusEditClientToDomainInput)(
                campusDto as {
                  id?: string;
                  jurisdiction: { value: string | null; label: string | null };
                  pseudonym: string;
                  name: Array<{ locale: string; value: string }>;
                },
              ))
              : (yield* Schema.decode(CampusFormToDomainInput)(
                campusDto as CampusFormRequestDTO,
              ));

          // Normaliser sadId pour éviter undefined et convertir translations en mutable
          const domainInput: CampusDomainInput = {
            ...domainInputRaw,
            sadId: domainInputRaw.sadId ?? null,
            translations: [...domainInputRaw.translations] as Array<{ locale: string; name: string | null }>,
          };
          console.log('[Service][Campuses][Update] domainInput mapped');

          // 3. Merge existing translations with new translations
          const existingCampusData = yield* existingCampus.toRaw();
          const existingTranslations = existingCampusData.translations;

          // Create a map of existing translations by locale for efficient lookup
          const existingTranslationsMap = new Map(
            existingTranslations.map(t => [t.locale, t])
          );

          // Start with a copy of existing translations
          const mergedTranslations = new Map(existingTranslationsMap);

          // Update or add new translations
          for (const newTranslation of domainInput.translations) {
            if (existingTranslationsMap.has(newTranslation.locale)) {
              // Update existing translation, preserving id and dataId
              const existing = existingTranslationsMap.get(newTranslation.locale);
              if (existing) {
                mergedTranslations.set(newTranslation.locale, {
                  ...existing,
                  name: newTranslation.name,
                });
              }
            } else {
              // Add new translation - note: id will be generated by the database
              // so we only include it in the domain object for type compatibility
              mergedTranslations.set(newTranslation.locale, {
                id: '', // Temporary id, will be generated by DB
                locale: newTranslation.locale,
                name: newTranslation.name,
                dataId: existingCampusData.id,
              });
            }
          }

          // Convert to array and create translation VOs
          const allTranslationsData = Array.from(mergedTranslations.values());
          const translationVOs = yield* Effect.all(
            allTranslationsData.map((t) => CampusTranslation.create(t)),
          );
          const translationCollection =
            yield* CampusTranslationCollection.create(translationVOs);

          // 6. Call the update method on the existing aggregate
          const updatedDomainCampus = yield* existingCampus.update({
            sadId: domainInput.sadId ?? null,
            institutionId: domainInput.institutionId,
            isActive: true,
            modifiedBy: userId,
            translations: translationCollection,
          });

          // 4. Save the updated aggregate
          const savedCampus = yield* repo.save(updatedDomainCampus);
          console.log('[Service][Campuses][Update] Saved campus id:', (yield* savedCampus.toRaw()).id);

          // 5. Serialize the result for the API response
          return yield* pipe(
            savedCampus.toRaw(),
            Effect.flatMap((raw) => serializeCampusToFormEdit(raw)),
          );
        });

      const deleteCampus = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* CampusesRepositoryLive;
          const existingCampus = yield* repo.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }
          const result = yield* repo.deleteCampus(id);
          return result.length > 0;
        });

      return {
        getAllCampuses,
        getCampusById,
        createCampus,
        updateCampus,
        deleteCampus,
      } as const;
    }),
  },
) { }
