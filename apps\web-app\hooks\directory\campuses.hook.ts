import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import {
  createDirectory,
  deleteDirectory,
  updateDirectory,
} from '@/services/directory/directory-list.service';
import type { CampusEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateCampus = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<CampusEdit, Error, CampusFormSchema>({
    mutationFn: async (payload) =>
      (await createDirectory({
        directoryListKey: 'campus',
        payload,
      })) as unknown as CampusEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'campus'],
      });
      toast({
        title: 'Success',
        description: 'Campus created successfully',
        variant: 'success',
      });
      router.push('/bottin/campus');
    },
  });
};

export const useUpdateCampus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    CampusEdit,
    Error,
    { id: string; payload: CampusFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateDirectory({
        directoryListKey: 'campus',
        id,
        payload,
      })) as unknown as CampusEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'campus'],
      });
      toast({
        title: 'Success',
        description: 'Campus updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeleteCampus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteDirectory({ directoryListKey: 'campus', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'campus'],
      });
      toast({
        title: 'Success',
        description: 'Campus deleted successfully',
        variant: 'success',
      });
    },
  });
};
