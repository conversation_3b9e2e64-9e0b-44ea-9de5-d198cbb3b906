import { handleEffectError } from '@/api/v2/utils/error-handler';
import { ControlledListsRuntime } from '@/infrastructure/runtimes/controlled-lists.runtime';
import {
  ControlledListsQuerySchema,
  ControlledListsResponseSchema,
} from '@rie/domain/schemas';
import { ControlledListsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Controlled Lists Route
export const getControlledListsRoute = describeRoute({
  description: 'Obtient des listes contrôlées pour plusieurs entités',
  operationId: 'getControlledLists',
  parameters: [
    {
      name: 'entities',
      in: 'query',
      required: true,
      schema: resolver(Schema.Array(Schema.String)),
      description:
        'Liste des entités à récupérer (ex: equipmentStatuses,equipmentCategories)',
      explode: false,
      style: 'form',
    },
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(
        Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
      ),
      description: 'Type de vue (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(
        Schema.Union(Schema.Literal('fr'), Schema.Literal('en')),
      ),
      description: 'Locale pour les traductions',
    },
    {
      name: 'limit',
      in: 'query',
      required: false,
      schema: resolver(Schema.NumberFromString),
      description: "Limite du nombre d'éléments (-1 pour tous)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(ControlledListsResponseSchema),
        },
      },
      description: 'Listes contrôlées retournées avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Paramètres non valides',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Listes contrôlées'],
});

// Create the router
const controlledListsRoute = new Hono();

// Get controlled lists
controlledListsRoute.get(
  '/',
  getControlledListsRoute,
  async (ctx) => {
    // Handle entities parameter manually to support comma-separated values
    const rawEntities = ctx.req.query('entities');
    const view = ctx.req.query('view') || 'list';
    const locale = ctx.req.query('locale') || 'fr';
    const limitStr = ctx.req.query('limit');

    if (!rawEntities) {
      return ctx.json({ error: 'entities parameter is required' }, 400);
    }

    // Parse entities - handle both comma-separated string and array
    let entities: string[];
    try {
      if (typeof rawEntities === 'string') {
        entities = rawEntities.split(',').map(e => e.trim()).filter(Boolean);
      } else if (Array.isArray(rawEntities)) {
        entities = rawEntities;
      } else {
        entities = [rawEntities];
      }
    } catch (error) {
      return ctx.json({ error: 'Invalid entities format' }, 400);
    }

    // Parse limit - keep as string for NumberFromString schema validation
    const limitValue: string | undefined = limitStr || undefined;
    if (limitStr) {
      const parsedLimit = Number(limitStr);
      if (Number.isNaN(parsedLimit)) {
        return ctx.json({ error: 'Invalid limit value' }, 400);
      }
      // Keep limitValue as string for schema validation
    }

    // Validate the parsed data using the schema
    const result = Schema.decodeUnknownEither(ControlledListsQuerySchema)({
      entities,
      view,
      locale,
      limit: limitValue,
    });

    if (result._tag === 'Left') {
      console.error('Validation error:', result.left);
      return ctx.json({ error: 'Invalid parameters', details: result.left }, 400);
    }

    const validatedQuery = result.right;

    const program = Effect.gen(function* () {
      const controlledListsService = yield* ControlledListsServiceLive;
      return yield* controlledListsService.getControlledLists(validatedQuery);
    });

    const effectResult = await ControlledListsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, effectResult);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(effectResult)) {
      // Transform the service response to match frontend expectations
      // Service returns: [{ entity: "roomCategories", data: [...] }]
      // Frontend expects: { "roomCategories": [...] }
      const transformedResponse = effectResult.value.reduce((acc, item) => {
        acc[item.entity] = item.data;
        return acc;
      }, {} as Record<string, Array<{ value: string; label: string }>>);

      return ctx.json(transformedResponse);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { controlledListsRoute };
