import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
  VendorsRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  UserPermissionsServiceLive,
  VendorsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const VendorServicesLayer = Layer.mergeAll(
  VendorsRepositoryLive.Default,
  VendorsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const VendorsRuntime = ManagedRuntime.make(VendorServicesLayer);
