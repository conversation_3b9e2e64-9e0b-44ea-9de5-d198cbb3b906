# Architecture Overview

This document provides a high-level overview of the RIE (Research Infrastructure and Equipment) management system architecture.

## 🏗️ System Architecture

RIE is built as a modern, scalable TypeScript monorepo designed for the University of Montreal's research infrastructure management needs.

### High-Level Architecture

```
 ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
 │   Web Browser   │    │   Mobile App    │    │  External APIs  │
 │   (Next.js)     │    │   (Future)      │    │                 │
 └─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
           │                      │                      │
           └──────────────────────┼──────────────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      Load Balancer        │
                    │      (Future)             │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │      (Hono + OpenAPI)     │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│   Auth Service    │   │  Business Logic   │   │  External APIs    │
│  (Better Auth)    │   │   (Effect-ts)     │   │   (Future)        │
└─────────┬─────────┘   └─────────┬─────────┘   └─────────┬─────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      Database Layer       │
                    │   (Drizzle ORM + PG)      │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      PostgreSQL           │
                    │      Database             │
                    └───────────────────────────┘
```

## 🎯 Core Principles

### 1. Domain-Driven Design (DDD)

- Business logic is centralized in the domain layer
- Clear separation between business rules and technical concerns
- Domain models represent real-world concepts

### 2. Clean Architecture

- Dependencies point inward toward the domain
- Infrastructure details are isolated from business logic
- Easy to test and maintain

### 3. Functional Programming

- Using Effect-ts for pure, composable functions
- Immutable data structures
- Type-safe error handling

### 4. Type Safety

- TypeScript throughout the entire stack
- Runtime type validation with schemas
- Compile-time guarantees

### 5. Monorepo Benefits

- Shared code and types across applications
- Consistent tooling and standards
- Atomic changes across multiple packages

## 📦 Technology Stack

### Frontend

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/UI
- **State Management**: Zustand + TanStack Query
- **Forms**: React Hook Form + Zod validation
- **Testing**: Vitest + React Testing Library + Playwright
- **Internationalization**: next-intl (French/English)

### Backend

- **Runtime**: Bun
- **Framework**: Hono (lightweight, edge-ready)
- **Language**: TypeScript
- **Effect System**: Effect-ts (functional programming)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth
- **API Documentation**: OpenAPI/Swagger
- **Testing**: Vitest

### Database

- **Primary**: PostgreSQL
- **ORM**: Drizzle (type-safe, performant)
- **Migrations**: Drizzle migrations
- **Schema**: Multi-schema (auth, main business logic)

### DevOps & Tooling

- **Package Manager**: pnpm with workspaces
- **Build System**: Turborepo
- **Code Quality**: Biome (linting + formatting)
- **Version Control**: Git with conventional commits
- **Containerization**: Docker + Docker Compose
- **CI/CD**: GitHub Actions (future)

## 🏢 Monorepo Structure

```
rie-fullstack/
├── apps/                          # Applications
│   ├── api/                       # Hono API server
│   ├── web-app/                   # Next.js frontend
│   └── web-app-e2e/              # Playwright E2E tests
│
├── packages/                      # Shared packages
│   ├── auth/                      # Authentication utilities
│   ├── db-schema/                 # Database schemas & migrations
│   ├── domain/                    # Business logic & types
│   ├── postgres-db/               # Database connection utilities
│   ├── constants/                 # Shared constants
│   ├── utils/                     # Utility functions
│   ├── biome-config/              # Linting configuration
│   └── typescript-config/         # TypeScript configurations
│
├── docs/                          # Documentation
├── tests/                         # Integration test data
└── docker-compose.yml             # Development services
```

## 🔄 Data Flow

### Request Flow

1. **Client Request**: Browser/mobile app makes HTTP request
2. **API Gateway**: Hono router receives and validates request
3. **Authentication**: Better Auth middleware validates user
4. **Authorization**: Permission system checks user access
5. **Business Logic**: Effect-ts use cases process the request
6. **Data Access**: Repository layer queries database via Drizzle
7. **Response**: Serialized data returned to client

### State Management Flow

1. **Server State**: TanStack Query manages API data
2. **Client State**: Zustand handles UI state
3. **Form State**: React Hook Form manages form data
4. **URL State**: Next.js router manages navigation state

## 🔐 Security Architecture

### Authentication Flow

1. User provides credentials
2. Better Auth validates and creates session
3. JWT token issued with user context
4. Token included in subsequent requests
5. Middleware validates token and extracts user info

### Authorization Model

- **Role-Based Access Control (RBAC)**
- **Users** → **Roles** → **Permission Groups** → **Permissions**
- **Context-Aware**: Permissions can be scoped to specific resources
- **Hierarchical**: Permissions can inherit from parent contexts

### Security Measures

- HTTPS enforcement
- CORS configuration
- Input validation and sanitization
- SQL injection prevention (parameterized queries)
- XSS protection
- CSRF protection

## 📊 Performance Considerations

### Frontend Performance

- **Server-Side Rendering**: Next.js App Router
- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Next.js Image component
- **Caching**: TanStack Query for API responses
- **Bundle Analysis**: Webpack Bundle Analyzer

### Backend Performance

- **Lightweight Framework**: Hono for minimal overhead
- **Connection Pooling**: PostgreSQL connection pooling
- **Query Optimization**: Drizzle ORM with raw SQL when needed
- **Caching Strategy**: Redis (future implementation)
- **Database Indexing**: Strategic index placement

### Database Performance

- **Optimized Queries**: Avoid N+1 problems
- **Proper Indexing**: On frequently queried columns
- **Connection Pooling**: Efficient connection management
- **Query Analysis**: Regular EXPLAIN ANALYZE reviews

## 🧪 Testing Strategy

### Testing Pyramid

1. **Unit Tests** (Base): Pure functions, utilities, business logic
2. **Integration Tests** (Middle): API endpoints, database operations
3. **E2E Tests** (Top): User workflows, critical paths

### Testing Tools

- **Unit/Integration**: Vitest with MSW for API mocking
- **Component Testing**: React Testing Library
- **E2E Testing**: Playwright
- **Visual Testing**: Storybook
- **Performance Testing**: Lighthouse CI

## 🚀 Deployment Architecture

### Development Environment

- Local development with Docker Compose
- Hot reloading for both frontend and backend
- Local PostgreSQL instance

### Production Environment (Future)

- Container orchestration (Kubernetes/Docker Swarm)
- Load balancing
- Database clustering
- CDN for static assets
- Monitoring and logging

## 🔮 Future Considerations

### Scalability

- Microservices architecture (if needed)
- Event-driven architecture
- Caching layer (Redis)
- Database sharding/read replicas

### Features

- Real-time updates (WebSockets)
- Mobile application
- Advanced analytics
- Integration with external systems

### DevOps

- CI/CD pipelines
- Infrastructure as Code
- Monitoring and alerting
- Automated testing in CI

---

**Next Steps**:

- [Monorepo Structure Details](./monorepo-structure.md)
- [API Architecture Deep Dive](./api-architecture.md)
- [Frontend Architecture Details](./frontend-architecture.md)
- [Database Design](./database-design.md)
