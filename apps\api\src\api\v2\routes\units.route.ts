import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UnitsRuntime } from '@/infrastructure/runtimes/units.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  SelectOptionDtoSchema,
  UnitDetailResponseDtoSchema,
  UnitEditResponseDtoSchema,
  UnitFormRequestDtoSchema,
  UnitListItemDtoSchema,
} from '@rie/api-contracts';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { UnitsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllUnitsRoute = describeRoute({
  description: 'Lister toutes les unités',
  operationId: 'getAllUnits',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(UnitListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Unités retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const getUnitByIdRoute = describeRoute({
  description: 'Obtenir une unité par ID',
  operationId: 'getUnitById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue: detail ou edit',
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              UnitDetailResponseDtoSchema,
              UnitEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Unité trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const createUnitRoute = describeRoute({
  description: 'Créer une unité',
  operationId: 'createUnit',
  parameters: [
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UnitFormRequestDtoSchema),
        example: {
          id: 'unit_123',
          isActive: true,
          type: { value: 'unit_type_123', label: 'Department' },
          parent: { value: 'parent_unit_123', label: 'Parent Unit' },
          names: [
            { locale: 'fr', value: 'Département Informatique' },
            { locale: 'en', value: 'Computer Science Department' },
          ],
          description: [
            { locale: 'fr', value: 'Description du département' },
            { locale: 'en', value: 'Department description' },
          ],
          acronyms: [
            { locale: 'fr', value: 'DI' },
            { locale: 'en', value: 'CS' },
          ],
          otherNames: [
            { locale: 'fr', value: null },
            { locale: 'en', value: null },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(UnitDetailResponseDtoSchema),
        },
      },
      description: 'Unité créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const updateUnitRoute = describeRoute({
  description: 'Mettre à jour une unité',
  operationId: 'updateUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UnitFormRequestDtoSchema),
        example: {
          guidId: 'updated_unit_guid_123',
          type: { value: 'updated_unit_type_123', label: 'Updated Department' },
          parent: {
            value: 'updated_parent_unit_123',
            label: 'Updated Parent Unit',
          },
          translations: [
            {
              locale: 'fr',
              name: 'Département Informatique Mise à Jour',
              description: 'Description mise à jour',
              otherNames: null,
              acronyms: 'DI-MAJ',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UnitEditResponseDtoSchema),
        },
      },
      description: 'Unité mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const deleteUnitRoute = describeRoute({
  description: 'Supprimer une unité',
  operationId: 'deleteUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Unité supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

const unitsRoute = new Hono<{
  Variables: HonoVariables;
}>();

unitsRoute.get(
  '/',
  getAllUnitsRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.getAllUnits({
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('unit:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Units][GET /] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Units][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Units][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.get(
  '/:id',
  getUnitByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
    }),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.getUnitById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('unit:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.post(
  '/',
  createUnitRoute,
  validator('json', UnitFormRequestDtoSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.createUnit({
        unitDto: body,
        userId: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('unit:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.put(
  '/:id',
  updateUnitRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', UnitFormRequestDtoSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.updateUnit({
        id,
        unitDto: body,
        userId: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('unit:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.delete(
  '/:id',
  deleteUnitRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.deleteUnit(id);
    }).pipe(
      withPolicy(checkPermission('unit:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Unit deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { unitsRoute };

