import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    SelectOptionDtoSchema,
} from '../common';

/**
 * Describes the entire request body for creating or updating a building.
 * This is the shape of the data the client will send to the API.
 **/

export const BuildingFormRequestDtoSchema = Schema.Struct({
    id: Schema.optional(Schema.String),
    isActive: Schema.optional(Schema.Boolean),
    sadId: Schema.optional(Schema.NullOr(Schema.String)),
    campus: SelectOptionDtoSchema,
    jurisdiction: SelectOptionDtoSchema,
    name: Schema.Array(OptionalLocalizedFieldDtoSchema),
    alias: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type BuildingFormRequestDTO = Schema.Schema.Type<
    typeof BuildingFormRequestDtoSchema
>;