'use client';

import { PermissionForm } from '@/app/[locale]/admin/permissions/(forms)/permission-form';
import type { PermissionFormSchema } from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { useUpdatePermission } from '@/hooks/admin/permissions/permissions.hook';
import type { PermissionDetail } from '@rie/domain/types';

interface EditPermissionProps {
  id: string;
  initialData: PermissionDetail;
}

export const EditPermission = ({ id, initialData }: EditPermissionProps) => {
  const { mutate, status } = useUpdatePermission();
  const handleOnSubmit = (data: PermissionFormSchema) => {
    mutate({ payload: data, id });
  };

  // Transform the permission detail to form schema format
  const formInitialData: PermissionFormSchema = {
    domain: initialData.domain,
    action: initialData.action,
  };

  return (
    <PermissionForm
      onSubmitAction={handleOnSubmit}
      status={status}
      initialData={formInitialData}
    />
  );
};
