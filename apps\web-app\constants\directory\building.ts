import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const initialColumnVisibility = {
  //TODO: Check if these are the right ones
  campus: true,
  name: true,
  jurisdiction: true,
  lastUpdatedAt: true,
} as const;

export const buildingFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const buildingFormDefaultValues = (
  locale: SupportedLocale,
): BuildingFormSchema => ({
  name: [{ locale, value: '' }],
  alias: [{ locale, value: '' }],
  campus: { label: null, value: null },
  jurisdiction: { label: null, value: null },
  civicAddresses: [],
});
