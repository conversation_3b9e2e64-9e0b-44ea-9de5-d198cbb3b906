import { FundingProjectNotFoundError } from '@rie/domain/errors';
import type { FundingProjectInputSchema } from '@rie/domain/schemas';
import type { CollectionViewType, ResourceViewType } from '@rie/domain/types';
import { FundingProjectsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';
import {
  serializeFundingProjectToDetail,
  serializeFundingProjectToEditClient,
  serializeFundingProjectToListItem,
  serializeFundingProjectToSelectOption,
} from '../serializers';

type FundingProjectInput = Schema.Schema.Type<typeof FundingProjectInputSchema>;

export class FundingProjectsServiceLive extends Effect.Service<FundingProjectsServiceLive>()(
  'FundingProjectsServiceLive',
  {
    dependencies: [FundingProjectsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const fundingProjectsRepository = yield* FundingProjectsRepositoryLive;

      const getAllFundingProjects = (params: {
        locale: string;
        fallbackLocale: string;
        view: CollectionViewType;
      }) => {
        return Effect.gen(function* () {
          const projects = yield* fundingProjectsRepository.findAllFundingProjects();

          // Serialize based on view type
          switch (params.view) {
            case 'list': {
              return yield* Effect.all(
                projects.map((project) =>
                  serializeFundingProjectToListItem(
                    project,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
            }
            case 'select': {
              return yield* Effect.all(
                projects.map((project) =>
                  serializeFundingProjectToSelectOption(
                    project,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
            }
            case 'grid':
              return projects;
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });
      };

      const getFundingProjectById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) => {
        return Effect.gen(function* () {
          const project =
            yield* fundingProjectsRepository.findFundingProjectById(params.id);
          if (!project) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id: params.id }));
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              return yield* serializeFundingProjectToDetail(
                project,
                params.locale,
                params.fallbackLocale,
              );
            }
            case 'edit': {
              // For edit view, we need to get holder and funding type labels
              // For now, using null placeholders - these should be resolved from repositories
              const holderLabel = null; // TODO: Resolve from holder/researcher repository
              const fundingTypeLabel = null; // TODO: Resolve from funding types repository
              return yield* serializeFundingProjectToEditClient(project, holderLabel, fundingTypeLabel);
            }
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });
      };

      const createFundingProject = (project: FundingProjectInput) => {
        return Effect.gen(function* () {
          return yield* fundingProjectsRepository.createFundingProject({
            project,
          });
        });
      };

      const updateFundingProject = ({
        id,
        project,
      }: { id: string; project: FundingProjectInput }) => {
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }

          return yield* fundingProjectsRepository.updateFundingProject({
            projectId: id,
            project,
          });
        });
      };

      const deleteFundingProject = (id: string) => {
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }
          const result =
            yield* fundingProjectsRepository.deleteFundingProject(id);
          return result.length > 0;
        });
      };

      return {
        getAllFundingProjects,
        getFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
      } as const;
    }),
  },
) { }
