import { updateEntity } from '@/services/directory/entities';
import type { EntityPayload } from '@/types/directory/directory';
import type { DirectoryEntity } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useEditEntity = <K extends DirectoryEntity>(
  directoryEntity: K,
  id: string,
  onSuccess: (id: string) => void,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: EntityPayload<K>) =>
      updateEntity(directoryEntity, id, payload),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['allInfrastructures'] }); //TODO: changer par le bon nom de query a invalider
      queryClient.invalidateQueries({ queryKey: ['infiniteEquipments'] });
      onSuccess(data.id.toString());
    },
  });
};
