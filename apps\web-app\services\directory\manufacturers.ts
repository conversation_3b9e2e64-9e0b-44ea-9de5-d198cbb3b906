import { defaultRieServiceParams } from '@/constants/rie-client';
import { rieClient } from '@/services/client/client';
import type { ManufacturerFull } from '@/types/directory/manufacturer';
import type { ApiResponse, ApiReturnType } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';

export const getAllManufacturers = async (
  locale: SupportedLocale,
): Promise<ApiReturnType<ManufacturerFull[]>> => {
  const response = await rieClient.get<ApiResponse<ManufacturerFull[]>>(
    'search/fournisseur',
    {
      params: defaultRieServiceParams(locale),
    },
  );
  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
  };
};

export const getManufacturerById = async (
  id: string,
  locale: SupportedLocale,
): Promise<{
  manufacturer: ManufacturerFull;
}> => {
  const manufacturerPromise = rieClient.get<{ data: ManufacturerFull }>(
    `search/fournisseur/${id}`,
    {
      params: defaultRieServiceParams(locale),
    },
  );

  const [manufacturerResponse] = await Promise.all([manufacturerPromise]);

  return {
    manufacturer: manufacturerResponse.data.data,
  };
};
