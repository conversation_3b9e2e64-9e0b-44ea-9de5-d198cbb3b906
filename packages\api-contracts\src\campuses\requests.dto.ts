import * as Schema from 'effect/Schema';
import {
  OptionalLocalizedFieldDtoSchema,
  SelectOptionDtoSchema,
} from '../common';

/**
 * Describes the entire request body for creating or updating a campus.
 * This is the shape of the data the client will send to the API.
 **/

export const CampusFormRequestDtoSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  isActive: Schema.optional(Schema.Boolean),
  sadId: Schema.optional(Schema.NullOr(Schema.String)),
  jurisdiction: SelectOptionDtoSchema,
  pseudonym: Schema.String,
  name: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type CampusFormRequestDTO = Schema.Schema.Type<
  typeof CampusFormRequestDtoSchema
>;
