'use client';

import { vendorsColumns } from '@/app/[locale]/bottin/manufacturiers/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/vendors';
import type { SupportedLocale } from '@/types/locale';
import { DbVendor } from '@rie/db-schema/entity-types';

type VendorsListProps = {
  locale: SupportedLocale;
};

export const VendorsList = ({ locale }: VendorsListProps) => {
  return (
    <DirectoryList<'supplier', 'list', DbVendor, Record<string, unknown>>
      directoryListKey="supplier"
      view="list"
      locale={locale}
      columns={vendorsColumns()}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="vendors"
    />
  );
};
