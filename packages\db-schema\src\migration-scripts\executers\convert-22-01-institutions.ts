import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { OrganizationMigrationConverter } from '../converters/22-01-convert-institution-inserts';

async function convertOrganizationData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new OrganizationMigrationConverter();
    // Convert the file
    await converter.convertFile();
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertOrganizationData().catch(console.error);
