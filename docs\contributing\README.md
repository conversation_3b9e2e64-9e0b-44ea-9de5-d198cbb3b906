# Contributing to RIE

Thank you for your interest in contributing to the RIE (Research Infrastructure and Equipment) management system! This guide will help you understand our development process and how to contribute effectively.

## 🎯 Getting Started

### Prerequisites

- Complete the [installation guide](../getting-started/installation.md)
- Read the [architecture overview](../architecture/README.md)
- Understand our [code style guidelines](./code-style.md)

### First Steps

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Set up the development environment**
4. **Create a feature branch** for your changes
5. **Make your changes** following our guidelines
6. **Test your changes** thoroughly
7. **Submit a pull request**

## 🔄 Development Workflow

### Branch Strategy

We use a **feature branch workflow** with the following conventions:

#### Branch Naming

```
feat/feature-name          # New features
fix/bug-description        # Bug fixes
docs/documentation-update  # Documentation changes
refactor/code-improvement  # Code refactoring
test/test-improvements     # Test additions/improvements
chore/maintenance-task     # Maintenance tasks
```

#### Branch Lifecycle

```
main (protected)
├── feat/user-management
├── fix/authentication-bug
└── docs/api-documentation
```

### Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): brief description

Detailed explanation of the change (optional)

- Additional context or reasoning
- Breaking changes (if any)
- Related issues or tickets

Closes #123
```

#### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks
- `perf`: Performance improvements
- `ci`: CI/CD changes

#### Examples

```bash
feat(api): add equipment management endpoints

- Add CRUD operations for equipment
- Include validation and error handling
- Add comprehensive tests

Closes #45

fix(auth): resolve token expiration issue

The JWT tokens were expiring too quickly due to incorrect
configuration. Updated the expiration time to 24 hours.

Fixes #67

docs(readme): update installation instructions

- Add missing environment variables
- Clarify database setup steps
- Include troubleshooting section
```

## 🧪 Testing Requirements

### Test Coverage

- **Unit tests**: Required for all new functions and business logic
- **Integration tests**: Required for API endpoints and database operations
- **E2E tests**: Required for critical user workflows
- **Component tests**: Required for new React components

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run specific test suite
pnpm test packages/utils

# Run E2E tests
cd apps/web-app-e2e && pnpm e2e:test
```

### Test Requirements

- **Minimum 80% coverage** for new code
- **All tests must pass** before merging
- **No flaky tests** - tests should be deterministic
- **Proper mocking** of external dependencies

## 📝 Code Review Process

### Pull Request Guidelines

#### PR Title

Use the same format as commit messages:

```
feat(api): add equipment management endpoints
```

#### PR Description Template

```markdown
## Description
Brief description of what this PR does.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass (if applicable)
- [ ] Manual testing completed

## Screenshots (if applicable)
Include screenshots for UI changes.

## Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

### Review Process

1. **Automated Checks**: CI runs tests, linting, and type checking
2. **Code Review**: At least one team member reviews the code
3. **Feedback**: Address any comments or suggestions
4. **Approval**: PR is approved by reviewer(s)
5. **Merge**: PR is merged into main branch

### Review Criteria

#### Code Quality

- [ ] Code follows established patterns and conventions
- [ ] Functions are well-named and have clear purposes
- [ ] Complex logic is properly commented
- [ ] No code duplication
- [ ] Proper error handling

#### Architecture

- [ ] Changes fit within the existing architecture
- [ ] Dependencies are appropriate and minimal
- [ ] Separation of concerns is maintained
- [ ] Clean Architecture principles are followed

#### Testing

- [ ] Adequate test coverage
- [ ] Tests are meaningful and test behavior, not implementation
- [ ] Edge cases are covered
- [ ] Tests are maintainable

#### Documentation

- [ ] Code is self-documenting where possible
- [ ] Complex algorithms are explained
- [ ] API changes are documented
- [ ] README updates if needed

## 🎨 Code Style Guidelines

### General Principles

- **Consistency**: Follow existing patterns in the codebase
- **Readability**: Write code that tells a story
- **Simplicity**: Prefer simple solutions over complex ones
- **Performance**: Consider performance implications
- **Security**: Follow security best practices

### TypeScript Guidelines

```typescript
// Use descriptive names
const getUserPermissions = (userId: string): Promise<Permission[]> => {
  // Implementation
}

// Prefer interfaces for object shapes
interface CreateUserRequest {
  readonly name: string
  readonly email: string
  readonly role?: string
}

// Use enums for constants
enum UserStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Pending = 'PENDING'
}

// Use proper error handling
const createUser = async (data: CreateUserRequest): Promise<User> => {
  try {
    return await userService.create(data)
  } catch (error) {
    throw new UserCreationError('Failed to create user', { cause: error })
  }
}
```

### React Guidelines

```typescript
// Use functional components with hooks
export function UserList({ filters }: UserListProps) {
  const { data: users, isLoading } = useUsers(filters)
  
  if (isLoading) {
    return <LoadingSpinner />
  }
  
  return (
    <div className="user-list">
      {users?.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  )
}

// Use proper prop types
interface UserCardProps {
  readonly user: User
  readonly onEdit?: (user: User) => void
  readonly className?: string
}
```

For detailed style guidelines, see [Code Style Guide](./code-style.md).

## 🚀 Release Process

### Versioning

We use [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Workflow

1. **Feature Freeze**: Stop adding new features
2. **Testing**: Comprehensive testing of the release candidate
3. **Documentation**: Update documentation and changelog
4. **Release**: Create release tag and deploy
5. **Post-Release**: Monitor for issues and hotfixes

## 🆘 Getting Help

### Resources

- **Documentation**: Check the [docs](../README.md) first
- **Architecture**: Review [architecture guides](../architecture/README.md)
- **Examples**: Look at existing code for patterns
- **Tests**: Check test files for usage examples

### Communication

- **Questions**: Ask in team chat or GitHub discussions
- **Issues**: Create GitHub issues for bugs or feature requests
- **Ideas**: Discuss major changes before implementing

### Mentorship

- New contributors are paired with experienced team members
- Regular code review sessions for learning
- Architecture discussions for complex changes

## 📋 Issue Guidelines

### Bug Reports

```markdown
**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. macOS, Windows, Linux]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional context**
Add any other context about the problem here.
```

### Feature Requests

```markdown
**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.
```

## 🏆 Recognition

### Contributors

All contributors are recognized in:

- Project README
- Release notes
- Team meetings
- Annual contributor appreciation

### Contribution Types

We value all types of contributions:

- Code contributions
- Documentation improvements
- Bug reports and testing
- Design and UX feedback
- Community support

---

**Thank you for contributing to RIE!** 🎉

Your contributions help make research infrastructure management better for everyone.
