# Domain-Driven Design Architecture Guide for Effect-TS Codebase

## Executive Summary

This document presents a comprehensive analysis of the current codebase architecture and provides recommendations for implementing Domain-Driven Design (DDD) patterns within an Effect-TS functional programming context.

**Key Findings:**
- Current architecture shows good separation of concerns but lacks proper domain modeling
- Services are correctly implemented as Application Services but domain lacks Aggregate Roots
- Repositories return raw data instead of domain entities, violating DDD principles
- Serializers mix data transformation with business logic, requiring pattern separation

**Key Architectural Decisions:**
- Adopt a hybrid functional DDD approach using Effect-TS patterns
- Implement immutable Aggregate Roots using `Data.case` for state management
- Transform repositories to work with domain entities while maintaining Effect composition
- Separate business logic from data transformation in serializers


## Recommended Architecture

### Layered Architecture with Effect-TS

```
┌───────────────────────────────────────────┐
│           Application Layer               │
│  ┌──────────────────────────────────────┐ │
│  │     EquipmentsServiceLive            │ │
│  │  (Orchestration & Cross-cutting)     │ │
│  └──────────────────────────────────────┘ │
└───────────────────────────────────────────┘
                    │
┌───────────────────────────────────────────┐
│             Domain Layer                  │
│  ┌──────────────────┐ ┌─────────────────┐ │
│  │  Aggregate       │ │  Value Objects  │ │
│  │  Roots           │ │                 │ │
│  │  - Equipment     │ │ - EquipmentCost │ │
│  │  - Infrastructure│ │ - Address       │ │
│  └──────────────────┘ └─────────────────┘ │
│  ┌──────────────────────────────────────┐ │
│  │        Domain Services               │ │
│  │  - Policy Services                   │ │
│  │  - Access Control                    │ │
│  └──────────────────────────────────────┘ │
└───────────────────────────────────────────┘
                    │
┌───────────────────────────────────────────┐
│         Infrastructure Layer              │
│  ┌──────────────────────────────────────┐ │
│  │         Repositories                 │ │
│  │  (Domain Entity ↔ Database)          │ │
│  └──────────────────────────────────────┘ │
└───────────────────────────────────────────┘
```

### Effect-TS + DDD Hybrid Approach

The recommended architecture combines DDD tactical patterns with Effect-TS functional programming principles:

- **Immutable Domain Objects**: Using `Data.case` for entity state
- **Effect Composition**: All operations return Effects for composability
- **Schema Validation**: Effect Schema for domain validation and transformation
- **Functional Aggregates**: Business invariants enforced through pure functions

Domain-Driven Design (DDD) provides a robust framework for structuring software around a core domain model. In the context of your units management application, we can identify Entities, Value Objects, and Aggregates by focusing on their distinct characteristics, particularly identity, mutability, and consistency boundaries.

Here's an analysis of these core DDD building blocks applied to your TypeScript units management application scenario:

### Entities

An Entity is an object defined by a thread of continuity and identity, rather than its attributes, persisting over time. Its primary significance is who it is, not what it is. Entities encapsulate Critical Business Rules that are essential to the business and would exist even without automation.

We'll use `Unit` as an implementation example:

• **Unit** (from units table): The Unit itself is a prime example of an Entity.

◦ **Identity**: Each unit has a unique ID (from the units table) that distinguishes it from all other units, even if their content were identical at some point. This ID remains constant throughout its lifecycle.
    
◦ **Mutability**: The content of a unit (name, description, specifications) can change over time, and new translations can be added, but the underlying unit itself remains the same identifiable entity.

◦ **Core Business Rules**: The Unit entity would likely embody rules related to its operational status (e.g., active, inactive, maintenance), its location, creation and last update dates, and perhaps categories or types. These are high-level rules crucial to the units management operation.

◦ **Independence**: According to Clean Architecture principles, the Unit entity (and other core entities) should be pure business logic, unsullied by concerns about databases, user interfaces, or third-party frameworks. It can be an object with methods or a combination of data structures and functions.

### Value Objects

A Value Object is an object whose value is important, having no conceptual identity. You care about a Value Object for what it is, rather than who it is. Key characteristics include immutability and the fact that two Value Objects with the same attributes are considered interchangeable and identical.

• **Translations** (Name, Description, Specifications in a specific language): Each translated piece of content is an excellent candidate for a Value Object.
    
◦ **Immutability**: If a unit's English name changes, you would replace the old TranslatedName Value Object with a new one, rather than modifying the existing instance. This ensures thread-safety and side-effect-free operations.
    
◦ **No Identity**: The specific "English name 'Production Unit A'" doesn't have an independent identity; its significance comes from being the English name of a particular unit. If another unit had the exact same English name, they would be considered equal from a value perspective.
    
◦ **Context and Validation**: You could create specific Value Objects like TranslatedString, LanguageCode, or even UnitName(string, LanguageCode) which would bring context to the value and could encapsulate validation rules (e.g., TranslatedString must not be empty, LanguageCode must be a valid ISO code).

### First-Class Collections

The First-Class Collection pattern is applied when a collection of domain objects has its own behavior and invariants beyond simply storing items. Instead of using a primitive array or list, the collection becomes its own Value Object with domain-specific methods and business rules.

• **UnitTranslationCollection**: A collection of translations for a unit that enforces business rules and provides domain-specific behavior.

◦ **Business Invariants**: Ensures no duplicate locales exist within the collection - each language should appear only once.

◦ **Domain Behavior**: Provides locale-specific lookup with fallback logic, a key business requirement for internationalization.

◦ **Encapsulation**: The collection hides the complexity of translation management from the aggregate root.

◦ **Immutability**: Like all value objects, the collection is immutable - operations return new instances rather than modifying existing ones.

This pattern is justified when:
- The collection has business rules to enforce (e.g., no duplicate locales)
- The collection provides domain-specific behavior (e.g., translation lookup with fallback)
- The collection represents a domain concept (e.g., "the complete set of translations for an entity")
- The collection's behavior would otherwise leak into the aggregate root or service layer

• **Client Request/Response Models (DTOs)**: The various data formats sent from the client (e.g., for creating/editing a unit) or sent to the client (for listing, detail view, or editing) are typically Value Objects or simple data structures.

◦ **Purpose**: These objects are primarily for data transfer and display, and their structure is defined by the needs of the UI or external systems, not the core business rules of the Unit entity.

◦ **Decoupling**: Having separate request and response models prevents the internal Unit entity from being coupled to UI-specific or database-specific data formats, adhering to the Single Responsibility Principle.

◦ **TypeScript Support**: Libraries like effect/Schema are designed to define immutable schema values that can be used for decoding (parsing external data into internal types) and encoding (converting internal types for external systems). This is ideal for defining these data transfer objects in a type-safe and validated manner.

### Aggregates

An Aggregate is a cluster of associated Entities and Value Objects that are treated as a single unit for the purpose of data changes and maintaining consistency. They define clear consistency boundaries within the domain model. Each Aggregate has an Aggregate Root, which must be an Entity, and this root is the only object external objects can hold references to.

• **Unit with its Translations**: The Unit entity, along with all its associated multi-language names, descriptions, and specifications (from the units_i18n table), would form an Aggregate.

◦ **Aggregate Root**: The Unit entity is the Aggregate Root. All operations that change the unit or its translations must go through this Unit root.

◦ **Consistency Boundary**: The Aggregate's boundary ensures that the unit, including all its localized content, is always in a valid and consistent state. For example:
    ▪ If a Unit is deleted, all its corresponding translations in units_i18n must also be deleted.
    ▪ A Unit might have an invariant that it must always have at least one translation (e.g., a default language).
    ▪ Changes to any translation (a Value Object within the Aggregate) must be managed by the Unit root to ensure all invariants are satisfied before the changes are committed.

◦ **Update Strategy**: When updating a Unit aggregate, the "replace all" pattern is used for its value objects (translations). Rather than tracking individual translation changes, all translations are replaced as a single atomic operation. This approach:
    ▪ Maintains aggregate consistency by treating all translations as a cohesive unit
    ▪ Simplifies the domain model by avoiding complex merge logic
    ▪ Aligns with value object immutability - value objects are replaced, not modified
    ▪ Ensures predictable behavior - what the client sends is exactly what gets stored
    
◦ **Global vs. Local Identity**: The Unit (root) has a global identity (its unique ID). Any internal objects like individual Translation records (if they were modeled as local entities within the Aggregate) would have local identity, meaning their uniqueness is only guaranteed within that specific Unit Aggregate. However, in your case, translations are likely Value Objects, and their identity is determined by their content and the Unit they belong to.
    
◦ **Referencing**: Other parts of your system should reference a unit only by its Unit ID, not by directly accessing specific translation objects or their IDs from units_i18n. This prevents accidental modifications or inconsistencies from outside the Aggregate's control.

By applying these DDD building blocks in your TypeScript application, you can create a clear, maintainable, and robust domain model that effectively tackles the complexity of managing multi-language units while ensuring data integrity.

## Distinguishing Business Logic from Presentation Logic

A critical part of a clean architecture is correctly identifying what constitutes a core business rule versus a presentation-specific rule. Getting this distinction right ensures the domain layer remains pure and the application is flexible to future UI changes.

### The Litmus Test

To distinguish between the two, ask this question:

> **"If we threw away our web app and built a command-line tool or a raw CSV data export instead, would this rule still need to exist?"**

- If **Yes**, it is a **Business Rule**.
- If **No**, it is a **Presentation Rule**.

### Business Rules

Business rules are fundamental truths or constraints of the domain itself, regardless of how they are displayed.

- **Examples:**
    - A `Unit` must have at least one translation.
    - A `Unit` cannot be its own parent.
    - An inactive `Unit` cannot be scheduled for use.
- **Responsibility:** Enforcing these rules is the primary job of the **Domain Layer** (Aggregates and Value Objects). The domain should guarantee its own consistency.

### Presentation Rules

Presentation rules are about formatting, arranging, or adapting data for a specific user interface or output context. They are about making data useful and convenient for a human user.

- **Examples:**
    - If the French name for a `Unit` is missing, display the English name as a user-friendly fallback.
    - In a list view, truncate `Unit` descriptions to the first 50 characters.
    - Display all dates in `MM/DD/YYYY` format.
- **Responsibility:** Implementing these rules is the job of the **Application/Presentation Layer**, specifically **Serializers**. The serializer takes pure data from the domain and applies the presentation rules to create the final DTO for a specific view.

By keeping these responsibilities separate, the domain remains a pure and accurate model of the business, while the serializers can be tailored to create many different presentations of that data without requiring any changes to the core business logic.

## Architectural Patterns and Data Flow

### 1. Architectural Patterns

This section introduces the high-level patterns that govern the architecture.

##### 1.1. First-Class Collections
 * Principle: When a collection of Value Objects has its own domain-specific behaviors, rules, or
   invariants, it should be treated as its own explicit Value Object.
 * Purpose: To encapsulate collection-specific logic, enforce invariants (e.g., no duplicate items), and
   provide a richer, more expressive domain model.
 * Example: The UnitTranslationCollection is the canonical example in this project. It manages the set of
   UnitTranslation objects and contains logic for locale fallbacks and ensuring there are no duplicate
   locales, which are responsibilities that don't belong on a plain array.

##### 1.2. The Anti-Corruption Layer (ACL)
 * Principle: A layer of software that acts as a translator between the internal domain model and external
   systems (like a UI, a database, or another API).
 * Purpose: To protect the integrity of the Domain Model by preventing external, often messy or unstable,
   data structures from "leaking" into it. This keeps the domain pure and focused exclusively on business
   logic.
 * Implementation: In our architecture, the ACL is primarily implemented by Mappers, which live in the
   Application/Service layer.

---

### 2. Data Flow and Transformation

This section details the practical implementation of the ACL and the flow of data through the system.

##### 2.1. The Three Schema Types
It's crucial to distinguish between the three different kinds of schemas in our system:
 1. Domain Schemas: Define the shape of our pure domain aggregates and value objects.
     * Location: packages/domain/src/schemas/
 2. Data Transfer Object (DTO) Schemas: Define the public "contract" of our API—the shape of data for
    requests and responses.
     * Location: packages/api-contracts/src/
 3. Persistence Schemas: Define the structure of our database tables.
     * Location: packages/db-schema/src/

##### 2.2. The `api-contracts` Package
 * Purpose: This package is the single source of truth for the API's external contract. It contains only
   data structure definitions (DTOs) and has no business logic. Its primary role is to break potential
   circular dependencies between the API/services and the domain.
 * Contents: Schemas and derived TypeScript types for all API requests and responses.
 * Dependencies: This package should have minimal dependencies and should never depend on the domain
   package.

##### 2.3. DTOs, Mappers, and Serializers
 * DTOs (Data Transfer Objects): These are passive data structures that define the shape of data crossing
   application boundaries. They are the "what".
     * Example: UnitDetailResponseDTO
 * Mappers: These are responsible for transforming data between different representations, specifically
   between DTOs and Domain Objects. They are the "how" and form our ACL.
     * Location: packages/services/src/mappers/ (or within the apps/api/ if not in a shared package).
     * Direction: They transform RequestDTOs -> Domain Inputs and Domain Objects -> ResponseDTOs.
 * Serializers: These transform objects into a specific transmission format (e.g., JSON). This is largely
   handled automatically by our web framework when returning data from controllers.

##### 2.4. Unidirectional Dependency Flow
The dependencies must always flow in one direction to prevent cycles and maintain separation of concerns.


  1 API Routes
  2    |
  3    +--> Depends on Services (+ Mappers)
  4    |
  5    +--> Depends on api-contracts (for DTOs)
  6 
  7 Services (+ Mappers)
  8    |
  9    +--> Depends on Domain (for Aggregates, VOs)
 10    |
 11    +--> Depends on Repositories (for data access)
 12 
 13 Repositories
 14    |
 15    +--> Depends on Domain (returns Domain Objects)
 16    |
 17    +--> Depends on db-schema (for persistence)
 18 
 19 Domain
 20    |
 21    +--> Depends on NOTHING (it is pure)

---

### 3. Naming Conventions

##### 3.1. DTO Suffix
 * Rule: All types derived from schemas in the api-contracts package must be suffixed with DTO.
 * Reason: This suffix provides a clear, immediate signal to any developer that the type is a simple data
   container for external communication, not a rich domain object with business logic.
 * Examples: CreateUnitRequestDTO, UnitSelectOptionDTO.

##### 3.2. Domain Types
 * Rule: Types in the domain package must not have a DTO suffix.
 * Reason: These types represent core business concepts, not data transfer structures.
 * Examples: Unit, UnitTranslation.

---

## 4. Complete Data Flow Documentation

This section provides a comprehensive walkthrough of how data flows through our DDD architecture, using the units domain as a concrete example. We'll trace the complete journey from client form submission to database persistence.

### 4.1. End-to-End Data Flow Overview

The data flow in our DDD architecture follows a clear path through multiple transformation stages:

```
Client Form → API Route → DTO Validation → Mapper (ACL) → Domain Objects → Repository → Database
     ↓           ↓            ↓              ↓              ↓             ↓          ↓
[JSON Data] → [HTTP Req] → [UnitFormDTO] → [UnitInput] → [Unit Aggregate] → [SQL] → [Persistence]
```

### 4.2. Detailed Data Flow: Creating a Unit

Let's trace a complete create unit operation through each layer:

#### Stage 1: Client Form Submission
**Location:** Client Application → API Route Handler
**File:** `apps/api/src/api/v2/routes/units.route.ts`

```typescript
// Client sends JSON data like:
{
  "isActive": true,
  "type": { "value": "unit_type_123", "label": "Department" },
  "parent": { "value": "parent_unit_123", "label": "Parent Unit" },
  "names": [
    { "locale": "fr", "value": "Département Informatique" },
    { "locale": "en", "value": "Computer Science Department" }
  ],
  "description": [
    { "locale": "fr", "value": "Description du département" },
    { "locale": "en", "value": "Department description" }
  ]
  // ... other fields
}

// API Route Handler receives and validates the request
unitsRoute.post('/', createUnitRoute,
  validator('json', UnitFormRequestDtoSchema), // ← DTO Validation happens here
  async (ctx) => {
    const body = ctx.req.valid('json'); // ← Validated UnitFormRequestDTO
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.createUnit({
        unitDto: body, // ← Pass DTO to service layer
        userId: user.id,
      });
    });
    // ... error handling and response
  }
);
```

**Key Points:**
- **DTO Validation**: The `UnitFormRequestDtoSchema` validates the incoming JSON against the API contract
- **Error Boundary**: Invalid data is rejected before reaching the domain layer
- **Effect Composition**: The operation is wrapped in Effect for composable error handling

#### Stage 2: Service Layer Processing
**Location:** Application Service
**File:** `packages/services/src/services/units.service.ts`

```typescript
const createUnit = (params: {
  unitDto: UnitFormRequestDTO; // ← Input: API Contract DTO
  userId: string;
}) =>
  Effect.gen(function* () {
    // STEP 1: Anti-Corruption Layer - Transform DTO to Domain Input
    const domainInput = yield* Schema.decode(UnitFormToDomainInput)(
      params.unitDto
    );
    // domainInput is now: UnitInput (domain schema)

    // STEP 2: Create Value Objects with validation
    const translationVOs = yield* Effect.all(
      domainInput.translations.map((t) => UnitTranslation.create(t))
    );
    const translationCollection = yield* UnitTranslationCollection.create(translationVOs);

    // STEP 3: Create Aggregate Root with business rules
    const unit = yield* Unit.create({
      ...domainInput,
      modifiedBy: params.userId,
      translations: translationCollection,
    });

    // STEP 4: Persist through Repository
    const repo = yield* UnitsRepositoryLive;
    const savedUnit = yield* repo.save(unit);

    // STEP 5: Transform back to Response DTO
    return yield* pipe(
      savedUnit.toRaw(),
      Effect.flatMap((raw) => serializeUnitToFormEdit(raw))
    );
  });
```

**Key Transformations:**
1. **UnitFormRequestDTO** → **UnitInput** (via Mapper/ACL)
2. **UnitInput** → **Value Objects** (UnitTranslation, UnitTranslationCollection)
3. **Value Objects** → **Unit Aggregate** (with business rules validation)
4. **Unit Aggregate** → **Persistence Data** (via toRaw())
5. **Saved Unit** → **Response DTO** (via Serializer)

#### Stage 3: Anti-Corruption Layer (Mapper)
**Location:** Data Transformation Layer
**File:** `packages/services/src/mappers/unit-form.mapper.ts`

```typescript
export const UnitFormToDomainInput = Schema.transformOrFail(
  UnitFormRequestDtoSchema, // ← Input: API Contract
  UnitInputSchema,          // ← Output: Domain Schema
  {
    decode: (formDto, _, ast) => {
      // Transform from client structure to domain structure
      const translationsMap = new Map<string, UnitI18NInput>();

      // Consolidate separate arrays (names, descriptions, etc.) into unified translations
      for (const item of formDto.names) {
        ensureLocale(item.locale);
        translationsMap.get(item.locale)!.name = item.value;
      }

      for (const item of formDto.description) {
        ensureLocale(item.locale);
        translationsMap.get(item.locale)!.description = item.value;
      }

      // Return clean domain input
      return {
        isActive: formDto.isActive,
        typeId: formDto.type.value,        // ← Extract value from SelectOption
        parentId: formDto.parent.value,    // ← Extract value from SelectOption
        translations: Array.from(translationsMap.values())
      };
    }
  }
);
```

**ACL Responsibilities:**
- **Structure Transformation**: Client's separate arrays → Domain's unified translations
- **Data Extraction**: SelectOption objects → primitive values
- **Validation**: Ensures data conforms to domain expectations
- **Protection**: Prevents external data structures from leaking into domain

#### Stage 4: Domain Layer Processing
**Location:** Domain Aggregates and Value Objects
**Files:** `packages/domain/src/aggregates/units.aggregate.ts`, `packages/domain/src/value-objects/`

```typescript
// Value Object Creation with Validation
export class UnitTranslation {
  static create(input: unknown): Effect.Effect<UnitTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbUnitI18NDataSchema)(input), // ← Domain validation
      Effect.map(validatedData => new UnitTranslation(validatedData))
    );
  }
}

// First-Class Collection with Business Rules
export class UnitTranslationCollection {
  static create(translations: readonly UnitTranslation[]):
    Effect.Effect<UnitTranslationCollection, DuplicateLocaleError> {
    return pipe(
      Effect.succeed(translations),
      Effect.flatMap(ensureNoDuplicateLocales), // ← Business rule enforcement
      Effect.map(validTranslations => new UnitTranslationCollection(validTranslations))
    );
  }
}

// Aggregate Root with Invariant Validation
export class Unit {
  static create(props: {
    typeId: string;
    modifiedBy: string;
    translations: UnitTranslationCollection;
  }): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(UnitAggregateState({
      id: DbUtils.cuid2(), // ← Generate unique identity
      isActive: true,
      typeId: props.typeId,
      modifiedBy: props.modifiedBy,
      translations: props.translations,
    }));

    // Validate business invariants before returning
    return pipe(
      newUnit.validateInvariants(), // ← Business rules validation
      Effect.map(() => newUnit)
    );
  }

  private validateInvariants(): Effect.Effect<void, UnitInvariantViolationError> {
    // Business Rule: Unit must have at least one translation
    if (this.data.translations.isEmpty()) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation'
      }));
    }

    // Business Rule: Unit must have at least one valid name
    const hasValidName = this.data.translations.getAll().some(t => {
      const name = t.getName();
      return name !== null && name.trim() !== '';
    });

    if (!hasValidName) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation with a valid name'
      }));
    }

    return Effect.succeed(void 0);
  }
}
```

**Domain Layer Responsibilities:**
- **Identity Management**: Aggregate generates its own unique ID
- **Business Rules**: Validates invariants (e.g., must have translations)
- **Immutability**: All objects are immutable by design
- **Encapsulation**: Internal state is protected and accessed through methods

#### Stage 5: Repository Layer Persistence
**Location:** Infrastructure Layer
**File:** `packages/repositories/src/units.repository.ts`

```typescript
const save = (unit: Unit) =>
  pipe(
    unit.toRaw(), // ← Convert aggregate to persistence format
    Effect.flatMap((unitData) =>
      dbClient.transaction((tx) => // ← Transaction boundary starts here
        Effect.gen(function* () {
          // STEP 1: Upsert main unit record
          const [savedUnit] = yield* tx((client) =>
            client.insert(DBSchema.units)
              .values(unitData)
              .onConflictDoUpdate({
                target: DBSchema.units.id,
                set: {
                  isActive: unitData.isActive,
                  typeId: unitData.typeId,
                  // ... other fields
                }
              })
              .returning({ id: DBSchema.units.id })
          );

          // STEP 2: Replace all translations (delete + insert pattern)
          yield* tx((client) =>
            client.delete(DBSchema.unitsI18N)
              .where(eq(DBSchema.unitsI18N.dataId, savedUnit.id))
          );

          if (unitData.translations.length > 0) {
            yield* tx((client) =>
              client.insert(DBSchema.unitsI18N).values(
                unitData.translations.map((t) => ({
                  ...t,
                  dataId: unitData.id, // ← Link translations to unit
                }))
              )
            );
          }

          // STEP 3: Re-fetch to get final state with DB timestamps
          const finalUnit = yield* findUnitById(savedUnit.id);
          return finalUnit; // ← Return fully hydrated aggregate
        })
      )
    )
  );
```

**Repository Responsibilities:**
- **Transaction Management**: Ensures atomicity across multiple tables
- **Aggregate Persistence**: Handles complex object-relational mapping
- **Consistency**: "Replace all" pattern maintains aggregate consistency
- **Rehydration**: Returns fully formed domain objects, not raw data

### 4.3. Data Flow: Update vs Create Differences

The update flow differs from create in key ways:

```typescript
const updateUnit = ({ id, unitDto, userId }) =>
  Effect.gen(function* () {
    // DIFFERENCE 1: Fetch existing aggregate first
    const existingUnit = yield* repo.findUnitById(id);
    if (!existingUnit) {
      return yield* Effect.fail(new UnitNotFoundError({ id }));
    }

    // DIFFERENCE 2: Use existing aggregate's update method
    const updatedDomainUnit = yield* existingUnit.update({
      ...domainInput,
      modifiedBy: userId,
      translations: translationCollection,
    });

    // Same: Save and serialize response
    const savedUnit = yield* repo.save(updatedDomainUnit);
    return yield* serializeResponse(savedUnit);
  });
```

**Key Differences:**
- **Identity Preservation**: Update preserves the existing aggregate's ID and timestamps
- **Aggregate Method**: Calls `existingUnit.update()` instead of `Unit.create()`
- **Validation Context**: Update validation may differ from create validation

## 5. DDD Entity-to-Code Mapping

This section provides concrete mappings between DDD theoretical concepts and their actual implementation in our TypeScript/Effect-TS codebase.

### 5.1. DDD Concepts Implementation Map

| DDD Concept | Implementation Location | TypeScript Pattern | Example |
|-------------|------------------------|-------------------|---------|
| **Aggregate Root** | `packages/domain/src/aggregates/` | `class` with private constructor | `Unit.create()` |
| **Value Objects** | `packages/domain/src/value-objects/` | Immutable `class` with validation | `UnitTranslation.create()` |
| **First-Class Collections** | `packages/domain/src/value-objects/` | Collection wrapper with rules | `UnitTranslationCollection` |
| **Domain Services** | `packages/domain/src/services/` | Pure functions or Effect services | Policy validation |
| **Application Services** | `packages/services/src/services/` | Effect.Service pattern | `UnitsServiceLive` |
| **Repositories** | `packages/repositories/src/` | Effect.Service with domain return types | `UnitsRepositoryLive` |
| **Anti-Corruption Layer** | `packages/services/src/mappers/` | Schema transformers | `UnitFormToDomainInput` |
| **DTOs** | `packages/api-contracts/src/` | Effect Schema definitions | `UnitFormRequestDTO` |
| **Domain Errors** | `packages/domain/src/errors/` | `Data.TaggedError` classes | `UnitNotFoundError` |

### 5.2. Detailed Code Examples by DDD Pattern

#### 5.2.1. Aggregate Root Implementation
**File:** `packages/domain/src/aggregates/units.aggregate.ts`

```typescript
// DDD Concept: Aggregate Root
// - Has global identity
// - Enforces business invariants
// - Controls access to internal entities/value objects
// - Manages consistency boundary

export class Unit {
  private constructor(private readonly data: UnitAggregateState) {}

  // Factory method ensures valid creation
  static create(props: {
    typeId: string;
    modifiedBy: string;
    translations: UnitTranslationCollection;
  }): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(UnitAggregateState({
      id: DbUtils.cuid2(), // ← Global identity
      isActive: true,
      typeId: props.typeId,
      modifiedBy: props.modifiedBy,
      translations: props.translations,
    }));

    // ← Invariant validation before creation
    return pipe(
      newUnit.validateInvariants(),
      Effect.map(() => newUnit)
    );
  }

  // Business method that maintains invariants
  update(props: UpdateProps): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(UnitAggregateState({
      ...this.data, // ← Preserve identity and timestamps
      typeId: props.typeId,
      translations: props.translations,
      modifiedBy: props.modifiedBy,
    }));

    return pipe(
      newUnit.validateInvariants(), // ← Re-validate after changes
      Effect.map(() => newUnit)
    );
  }

  // Controlled access to internal state
  get id(): string {
    return this.data.id;
  }

  // Business logic encapsulated in the aggregate
  isActive(): boolean {
    return this.data.isActive;
  }

  // Complex business behavior
  getCompositeTranslations(locale: string, fallbackLocale = 'en') {
    return this.data.translations.getCompositeTranslations(locale, fallbackLocale);
  }
}
```

#### 5.2.2. Value Object Implementation
**File:** `packages/domain/src/value-objects/unit-translation.vo.ts`

```typescript
// DDD Concept: Value Object
// - No identity (compared by value)
// - Immutable
// - Self-validating
// - Can contain business logic

export class UnitTranslation {
  private constructor(private readonly data: UnitTranslationFields) {}

  // Factory method with validation
  static create(input: unknown): Effect.Effect<UnitTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbUnitI18NDataSchema)(input), // ← Validation
      Effect.map(validatedData =>
        new UnitTranslation(UnitTranslationData(validatedData))
      )
    );
  }

  // Value-based equality (implicit through immutability)
  getLocale(): string { return this.data.locale; }
  getName(): string { return this.data.name; }
  getDescription(): string | null { return this.data.description; }

  // Business logic within value object
  hasValidName(): boolean {
    return this.data.name !== null && this.data.name.trim() !== '';
  }

  // Immutable transformation
  withName(newName: string): Effect.Effect<UnitTranslation, ParseError> {
    return UnitTranslation.create({
      ...this.data,
      name: newName
    });
  }
}
```

#### 5.2.3. First-Class Collection Implementation
**File:** `packages/domain/src/value-objects/unit-translation-collection.vo.ts`

```typescript
// DDD Concept: First-Class Collection
// - Collection with its own business rules
// - Encapsulates collection-specific behavior
// - Enforces invariants across collection items

export class UnitTranslationCollection {
  private constructor(
    public readonly translations: readonly UnitTranslation[]
  ) {}

  static create(
    translations: readonly UnitTranslation[]
  ): Effect.Effect<UnitTranslationCollection, DuplicateLocaleError> {
    return pipe(
      Effect.succeed(translations),
      Effect.flatMap(ensureNoDuplicateLocales), // ← Collection invariant
      Effect.map(validTranslations =>
        new UnitTranslationCollection(validTranslations)
      )
    );
  }

  // Collection-specific business behavior
  getCompositeTranslations(locale: string, fallbackLocale: string) {
    const primary = this.findByLocale(locale);
    const fallback = this.findByLocale(fallbackLocale);

    return {
      name: primary?.getName() || fallback?.getName() || null,
      description: primary?.getDescription() || fallback?.getDescription() || null,
      // ... other fields with fallback logic
    };
  }

  // Collection query methods
  findByLocale(locale: string): UnitTranslation | undefined {
    return this.translations.find(t => t.getLocale() === locale);
  }

  isEmpty(): boolean {
    return this.translations.length === 0;
  }

  getAll(): readonly UnitTranslation[] {
    return this.translations;
  }
}

// Collection invariant enforcement
function ensureNoDuplicateLocales(
  translations: readonly UnitTranslation[]
): Effect.Effect<readonly UnitTranslation[], DuplicateLocaleError> {
  const locales = translations.map(t => t.getLocale());
  const uniqueLocales = new Set(locales);

  if (locales.length !== uniqueLocales.size) {
    return Effect.fail(new DuplicateLocaleError({
      message: 'Duplicate locales found in translation collection'
    }));
  }

  return Effect.succeed(translations);
}
```

#### 5.2.4. Application Service Implementation
**File:** `packages/services/src/services/units.service.ts`

```typescript
// DDD Concept: Application Service
// - Orchestrates domain objects
// - Handles cross-cutting concerns
// - Coordinates between layers
// - No business logic (delegates to domain)

export class UnitsServiceLive extends Effect.Service<UnitsServiceLive>()(
  'UnitsServiceLive',
  {
    dependencies: [UnitsRepositoryLive.Default], // ← Dependency injection
    effect: Effect.gen(function* () {

      const createUnit = (params: {
        unitDto: UnitFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Anti-Corruption Layer
          const domainInput = yield* Schema.decode(UnitFormToDomainInput)(
            params.unitDto
          );

          // 2. Create Value Objects (delegates to domain)
          const translationVOs = yield* Effect.all(
            domainInput.translations.map(t => UnitTranslation.create(t))
          );
          const translationCollection =
            yield* UnitTranslationCollection.create(translationVOs);

          // 3. Create Aggregate (delegates to domain)
          const unit = yield* Unit.create({
            ...domainInput,
            modifiedBy: params.userId,
            translations: translationCollection,
          });

          // 4. Persistence (delegates to repository)
          const repo = yield* UnitsRepositoryLive;
          const savedUnit = yield* repo.save(unit);

          // 5. Response transformation (presentation concern)
          return yield* pipe(
            savedUnit.toRaw(),
            Effect.flatMap(raw => serializeUnitToFormEdit(raw))
          );
        });

      return { createUnit, /* ... other methods */ };
    })
  }
) {}
```

#### 5.2.5. Repository Implementation
**File:** `packages/repositories/src/units.repository.ts`

```typescript
// DDD Concept: Repository
// - Provides collection-like interface for aggregates
// - Handles object-relational mapping
// - Returns domain objects, not raw data
// - Encapsulates persistence technology

export class UnitsRepositoryLive extends Effect.Service<UnitsRepositoryLive>()(
  'UnitsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      // Collection-like interface
      const findUnitById = (id: string) => {
        const getRawUnitEffect = dbClient.makeQuery((execute, id: string) =>
          execute((client) =>
            client.query.units.findFirst({
              where: eq(DBSchema.units.id, id),
              // ... columns and relations
            })
          )
        );

        return pipe(
          getRawUnitEffect(id),
          Effect.flatMap((rawUnit) => {
            if (!rawUnit) return Effect.succeed(null);
            // ← Returns domain object, not raw data
            return Unit.fromDatabaseData(rawUnit);
          })
        );
      };

      // Aggregate persistence with transaction boundaries
      const save = (unit: Unit) =>
        pipe(
          unit.toRaw(), // ← Convert to persistence format
          Effect.flatMap((unitData) =>
            dbClient.transaction((tx) => // ← Transaction boundary
              Effect.gen(function* () {
                // Handle complex aggregate persistence
                const [savedUnit] = yield* tx((client) =>
                  client.insert(DBSchema.units)
                    .values(unitData)
                    .onConflictDoUpdate({
                      target: DBSchema.units.id,
                      set: { /* ... */ }
                    })
                    .returning({ id: DBSchema.units.id })
                );

                // Handle related entities (translations)
                yield* tx((client) =>
                  client.delete(DBSchema.unitsI18N)
                    .where(eq(DBSchema.unitsI18N.dataId, savedUnit.id))
                );

                if (unitData.translations.length > 0) {
                  yield* tx((client) =>
                    client.insert(DBSchema.unitsI18N)
                      .values(unitData.translations.map(t => ({
                        ...t,
                        dataId: unitData.id,
                      })))
                  );
                }

                // Return fully hydrated domain object
                return yield* findUnitById(savedUnit.id);
              })
            )
          )
        );

      return { findUnitById, save, /* ... */ };
    })
  }
) {}
```

## 6. Validation Pipeline and Error Handling

This section documents the comprehensive validation and error handling strategy across all architectural layers.

### 6.1. Multi-Layer Validation Strategy

Our validation occurs at three distinct layers, each with different responsibilities:

```
Layer 1: DTO Validation (API Boundary)
   ↓
Layer 2: Domain Validation (Business Rules)
   ↓
Layer 3: Persistence Validation (Database Constraints)
```

#### 6.1.1. Layer 1: DTO Validation (API Boundary)
**Purpose:** Validate external input format and basic constraints
**Location:** `packages/api-contracts/src/`
**Technology:** Effect Schema validation

```typescript
// File: packages/api-contracts/src/units/requests.dto.ts
export const UnitFormRequestDtoSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  isActive: Schema.Boolean,
  type: SelectOptionDtoSchema, // ← Validates structure: {value, label}
  parent: SelectOptionDtoSchema,
  names: Schema.Array(OptionalLocalizedFieldDtoSchema), // ← Validates array structure
  description: Schema.Array(OptionalLocalizedFieldDtoSchema),
  acronyms: Schema.Array(OptionalLocalizedFieldDtoSchema),
  otherNames: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

// Usage in API route:
unitsRoute.post('/', createUnitRoute,
  validator('json', UnitFormRequestDtoSchema), // ← Validation happens here
  async (ctx) => {
    const body = ctx.req.valid('json'); // ← Guaranteed to be valid DTO
    // ... rest of handler
  }
);
```

**DTO Validation Catches:**
- Missing required fields
- Wrong data types (string vs number)
- Invalid array structures
- Malformed nested objects

**Error Response Example:**
```json
{
  "error": "Validation failed",
  "details": {
    "field": "names",
    "message": "Expected array, received string"
  }
}
```

#### 6.1.2. Layer 2: Domain Validation (Business Rules)
**Purpose:** Enforce business invariants and domain-specific rules
**Location:** `packages/domain/src/`
**Technology:** Effect-based validation with custom domain errors

```typescript
// File: packages/domain/src/value-objects/unit-translation.vo.ts
export class UnitTranslation {
  static create(input: unknown): Effect.Effect<UnitTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbUnitI18NDataSchema)(input), // ← Domain schema validation
      Effect.map(validatedData => new UnitTranslation(validatedData))
    );
  }
}

// File: packages/domain/src/value-objects/unit-translation-collection.vo.ts
export class UnitTranslationCollection {
  static create(
    translations: readonly UnitTranslation[]
  ): Effect.Effect<UnitTranslationCollection, DuplicateLocaleError> {
    return pipe(
      Effect.succeed(translations),
      Effect.flatMap(ensureNoDuplicateLocales), // ← Business rule validation
      Effect.map(validTranslations => new UnitTranslationCollection(validTranslations))
    );
  }
}

// File: packages/domain/src/aggregates/units.aggregate.ts
export class Unit {
  private validateInvariants(): Effect.Effect<void, UnitInvariantViolationError> {
    // Business Rule 1: Must have at least one translation
    if (this.data.translations.isEmpty()) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation'
      }));
    }

    // Business Rule 2: Must have at least one valid name
    const hasValidName = this.data.translations.getAll().some(t => {
      const name = t.getName();
      return name !== null && name.trim() !== '';
    });

    if (!hasValidName) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation with a valid name'
      }));
    }

    // Business Rule 3: Cannot be its own parent
    if (this.data.parentId && this.data.parentId === this.data.id) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'A unit cannot be its own parent'
      }));
    }

    return Effect.succeed(void 0);
  }
}
```

**Domain Validation Catches:**
- Business rule violations
- Invalid domain state combinations
- Aggregate consistency issues
- Value object constraint violations

#### 6.1.3. Layer 3: Persistence Validation (Database Constraints)
**Purpose:** Final safety net and data integrity enforcement
**Location:** Database schema and repository error handling
**Technology:** Database constraints + Effect error handling

```typescript
// File: packages/repositories/src/units.repository.ts
const save = (unit: Unit) =>
  pipe(
    unit.toRaw(), // ← May fail if aggregate state is invalid
    Effect.flatMap((unitData) =>
      dbClient.transaction((tx) =>
        Effect.gen(function* () {
          try {
            const [savedUnit] = yield* tx((client) =>
              client.insert(DBSchema.units)
                .values(unitData) // ← Database constraints enforced here
                .onConflictDoUpdate({
                  target: DBSchema.units.id,
                  set: { /* ... */ }
                })
                .returning({ id: DBSchema.units.id })
            );
          } catch (dbError) {
            // Transform database errors to domain errors
            if (dbError.code === '23505') { // Unique constraint violation
              return yield* Effect.fail(new UnitPersistenceError({
                reason: 'Unit with this identifier already exists'
              }));
            }
            // ... handle other database errors
          }
        })
      )
    ),
    Effect.mapError((error) => {
      // Transform any remaining errors to domain errors
      if (error instanceof ParseError) {
        return new UnitPersistenceError({
          reason: `Invalid unit data: ${error.message}`
        });
      }
      return error;
    })
  );
```

### 6.2. Effect-TS Error Handling Patterns

Our error handling leverages Effect's typed error channels for composable and type-safe error management.

#### 6.2.1. Error Types Hierarchy

```typescript
// File: packages/domain/src/errors/units.error.ts

// Base domain errors
export class UnitNotFoundError extends Data.TaggedError('UnitNotFoundError')<{
  readonly id: string;
}> {}

export class UnitValidationError extends Data.TaggedError('UnitValidationError')<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> {}

export class UnitInvariantViolationError extends Data.TaggedError('UnitInvariantViolationError')<{
  readonly unitId: string;
  readonly reason: string;
}> {}

export class UnitPersistenceError extends Data.TaggedError('UnitPersistenceError')<{
  readonly unitId?: string;
  readonly reason: string;
}> {}

// Collection-specific errors
export class DuplicateLocaleError extends Data.TaggedError('DuplicateLocaleError')<{
  readonly message: string;
}> {}
```

#### 6.2.2. Error Composition and Propagation

```typescript
// Service layer error handling
const createUnit = (params: {
  unitDto: UnitFormRequestDTO;
  userId: string;
}) =>
  Effect.gen(function* () {
    // Each step can fail with specific error types
    const domainInput = yield* Schema.decode(UnitFormToDomainInput)(params.unitDto);
    // ↑ Can fail with: ParseError

    const translationVOs = yield* Effect.all(
      domainInput.translations.map(t => UnitTranslation.create(t))
    );
    // ↑ Can fail with: ParseError

    const translationCollection = yield* UnitTranslationCollection.create(translationVOs);
    // ↑ Can fail with: DuplicateLocaleError

    const unit = yield* Unit.create({
      ...domainInput,
      modifiedBy: params.userId,
      translations: translationCollection,
    });
    // ↑ Can fail with: UnitInvariantViolationError

    const repo = yield* UnitsRepositoryLive;
    const savedUnit = yield* repo.save(unit);
    // ↑ Can fail with: UnitPersistenceError

    return yield* pipe(
      savedUnit.toRaw(),
      Effect.flatMap(raw => serializeUnitToFormEdit(raw))
    );
    // ↑ Can fail with: ParseError
  });

// Error handling in API route
const result = await UnitsRuntime.runPromiseExit(program);
const errorResponse = handleEffectError(ctx, result);
if (errorResponse) {
  return errorResponse; // ← Typed error responses
}
```

#### 6.2.3. Error Recovery and Transformation

```typescript
// File: apps/api/src/api/v2/utils/error-handler.ts
export function handleEffectError(ctx: Context, result: Exit.Exit<any, any>) {
  if (Exit.isFailure(result)) {
    const error = result.cause._tag === 'Fail' ? result.cause.error : null;

    // Pattern match on error types
    switch (error?._tag) {
      case 'UnitNotFoundError':
        return ctx.json({
          error: 'Unit not found',
          unitId: error.id
        }, 404);

      case 'UnitInvariantViolationError':
        return ctx.json({
          error: 'Business rule violation',
          reason: error.reason,
          unitId: error.unitId
        }, 400);

      case 'DuplicateLocaleError':
        return ctx.json({
          error: 'Validation failed',
          message: error.message
        }, 400);

      case 'UnitPersistenceError':
        return ctx.json({
          error: 'Failed to save unit',
          reason: error.reason
        }, 500);

      default:
        return ctx.json({ error: 'Internal server error' }, 500);
    }
  }
  return null;
}
```

### 6.3. Transaction Boundaries and Consistency Guarantees

#### 6.3.1. Transaction Scope Definition

```typescript
// Repository transaction boundaries
const save = (unit: Unit) =>
  pipe(
    unit.toRaw(),
    Effect.flatMap((unitData) =>
      dbClient.transaction((tx) => // ← Transaction starts here
        Effect.gen(function* () {
          // All operations within this block are atomic

          // 1. Save/update main unit record
          const [savedUnit] = yield* tx((client) => /* ... */);

          // 2. Delete existing translations
          yield* tx((client) => /* ... */);

          // 3. Insert new translations
          yield* tx((client) => /* ... */);

          // 4. Re-fetch for consistency
          const finalUnit = yield* findUnitById(savedUnit.id);

          return finalUnit;
        })
      ) // ← Transaction ends here (commit or rollback)
    )
  );
```

**Transaction Guarantees:**
- **Atomicity**: All operations succeed or all fail
- **Consistency**: Aggregate remains in valid state
- **Isolation**: Concurrent operations don't interfere
- **Durability**: Committed changes are permanent

#### 6.3.2. Consistency Patterns

**Replace-All Pattern for Value Objects:**
```typescript
// Instead of tracking individual changes, we replace all translations
yield* tx((client) =>
  client.delete(DBSchema.unitsI18N)
    .where(eq(DBSchema.unitsI18N.dataId, savedUnit.id))
);

if (unitData.translations.length > 0) {
  yield* tx((client) =>
    client.insert(DBSchema.unitsI18N)
      .values(unitData.translations.map(t => ({
        ...t,
        dataId: unitData.id,
      })))
  );
}
```

**Benefits:**
- Simpler logic (no complex diff calculations)
- Guaranteed consistency (what you send is what you get)
- Aligns with value object immutability
- Prevents orphaned records

## 7. Visual Documentation and Diagrams

This section provides visual representations of the data flow and DDD entity mappings to help understand the architecture at a glance.

### 7.1. Complete Data Flow Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                CLIENT FORM SUBMISSION                                   │
│  {                                                                                      │
│    "isActive": true,                                                                    │
│    "type": {"value": "dept", "label": "Department"},                                    │
│    "names": [{"locale": "fr", "value": "Informatique"}],                                │
│    "description": [{"locale": "fr", "value": "Département d'informatique"}]             │
│  }                                                                                      │
└─────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼
┌────────────────────────────────────────────────────────────────────────────────────────┐
│                              LAYER 1: API ROUTE HANDLER                                │
│  File: apps/api/src/api/v2/routes/units.route.ts                                       │
│                                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. HTTP Request Validation                                                      │   │
│  │    validator('json', UnitFormRequestDtoSchema)                                  │   │
│  │    ↓                                                                            │   │
│  │ 2. Authentication & Authorization                                               │   │
│  │    withPolicy(checkPermission('unit:create'))                                   │   │
│  │    ↓                                                                            │   │
│  │ 3. Effect Program Creation                                                      │   │
│  │    Effect.gen(function* () { ... })                                             │   │
│  └─────────────────────────────────────────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ UnitFormRequestDTO
┌────────────────────────────────────────────────────────────────────────────────────────┐
│                           LAYER 2: APPLICATION SERVICE                                 │
│  File: packages/services/src/services/units.service.ts                                 │
│                                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐   │
│  │ STEP 1: Anti-Corruption Layer (Mapper)                                          │   │
│  │  UnitFormRequestDTO → UnitInput                                                 │   │
│  │  Schema.decode(UnitFormToDomainInput)(params.unitDto)                           │   │
│  └─────────────────────────────────────────────────────────────────────────────────┘   │
│                                            │                                           │
│                                            ▼ UnitInput                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐   │
│  │ STEP 2: Value Object Creation                                                   │   │
│  │  UnitTranslation.create(t) for each translation                                 │   │
│  │  UnitTranslationCollection.create(translationVOs)                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────┘   │
│                                            │                                           │
│                                            ▼ Value Objects                             │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐   │
│  │ STEP 3: Aggregate Root Creation                                                 │   │
│  │  Unit.create({ ...domainInput, translations })                                  │   │
│  │  • Generates unique ID                                                          │   │
│  │  • Validates business invariants                                                │   │
│  └─────────────────────────────────────────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ Unit Aggregate
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              LAYER 3: DOMAIN LAYER                                      │
│  Files: packages/domain/src/aggregates/, packages/domain/src/value-objects/             │
│                                                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ Unit Aggregate Root                                                             │    │
│  │  • validateInvariants()                                                         │    │
│  │    - Must have at least one translation                                         │    │
│  │    - Must have at least one valid name                                          │    │
│  │    - Cannot be its own parent                                                   │    │
│  │  • Encapsulates UnitTranslationCollection                                       │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
│                                            │                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ UnitTranslationCollection (First-Class Collection)                              │    │
│  │  • ensureNoDuplicateLocales()                                                   │    │
│  │  • getCompositeTranslations(locale, fallback)                                   │    │
│  │  • Contains: UnitTranslation[] value objects                                    │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
│                                            │                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ UnitTranslation Value Objects                                                   │    │
│  │  • Immutable                                                                    │    │
│  │  • Self-validating                                                              │    │
│  │  • Value-based equality                                                         │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ Valid Unit Aggregate
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                          LAYER 4: REPOSITORY LAYER                                      │
│  File: packages/repositories/src/units.repository.ts                                    │
│                                                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ STEP 1: Aggregate Dehydration                                                   │    │
│  │  unit.toRaw() → UnitData (persistence format)                                   │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
│                                            │                                            │
│                                            ▼ UnitData                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ STEP 2: Database Transaction                                                    │    │
│  │  dbClient.transaction((tx) => {                                                 │    │
│  │    • INSERT/UPDATE units table                                                  │    │
│  │    • DELETE existing translations                                               │    │
│  │    • INSERT new translations                                                    │    │
│  │    • Re-fetch for consistency                                                   │    │
│  │  })                                                                             │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
│                                            │                                            │
│                                            ▼ Persisted Data                             │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ STEP 3: Aggregate Rehydration                                                   │    │
│  │  Unit.fromDatabaseData(rawData) → Unit Aggregate                                │    │
│  │  • Reconstructs value objects                                                   │    │
│  │  • Validates invariants                                                         │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ Saved Unit Aggregate
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                           LAYER 5: RESPONSE SERIALIZATION                               │
│  File: packages/services/src/serializers/unit.serializer.ts                             │
│                                                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐    │
│  │ Response Transformation                                                         │    │
│  │  savedUnit.toRaw() → serializeUnitToFormEdit(raw) → UnitEditResponseDTO         │    │
│  │  • Formats for client consumption                                               │    │
│  │  • Applies presentation rules                                                   │    │
│  │  • Handles locale fallbacks                                                     │    │
│  └─────────────────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ UnitEditResponseDTO
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                 CLIENT RESPONSE                                         │
│  {                                                                                      │
│    "id": "unit_clx1y2z3a4b5c6d7e8f9",                                                   │
│    "isActive": true,                                                                    │
│    "typeId": "dept",                                                                    │
│    "parentId": "parent_unit_123",                                                       │
│    "translations": [                                                                    │
│      {                                                                                  │
│        "name": {"locale": "fr", "value": "Informatique"},                               │
│        "description": {"locale": "fr", "value": "Département d'informatique"}           │
│      }                                                                                  │
│    ]                                                                                    │
│  }                                                                                      │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

### 7.2. DDD Entity-to-Code Structure Mapping

```
packages/
├── domain/                           ← DOMAIN LAYER (Pure Business Logic)
│   ├── src/
│   │   ├── aggregates/
│   │   │   └── units.aggregate.ts     ← Unit (Aggregate Root)
│   │   │       • Unit.create()       ← Factory with invariant validation
│   │   │       • Unit.update()       ← Business method
│   │   │       • validateInvariants() ← Business rules enforcement
│   │   │
│   │   ├── value-objects/
│   │   │   ├── unit-translation.vo.ts           ← UnitTranslation (Value Object)
│   │   │   │   • UnitTranslation.create()       ← Self-validating factory
│   │   │   │   • Immutable properties           ← Value-based equality
│   │   │   │
│   │   │   └── unit-translation-collection.vo.ts ← UnitTranslationCollection (First-Class Collection)
│   │   │       • ensureNoDuplicateLocales()     ← Collection invariant
│   │   │       • getCompositeTranslations()     ← Domain behavior
│   │   │
│   │   ├── errors/
│   │   │   └── units.error.ts        ← Domain Errors
│   │   │       • UnitNotFoundError   ← Typed error classes
│   │   │       • UnitInvariantViolationError
│   │   │
│   │   └── schemas/
│   │       └── units.schema.ts       ← Domain Schemas
│   │           • UnitInputSchema     ← Domain input validation
│   │           • DbUnitI18NInputSchema
│   │
├── services/                         ← APPLICATION LAYER (Orchestration)
│   ├── src/
│   │   ├── services/
│   │   │   └── units.service.ts      ← UnitsServiceLive (Application Service)
│   │   │       • createUnit()        ← Use case orchestration
│   │   │       • updateUnit()        ← Cross-cutting concerns
│   │   │
│   │   ├── mappers/
│   │   │   └── unit-form.mapper.ts   ← UnitFormToDomainInput (Anti-Corruption Layer)
│   │   │       • Schema.transformOrFail() ← DTO → Domain transformation
│   │   │
│   │   └── serializers/
│   │       └── unit.serializer.ts    ← Response Serializers
│   │           • serializeUnitToDetail() ← Domain → DTO transformation
│   │           • serializeUnitToFormEdit()
│   │
├── repositories/                     ← INFRASTRUCTURE LAYER (Persistence)
│   └── src/
│       └── units.repository.ts       ← UnitsRepositoryLive (Repository)
│           • findUnitById()          ← Collection-like interface
│           • save()                  ← Aggregate persistence
│           • Transaction boundaries  ← Consistency guarantees
│
├── api-contracts/                    ← API BOUNDARY (External Contract)
│   └── src/
│       └── units/
│           ├── requests.dto.ts       ← UnitFormRequestDTO (Input DTO)
│           │   • UnitFormRequestDtoSchema ← API validation
│           │
│           └── responses.dto.ts      ← Response DTOs
│               • UnitDetailResponseDTO ← Output contracts
│               • UnitEditResponseDTO
│
└── apps/
    └── api/
        └── src/
            └── api/v2/routes/
                └── units.route.ts    ← API ROUTES (HTTP Interface)
                    • POST /units     ← HTTP endpoint
                    • PUT /units/:id  ← Request validation
                    • Error handling  ← Response formatting
```

### 7.3. Schema Transformation Flow

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              SCHEMA TRANSFORMATION PIPELINE                             │
└─────────────────────────────────────────────────────────────────────────────────────────┘

CLIENT JSON                    DTO SCHEMA                     DOMAIN SCHEMA                DATABASE SCHEMA
     │                             │                              │                            │
     ▼                             ▼                              ▼                            ▼
┌──────────┐                 ┌──────────────┐              ┌─────────────┐               ┌─────────────┐
│   Raw    │  Validation     │ UnitFormReq  │ Mapper (ACL) │ UnitInput   │  Aggregate    │ UnitData    │
│   JSON   │ ──────────────► │ uestDTO      │ ───────────► │ Schema      │ ────────────► │ (Raw DB)    │
│   Data   │                 │              │              │             │               │             │
└──────────┘                 └──────────────┘              └─────────────┘               └─────────────┘
     │                             │                              │                            │
     │                             │                              │                            │
     ▼                             ▼                              ▼                            ▼
┌──────────┐                 ┌──────────────┐              ┌─────────────┐               ┌─────────────┐
│ {        │                 │ {            │              │ {           │               │ {           │
│   names: │                 │   names:     │              │   transla-  │               │   transla-  │
│   [      │                 │   Array<     │              │   tions:    │               │   tions: [  │
│     {    │                 │     {        │              │   Array<    │               │     {       │
│   locale │                 │   locale:str │              │     {       │               │   locale:str│
│   value  │                 │   value:str  │              │   locale:str│               │   name: str │
│     }    │                 │     }        │              │   name: str │               │   dataId:str│
│   ],     │                 │   >,         │              │   desc: str │               │     },      │
│   desc:  │                 │   desc:      │              │     }       │               │     ...     │
│   [...]  │                 │   Array<...> │              │   >         │               │   ]         │
│ }        │                 │ }            │              │ }           │               │ }           │
└──────────┘                 └──────────────┘              └─────────────┘               └─────────────┘

VALIDATION RULES:            VALIDATION RULES:             VALIDATION RULES:             CONSTRAINTS:
• JSON syntax               • Required fields             • Business invariants         • Foreign keys
• Basic types              • Array structures            • Domain constraints          • Unique indexes
                          • Nested objects              • Value object rules          • Check constraints
```

## 8. Beginner's Guide to DDD Implementation

This section provides a structured learning path for developers new to Domain-Driven Design, using our units domain as the primary example.

### 8.1. Learning Path: From Theory to Practice

#### Step 1: Understanding DDD Concepts
Start with these fundamental concepts and their practical implementations:

**1. Domain Model** - The heart of your business logic
```typescript
// Theoretical: A model that represents the business domain
// Practical: Our Unit aggregate with business rules
export class Unit {
  // Business rule: Unit must have at least one translation
  private validateInvariants(): Effect.Effect<void, UnitInvariantViolationError> {
    if (this.data.translations.isEmpty()) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation'
      }));
    }
    return Effect.succeed(void 0);
  }
}
```

**2. Ubiquitous Language** - Common vocabulary between developers and domain experts
```typescript
// Instead of generic terms like "Item" or "Record"
// We use domain-specific terms like "Unit", "Translation", "Aggregate"

// Bad: Generic naming
class Item { }
class ItemData { }

// Good: Domain-specific naming
class Unit { }           // ← Domain expert understands "Unit"
class UnitTranslation { } // ← Clear business meaning
```

**3. Bounded Context** - Clear boundaries around related concepts
```
Our Units Bounded Context includes:
├── Unit (Aggregate Root)
├── UnitTranslation (Value Object)
├── UnitTranslationCollection (First-Class Collection)
├── UnitType (Reference to another bounded context)
└── Unit-specific business rules

What's NOT in this context:
├── User management (belongs to Identity context)
├── Permissions (belongs to Authorization context)
└── Audit logs (belongs to Auditing context)
```

#### Step 2: Identifying DDD Building Blocks

**Entity vs Value Object Decision Tree:**
```
Does the object have a unique identity that persists over time?
├── YES → Entity
│   └── Example: Unit (has unique ID, can change properties but remains the same unit)
└── NO → Value Object
    └── Example: UnitTranslation (defined by its values: locale + name + description)

Does the object need to enforce business rules across multiple related objects?
├── YES → Aggregate Root
│   └── Example: Unit (manages its translations, enforces consistency)
└── NO → Regular Entity or Value Object
```

**Collection Pattern Decision:**
```
Does your collection need business logic beyond basic array operations?
├── YES → First-Class Collection
│   └── Example: UnitTranslationCollection (enforces no duplicate locales)
└── NO → Regular Array
    └── Example: Simple list of IDs
```

#### Step 3: Implementing the Layers

**Layer Implementation Order:**
1. **Start with Domain Layer** (pure business logic)
2. **Add Application Services** (orchestration)
3. **Implement Repositories** (persistence)
4. **Create API Contracts** (external interface)
5. **Build API Routes** (HTTP endpoints)

### 8.2. DDD Glossary with Code Examples

| DDD Term                        | Definition                                                     | Code Location                                                         | Example                                     |
|---------------------------------|----------------------------------------------------------------|-----------------------------------------------------------------------|---------------------------------------------|
| **Aggregate**                   | A cluster of objects treated as a single unit for data changes | `packages/domain/src/aggregates/`                                     | `Unit` + `UnitTranslationCollection`        |
| **Aggregate Root**              | The only entry point to an aggregate                           | `packages/domain/src/aggregates/units.aggregate.ts`                    | `Unit.create()`, `Unit.update()`            |
| **Value Object**                | An object defined by its values, not identity                  | `packages/domain/src/value-objects/`                                  | `UnitTranslation`                           |
| **First-Class Collection**      | A collection with its own business behavior                    | `packages/domain/src/value-objects/unit-translation-collection.vo.ts` | `UnitTranslationCollection`                 |
| **Entity**                      | An object with unique identity that persists over time         | `packages/domain/src/aggregates/units.aggregate.ts`                    | `Unit` (has unique ID)                      |
| **Domain Service**              | Business logic that doesn't belong to any specific entity      | `packages/domain/src/services/`                                       | Policy validation functions                 |
| **Application Service**         | Orchestrates domain objects and coordinates use cases          | `packages/services/src/services/`                                     | `UnitsServiceLive`                          |
| **Repository**                  | Provides collection-like interface for aggregates              | `packages/repositories/src/`                                          | `UnitsRepositoryLive`                       |
| **Anti-Corruption Layer (ACL)** | Protects domain from external data structures                  | `packages/services/src/mappers/`                                      | `UnitFormToDomainInput`                     |
| **Data Transfer Object (DTO)**  | Simple data structure for crossing boundaries                  | `packages/api-contracts/src/`                                         | `UnitFormRequestDTO`                        |
| **Domain Event**                | Something important that happened in the domain                | `packages/domain/src/events/`                                         | `UnitCreatedEvent` (if implemented)         |
| **Specification**               | Business rule that can be checked                              | Domain methods                                                        | `unit.validateInvariants()`                 |
| **Factory**                     | Creates complex objects with validation                        | Static methods                                                        | `Unit.create()`, `UnitTranslation.create()` |
| **Invariant**                   | A condition that must always be true                           | Private validation methods                                            | `validateInvariants()`                      |

### 8.3. Before/After Code Examples

#### Example 1: Anemic Domain Model → Rich Domain Model

**Before (Anemic - Business logic in service):**
```typescript
// Service contains business logic (BAD)
class UnitsService {
  createUnit(data: any) {
    // Business logic mixed with orchestration
    if (!data.translations || data.translations.length === 0) {
      throw new Error('Unit must have translations');
    }

    const locales = data.translations.map(t => t.locale);
    if (new Set(locales).size !== locales.length) {
      throw new Error('Duplicate locales not allowed');
    }

    // More business logic...
    return this.repository.save(data);
  }
}

// Domain object is just data (BAD)
class Unit {
  constructor(
    public id: string,
    public translations: Translation[]
  ) {}
}
```

**After (Rich Domain Model - Business logic in domain):**
```typescript
// Service only orchestrates (GOOD)
class UnitsServiceLive {
  createUnit(params: { unitDto: UnitFormRequestDTO; userId: string }) {
    return Effect.gen(function* () {
      // 1. Transform external data
      const domainInput = yield* Schema.decode(UnitFormToDomainInput)(params.unitDto);

      // 2. Create value objects (with their own validation)
      const translationVOs = yield* Effect.all(
        domainInput.translations.map(t => UnitTranslation.create(t))
      );
      const translationCollection = yield* UnitTranslationCollection.create(translationVOs);

      // 3. Create aggregate (with business rules)
      const unit = yield* Unit.create({
        ...domainInput,
        modifiedBy: params.userId,
        translations: translationCollection,
      });

      // 4. Persist
      const repo = yield* UnitsRepositoryLive;
      return yield* repo.save(unit);
    });
  }
}

// Domain object contains business logic (GOOD)
export class Unit {
  private constructor(private readonly data: UnitAggregateState) {}

  static create(props: CreateProps): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(UnitAggregateState({
      id: DbUtils.cuid2(),
      // ... other properties
    }));

    // Business rules enforced in domain
    return pipe(
      newUnit.validateInvariants(),
      Effect.map(() => newUnit)
    );
  }

  private validateInvariants(): Effect.Effect<void, UnitInvariantViolationError> {
    // Business logic lives here
    if (this.data.translations.isEmpty()) {
      return Effect.fail(new UnitInvariantViolationError({
        unitId: this.data.id,
        reason: 'Unit must have at least one translation'
      }));
    }
    return Effect.succeed(void 0);
  }
}
```

#### Example 2: Primitive Obsession → Value Objects

**Before (Primitive Obsession):**
```typescript
// Using primitive strings everywhere (BAD)
class Unit {
  constructor(
    public id: string,
    public frenchName: string,
    public englishName: string,
    public frenchDescription: string,
    public englishDescription: string
  ) {}

  // Business logic scattered and repetitive
  getDisplayName(locale: string): string {
    if (locale === 'fr' && this.frenchName) {
      return this.frenchName;
    }
    if (locale === 'en' && this.englishName) {
      return this.englishName;
    }
    return this.frenchName || this.englishName || 'Unnamed Unit';
  }
}
```

**After (Value Objects):**
```typescript
// Rich value objects with behavior (GOOD)
export class UnitTranslation {
  private constructor(private readonly data: UnitTranslationFields) {}

  static create(input: unknown): Effect.Effect<UnitTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbUnitI18NDataSchema)(input),
      Effect.map(validatedData => new UnitTranslation(validatedData))
    );
  }

  getLocale(): string { return this.data.locale; }
  getName(): string { return this.data.name; }
  getDescription(): string | null { return this.data.description; }

  hasValidName(): boolean {
    return this.data.name !== null && this.data.name.trim() !== '';
  }
}

export class UnitTranslationCollection {
  private constructor(public readonly translations: readonly UnitTranslation[]) {}

  // Encapsulated business behavior
  getCompositeTranslations(locale: string, fallbackLocale: string) {
    const primary = this.findByLocale(locale);
    const fallback = this.findByLocale(fallbackLocale);

    return {
      name: primary?.getName() || fallback?.getName() || null,
      description: primary?.getDescription() || fallback?.getDescription() || null,
    };
  }

  private findByLocale(locale: string): UnitTranslation | undefined {
    return this.translations.find(t => t.getLocale() === locale);
  }
}

// Clean aggregate with encapsulated behavior
export class Unit {
  getCompositeTranslations(locale: string, fallbackLocale = 'en') {
    return this.data.translations.getCompositeTranslations(locale, fallbackLocale);
  }
}
```

### 8.4. Common Pitfalls and Solutions

| Pitfall | Problem | Solution | Example |
|---------|---------|----------|---------|
| **Anemic Domain Model** | Business logic in services | Move logic to domain objects | Business rules in `Unit.validateInvariants()` |
| **Primitive Obsession** | Using primitives everywhere | Create value objects | `UnitTranslation` instead of raw strings |
| **Large Aggregates** | Aggregate tries to do everything | Split into smaller aggregates | Separate `Unit` from `UnitType` |
| **Repository Leakage** | Repository returns raw data | Return domain objects | `Unit.fromDatabaseData()` |
| **DTO Contamination** | DTOs used in domain layer | Use mappers/ACL | `UnitFormToDomainInput` mapper |
| **Missing Invariants** | Invalid states possible | Add validation | `ensureNoDuplicateLocales()` |
| **Transaction Boundaries** | Inconsistent data | Define clear boundaries | Repository transaction scope |

### 8.5. Quick Reference: "When to Use What"

**Use Aggregate Root when:**
- Object has unique identity
- Object needs to enforce business rules
- Object coordinates multiple related objects
- Example: `Unit` manages its translations

**Use Value Object when:**
- Object is defined by its values
- Object is immutable
- Two objects with same values are interchangeable
- Example: `UnitTranslation`

**Use First-Class Collection when:**
- Collection has business rules
- Collection provides domain-specific behavior
- Collection represents a domain concept
- Example: `UnitTranslationCollection`

**Use Application Service when:**
- Orchestrating multiple domain objects
- Handling cross-cutting concerns
- Coordinating with external systems
- Example: `UnitsServiceLive.createUnit()`

**Use Repository when:**
- Providing collection-like interface for aggregates
- Handling persistence concerns
- Abstracting database technology
- Example: `UnitsRepositoryLive`

This document serves as both a design decision record and implementation guide. All architectural decisions should be evaluated against these principles, and any deviations should be documented with clear justification.

---
