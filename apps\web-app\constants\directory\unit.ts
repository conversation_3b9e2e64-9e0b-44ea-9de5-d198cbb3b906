import { NULLABLE_SELECT_OPTION } from '@/constants/common';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const initialColumnVisibility = {
  acronym: true,
  lastUpdatedAt: true,
  name: true,
  parentUnit: true,
} as const;

export const unitFormSections = {
  affiliations: {
    key: 'affiliations',
    order: 2,
  },
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const unitFormDefaultValues = (
  locale: SupportedLocale,
): UnitFormSchema => ({
  alias: [{ locale, value: '' }],
  names: [{ locale, value: '' }],
  parentUnit: NULLABLE_SELECT_OPTION,
  pseudonym: [{ locale, value: '' }],
  relatedOrganizations: [],
  unitType: '1',
});
