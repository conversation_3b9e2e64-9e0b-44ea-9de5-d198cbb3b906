import * as Schema from 'effect/Schema';

/**
 * Schema that normalizes empty strings, undefined, and null to null.
 * Used for API input sanitization at the anti-corruption layer boundary.
 */
export const NormalizedNullableStringSchema = Schema.Union(
  Schema.String,
  Schema.Null,
  Schema.Undefined,
).pipe(
  Schema.transform(Schema.NullOr(Schema.String), {
    strict: true,
    decode: (value) => {
      if (value === null || value === undefined || value.trim() === '') {
        return null;
      }
      return value;
    },
    encode: (value) => value,
  }),
);
