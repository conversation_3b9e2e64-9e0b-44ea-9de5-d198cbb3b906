import { ExcellenceHubI18nMigrationConverter } from '../converters/27-02-convert-excellence-hub-i18n-inserts';

async function convertExcellenceHubI18nData() {
  try {
    // Create converter instance
    const converter = new ExcellenceHubI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();
  } catch (error) {
    console.error('Error converting excellence hub i18n data:', error);
    process.exit(1);
  }
}

// Run the conversion
convertExcellenceHubI18nData();
