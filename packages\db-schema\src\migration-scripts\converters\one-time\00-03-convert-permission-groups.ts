import { DbUtils } from '@rie/utils';
import { OUTPUT_PATH } from '../../constants';
import { BaseConverter } from '../base-converter';

interface PermissionGroup {
  name: string;
  description: string;
  permissions: string[];
}

export class PermissionGroupsMigrationConverter extends BaseConverter {
  private permissionGroupIdMappings: Record<string, string> = {};
  private permissionIdMappings: Record<string, string> = {};

  async convertFile(): Promise<void> {
    // Load existing permission mappings
    this.permissionIdMappings = await this.loadEntityIdMappings('permissions');

    // Define permission groups from the Permission Groups Catalog
    const permissionGroups: PermissionGroup[] = [
      // Domain: User (User Management)
      {
        name: 'CanViewUsers',
        description: 'Allows viewing, searching, and sorting users.',
        permissions: ['user-read'],
      },
      {
        name: 'CanCreateUser',
        description: 'Allows creating a new user.',
        permissions: ['user-create', 'user-read'],
      },
      {
        name: 'CanEditU<PERSON>',
        description: 'Allows modifying and deactivating a user.',
        permissions: ['user-update', 'user-read'],
      },
      {
        name: '<PERSON><PERSON><PERSON><PERSON>U<PERSON>',
        description: 'Allows deleting a user.',
        permissions: ['user-delete', 'user-read'],
      },
      {
        name: 'UserManagement',
        description: 'Full management rights over users.',
        permissions: ['user-create', 'user-read', 'user-update', 'user-delete'],
      },

      // Domain: PermissionGroup (Group Management)
      {
        name: 'CanViewPermissionGroups',
        description: 'Allows viewing the list of groups.',
        permissions: ['user-read'], // Permission groups are managed through user domain
      },
      {
        name: 'CanManagePermissionGroupMembers',
        description: 'Allows assigning/removing users from groups.',
        permissions: ['user-update'], // Managing group membership requires user update permissions
      },

      // Domain: Infrastructure
      {
        name: 'CanViewInfrastructures',
        description: 'Allows viewing infrastructures and exporting.',
        permissions: ['infrastructure-read'],
      },
      {
        name: 'CanCreateInfrastructure',
        description: 'Allows creating an infrastructure.',
        permissions: ['infrastructure-create', 'infrastructure-read'],
      },
      {
        name: 'CanEditInfrastructure',
        description: 'Allows modifying an infrastructure.',
        permissions: ['infrastructure-update', 'infrastructure-read'],
      },
      {
        name: 'CanDeleteInfrastructure',
        description: 'Allows deleting an infrastructure.',
        permissions: ['infrastructure-delete', 'infrastructure-read'],
      },
      {
        name: 'InfrastructureManagement',
        description: 'Full management rights over infrastructures.',
        permissions: [
          'infrastructure-create',
          'infrastructure-read',
          'infrastructure-update',
          'infrastructure-delete',
        ],
      },

      // Domain: Equipment
      {
        name: 'CanViewEquipment',
        description: 'Allows viewing equipment, its details, and exporting.',
        permissions: ['equipment-read'],
      },
      {
        name: 'CanCreateEquipment',
        description: 'Allows creating new equipment.',
        permissions: ['equipment-create', 'equipment-read'],
      },
      {
        name: 'CanEditEquipment',
        description: 'Allows modifying equipment.',
        permissions: ['equipment-update', 'equipment-read'],
      },
      {
        name: 'CanDeleteEquipment',
        description: 'Allows deleting equipment.',
        permissions: ['equipment-delete', 'equipment-read'],
      },
      {
        name: 'EquipmentManagement',
        description: 'Full management rights over equipment.',
        permissions: [
          'equipment-create',
          'equipment-read',
          'equipment-update',
          'equipment-delete',
        ],
      },

      // Domain: Directory
      {
        name: 'CanAccessDirectory',
        description: 'Allows having the "Directory" tab in the menu.',
        permissions: ['people-read'], // Directory access requires people read permissions
      },
      {
        name: 'CanCreateDirectoryEntity',
        description: 'Allows creating a generic directory object.',
        permissions: ['people-create'], // Creating directory entities maps to people create
      },
      {
        name: 'CanEditDirectoryEntity',
        description: 'Allows modifying and deactivating a directory object.',
        permissions: ['people-update', 'people-read'],
      },
      {
        name: 'CanDeleteDirectoryEntity',
        description: 'Allows deleting a directory object.',
        permissions: ['people-delete', 'people-read'],
      },
      {
        name: 'CanManageDirectoryEntityPermissions',
        description: 'Allows modifying the "Editing Permissions" section.',
        permissions: ['user-update'], // Managing permissions requires user update
      },

      // Domain: Building
      {
        name: 'CanViewBuilding',
        description: 'Allows viewing buildings and their details.',
        permissions: ['building-read'],
      },
      {
        name: 'CanCreateBuilding',
        description: 'Allows creating new buildings.',
        permissions: ['building-create'],
      },
      {
        name: 'CanEditBuilding',
        description: 'Allows modifying building information.',
        permissions: ['building-update'],
      },
      {
        name: 'CanDeleteBuilding',
        description: 'Allows deleting buildings.',
        permissions: ['building-delete'],
      },
      {
        name: 'BuildingManagement',
        description: 'Full management rights over buildings.',
        permissions: [
          'building-read',
          'building-create',
          'building-update',
          'building-delete',
        ],
      },

      // Domain: Campus
      {
        name: 'CanViewCampus',
        description: 'Allows viewing campus information and details.',
        permissions: ['campus-read'],
      },
      {
        name: 'CanCreateCampus',
        description: 'Allows creating new campus entries.',
        permissions: ['campus-create'],
      },
      {
        name: 'CanEditCampus',
        description: 'Allows modifying campus information.',
        permissions: ['campus-update'],
      },
      {
        name: 'CanDeleteCampus',
        description: 'Allows deleting campus entries.',
        permissions: ['campus-delete'],
      },
      {
        name: 'CampusManagement',
        description: 'Full management rights over campus data.',
        permissions: [
          'campus-read',
          'campus-create',
          'campus-update',
          'campus-delete',
        ],
      },

      // Domain: Funding Project
      {
        name: 'CanViewFundingProject',
        description: 'Allows viewing funding projects and their details.',
        permissions: ['fundingProject-read'],
      },
      {
        name: 'CanCreateFundingProject',
        description: 'Allows creating new funding projects.',
        permissions: ['fundingProject-create'],
      },
      {
        name: 'CanEditFundingProject',
        description: 'Allows modifying funding project information.',
        permissions: ['fundingProject-update'],
      },
      {
        name: 'CanDeleteFundingProject',
        description: 'Allows deleting funding projects.',
        permissions: ['fundingProject-delete'],
      },
      {
        name: 'FundingProjectManagement',
        description: 'Full management rights over funding projects.',
        permissions: [
          'fundingProject-read',
          'fundingProject-create',
          'fundingProject-update',
          'fundingProject-delete',
        ],
      },

      // Domain: Institution
      {
        name: 'CanViewInstitution',
        description: 'Allows viewing institution information.',
        permissions: ['institution-read'],
      },
      {
        name: 'CanCreateInstitution',
        description: 'Allows creating new institutions.',
        permissions: ['institution-create'],
      },
      {
        name: 'CanEditInstitution',
        description: 'Allows modifying institution information.',
        permissions: ['institution-update'],
      },
      {
        name: 'CanDeleteInstitution',
        description: 'Allows deleting institutions.',
        permissions: ['institution-delete'],
      },
      {
        name: 'InstitutionManagement',
        description: 'Full management rights over institutions.',
        permissions: [
          'institution-read',
          'institution-create',
          'institution-update',
          'institution-delete',
        ],
      },

      // Domain: People
      {
        name: 'CanViewPeople',
        description: 'Allows viewing people profiles and information.',
        permissions: ['people-read'],
      },
      {
        name: 'CanCreatePeople',
        description: 'Allows creating new people profiles.',
        permissions: ['people-create'],
      },
      {
        name: 'CanEditPeople',
        description: 'Allows modifying people information.',
        permissions: ['people-update'],
      },
      {
        name: 'CanDeletePeople',
        description: 'Allows deleting people profiles.',
        permissions: ['people-delete'],
      },
      {
        name: 'PeopleManagement',
        description: 'Full management rights over people data.',
        permissions: [
          'people-read',
          'people-create',
          'people-update',
          'people-delete',
        ],
      },

      // Domain: Room
      {
        name: 'CanViewRoom',
        description: 'Allows viewing room information and details.',
        permissions: ['room-read'],
      },
      {
        name: 'CanCreateRoom',
        description: 'Allows creating new rooms.',
        permissions: ['room-create'],
      },
      {
        name: 'CanEditRoom',
        description: 'Allows modifying room information.',
        permissions: ['room-update'],
      },
      {
        name: 'CanDeleteRoom',
        description: 'Allows deleting rooms.',
        permissions: ['room-delete'],
      },
      {
        name: 'RoomManagement',
        description: 'Full management rights over rooms.',
        permissions: ['room-read', 'room-create', 'room-update', 'room-delete'],
      },

      // Domain: Unit
      {
        name: 'CanViewUnit',
        description: 'Allows viewing unit information and details.',
        permissions: ['unit-read'],
      },
      {
        name: 'CanCreateUnit',
        description: 'Allows creating new units.',
        permissions: ['unit-create'],
      },
      {
        name: 'CanEditUnit',
        description: 'Allows modifying unit information.',
        permissions: ['unit-update'],
      },
      {
        name: 'CanDeleteUnit',
        description: 'Allows deleting units.',
        permissions: ['unit-delete'],
      },
      {
        name: 'UnitManagement',
        description: 'Full management rights over units.',
        permissions: ['unit-read', 'unit-create', 'unit-update', 'unit-delete'],
      },

      // Domain: Vendor
      {
        name: 'CanViewVendor',
        description: 'Allows viewing vendor information and details.',
        permissions: ['vendor-read'],
      },
      {
        name: 'CanCreateVendor',
        description: 'Allows creating new vendors.',
        permissions: ['vendor-create'],
      },
      {
        name: 'CanEditVendor',
        description: 'Allows modifying vendor information.',
        permissions: ['vendor-update'],
      },
      {
        name: 'CanDeleteVendor',
        description: 'Allows deleting vendors.',
        permissions: ['vendor-delete'],
      },
      {
        name: 'VendorManagement',
        description: 'Full management rights over vendors.',
        permissions: [
          'vendor-read',
          'vendor-create',
          'vendor-update',
          'vendor-delete',
        ],
      },

      // Domain: Controlled Lists
      {
        name: 'CanAccessControlledLists',
        description: 'Allows viewing all controlled lists entities.',
        permissions: [
          'applicationSector-read',
          'documentationCategory-read',
          'equipmentCategory-read',
          'equipmentStatus-read',
          'equipmentType-read',
          'excellenceHub-read',
          'fundingProjectType-read',
          'fundingProjectIdentifierType-read',
          'innovationLab-read',
          'infrastructureStatus-read',
          'infrastructureType-read',
          'institutionType-read',
          'mediaType-read',
          'peopleRoleType-read',
          'roomCategory-read',
          'researchField-read',
          'technique-read',
          'unitType-read',
          'visibility-read',
        ],
      },
      {
        name: 'CanCreateControlledListEntity',
        description: 'Allows creating an item in a controlled list entity.',
        permissions: [
          'applicationSector-create',
          'documentationCategory-create',
          'equipmentCategory-create',
          'equipmentStatus-create',
          'equipmentType-create',
          'excellenceHub-create',
          'fundingProjectType-create',
          'fundingProjectIdentifierType-create',
          'innovationLab-create',
          'infrastructureStatus-create',
          'infrastructureType-create',
          'institutionType-create',
          'mediaType-create',
          'peopleRoleType-create',
          'roomCategory-create',
          'researchField-create',
          'technique-create',
          'unitType-create',
          'visibility-create',
        ],
      },
      {
        name: 'CanEditControlledListEntity',
        description: 'Allows modifying items in a controlled list entity.',
        permissions: [
          'applicationSector-update',
          'documentationCategory-update',
          'equipmentCategory-update',
          'equipmentStatus-update',
          'equipmentType-update',
          'excellenceHub-update',
          'fundingProjectType-update',
          'fundingProjectIdentifierType-update',
          'innovationLab-update',
          'infrastructureStatus-update',
          'infrastructureType-update',
          'institutionType-update',
          'mediaType-update',
          'peopleRoleType-update',
          'roomCategory-update',
          'researchField-update',
          'technique-update',
          'unitType-update',
          'visibility-update',
        ],
      },
      {
        name: 'CanDeleteControlledListEntity',
        description: 'Allows deleting an item in a controlled list entity.',
        permissions: [
          'applicationSector-delete',
          'documentationCategory-delete',
          'equipmentCategory-delete',
          'equipmentStatus-delete',
          'equipmentType-delete',
          'excellenceHub-delete',
          'fundingProjectType-delete',
          'fundingProjectIdentifierType-delete',
          'innovationLab-delete',
          'infrastructureStatus-delete',
          'infrastructureType-delete',
          'institutionType-delete',
          'mediaType-delete',
          'peopleRoleType-delete',
          'roomCategory-delete',
          'researchField-delete',
          'technique-delete',
          'unitType-delete',
          'visibility-delete',
        ],
      },
      {
        name: 'CanManageControlledList',
        description: 'Allows managing a controlled list.',
        permissions: [
          'applicationSector-create',
          'applicationSector-read',
          'applicationSector-update',
          'applicationSector-delete',
          'documentationCategory-create',
          'documentationCategory-read',
          'documentationCategory-update',
          'documentationCategory-delete',
          'equipmentCategory-create',
          'equipmentCategory-read',
          'equipmentCategory-update',
          'equipmentCategory-delete',
          'equipmentStatus-create',
          'equipmentStatus-read',
          'equipmentStatus-update',
          'equipmentStatus-delete',
          'equipmentType-create',
          'equipmentType-read',
          'equipmentType-update',
          'equipmentType-delete',
          'excellenceHub-create',
          'excellenceHub-read',
          'excellenceHub-update',
          'excellenceHub-delete',
          'fundingProjectType-create',
          'fundingProjectType-read',
          'fundingProjectType-update',
          'fundingProjectType-delete',
          'fundingProjectIdentifierType-create',
          'fundingProjectIdentifierType-read',
          'fundingProjectIdentifierType-update',
          'fundingProjectIdentifierType-delete',
          'innovationLab-create',
          'innovationLab-read',
          'innovationLab-update',
          'innovationLab-delete',
          'infrastructureStatus-create',
          'infrastructureStatus-read',
          'infrastructureStatus-update',
          'infrastructureStatus-delete',
          'infrastructureType-create',
          'infrastructureType-read',
          'infrastructureType-update',
          'infrastructureType-delete',
          'institutionType-create',
          'institutionType-read',
          'institutionType-update',
          'institutionType-delete',
          'mediaType-create',
          'mediaType-read',
          'mediaType-update',
          'mediaType-delete',
          'peopleRoleType-create',
          'peopleRoleType-read',
          'peopleRoleType-update',
          'peopleRoleType-delete',
          'roomCategory-create',
          'roomCategory-read',
          'roomCategory-update',
          'roomCategory-delete',
          'researchField-create',
          'researchField-read',
          'researchField-update',
          'researchField-delete',
          'technique-create',
          'technique-read',
          'technique-update',
          'technique-delete',
          'unitType-create',
          'unitType-read',
          'unitType-update',
          'unitType-delete',
          'visibility-create',
          'visibility-read',
          'visibility-update',
          'visibility-delete',
        ],
      },

      // Access Levels (Data Visibility)
      {
        name: 'CanViewUdeMData',
        description: 'Unlocks reading data with "UdeM" visibility.',
        permissions: ['visibility-read'],
      },
      {
        name: 'CanViewPartnerData',
        description: 'Unlocks reading data with "UdeM & Partners" visibility.',
        permissions: ['visibility-read'],
      },
      {
        name: 'CanViewPrivateData',
        description: 'Unlocks reading "private" data.',
        permissions: ['visibility-read'],
      },
    ];

    // Generate permission groups INSERT statement
    let permissionGroupsInsertStatement =
      'INSERT INTO permission_groups (id, name, description) VALUES\n';

    // Generate permission groups and collect relationships
    const permissionGroupRelationships: Array<{
      groupId: string;
      permissionId: string;
    }> = [];

    permissionGroups.forEach((group, index) => {
      const groupId = DbUtils.cuid2();
      permissionGroupsInsertStatement += `('${groupId}', '${group.name}', '${group.description}')${index === permissionGroups.length - 1 ? ';' : ',\n'}`;
      this.permissionGroupIdMappings[group.name] = groupId;

      // Process permissions for this group
      for (const permissionKey of group.permissions) {
        const permissionId =
          this.permissionIdMappings[
            permissionKey as keyof typeof this.permissionIdMappings
          ];
        if (permissionId) {
          permissionGroupRelationships.push({ groupId, permissionId });
        } else {
          console.warn(`Permission ID not found for key: ${permissionKey}`);
        }
      }
    });

    // Generate permission group permissions INSERT statement
    let permissionGroupPermissionsInsertStatement = '';
    if (permissionGroupRelationships.length > 0) {
      permissionGroupPermissionsInsertStatement =
        '\nINSERT INTO permission_group_permissions (group_id, permission_id) VALUES\n';

      permissionGroupRelationships.forEach((relationship, index) => {
        permissionGroupPermissionsInsertStatement += `('${relationship.groupId}', '${relationship.permissionId}')${index === permissionGroupRelationships.length - 1 ? ';' : ',\n'}`;
      });
    }

    // Create the output directory if it doesn't exist
    const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
    await this.safeMkdir(outputDir, { recursive: true });

    await this.writeMappingsToJson(
      outputDir,
      { mysql: 'permission_groups', postgres: 'permission_groups' },
      this.permissionGroupIdMappings,
    );

    console.log(permissionGroupsInsertStatement);
    console.log(permissionGroupPermissionsInsertStatement);

    // Append the SQL content to the output file
    await this.safeAppendFile(
      OUTPUT_PATH,
      permissionGroupsInsertStatement +
        permissionGroupPermissionsInsertStatement,
    );
    console.log('Permission groups and relationships data added successfully!');
  }
}
