import { selectOptionSchema } from '@/schemas/common-schema';
import { z } from 'zod';

export const getFundingProjectFormSchema = (
    t: (val: string, args?: Record<string, number | string>) => string,
) => {
    const nameSchema = z
        .array(
            z.object({
                locale: z.string().trim(),
                value: z
                    .string()
                    .trim()
                    .max(150, {
                        message: t('name.error.max', {
                            max: 150,
                        }),
                    }),
            }),
        )
        .refine((data) => data.some(({ value }) => value !== ''), {
            message: t('name.error.required'),
        });

    const descriptionSchema = z
        .array(
            z.object({
                locale: z.string().trim(),
                value: z
                    .string()
                    .trim()
                    .max(1500, {
                        message: t('description.error.max', {
                            max: 1500,
                        }),
                    }),
            }),
        );

    const obtainingYearSchema = z
        .number()
        .int()
        .min(1900, {
            message: t('obtainingYear.error.min', {
                min: 1900,
            }),
        })
        .max(new Date().getFullYear() + 10, {
            message: t('obtainingYear.error.max', {
                max: new Date().getFullYear() + 10,
            }),
        })
        .optional();

    return z.object({
        id: z.string().optional(),
        name: nameSchema,
        description: descriptionSchema,
        holder: selectOptionSchema(t('holder.error.required')),
        fundingType: selectOptionSchema(t('fundingType.error.required')),
        obtainingYear: obtainingYearSchema,
        endDate: z.string().optional(),
        fciId: z.string().optional(),
        synchroId: z.string().optional(),
        purchasedEquipment: z.array(selectOptionSchema()).default([]),
        associateResearchers: z.array(selectOptionSchema()).default([]),
        financedInfrastructures: z.array(selectOptionSchema()).default([]),
    });
};

export type FundingProjectFormSchema = z.infer<ReturnType<typeof getFundingProjectFormSchema>>;