import { handleEffectError } from '@/api/v2/utils/error-handler';
import { EquipmentsRuntime } from '@/infrastructure/runtimes/equipments.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  EquipmentInputSchema,
  EquipmentSchema,
  ResourceIdSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { EquipmentsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  permissionForResource,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllEquipmentsRoute = describeRoute({
  description:
    'Lister tous les équipements avec filtrage optimisé au niveau base de données. Les utilisateurs non authentifiés voient uniquement les équipements dont l\'infrastructure parent a une visibilité "public". Les utilisateurs authentifiés voient les équipements selon leurs permissions.',
  operationId: 'getAllEquipments',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(EquipmentSchema)),
        },
      },
      description:
        'Équipements retournés (infrastructures publiques pour les utilisateurs non authentifiés, filtrés selon les permissions pour les utilisateurs authentifiés)',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const getEquipmentByIdRoute = describeRoute({
  description:
    'Obtenir un équipement par ID. Les utilisateurs non authentifiés peuvent accéder aux équipements dont l\'infrastructure parent a une visibilité "public". Les utilisateurs authentifiés utilisent le système de permissions hiérarchique.',
  operationId: 'getEquipmentById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
      example: 'equip123abc456def789ghi',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement trouvé',
    },
    403: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        'Accès interdit - Équipement non public ou permissions insuffisantes',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const createEquipmentRoute = describeRoute({
  description: 'Créer un équipement',
  operationId: 'createEquipment',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(EquipmentInputSchema),
        example: {
          model: 'Advanced Microscope X1000',
          serialNumber: 'AM-X1000-2024-001',
          typeId: 'equipment_type_001',
          useInClinicalTrial: false,
          isHidden: false,
          workingPercentage: 95.5,
          monetaryCost: 150000.0,
          manufactureYear: 2024,
          acquisitionDate: '2024-01-15',
          installationDate: '2024-02-01',
          scientificManagerId: 'w4ku5n46mdvkv5bjuuml2j3w',
          manufacturerId: 'wjbwu31n6ifz8ijm7g7v2b2m',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          isFeatured: false,
          translations: [
            {
              locale: 'en',
              name: 'Advanced Research Microscope',
              description:
                'High-resolution microscope for advanced research applications',
              specification: 'Resolution: 0.1nm, Magnification: 1000x-100000x',
              usageContext: 'Research laboratories, material science',
              risk: 'Low risk when operated by trained personnel',
              comment: 'Requires specialized training for operation',
            },
            {
              locale: 'fr',
              name: 'Microscope de Recherche Avancé',
              description:
                'Microscope haute résolution pour applications de recherche avancées',
              specification: 'Résolution: 0.1nm, Grossissement: 1000x-100000x',
              usageContext: 'Laboratoires de recherche, science des matériaux',
              risk: "Risque faible lorsqu'utilisé par du personnel formé",
              comment: "Nécessite une formation spécialisée pour l'utilisation",
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Clé étrangère non trouvée - Le type, gestionnaire, manufacturier ou institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const updateEquipmentRoute = describeRoute({
  description: 'Mettre à jour un équipement',
  operationId: 'updateEquipment',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
      example: 'equip123abc456def789ghi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(EquipmentInputSchema),
        example: {
          model: 'Updated Advanced Microscope X2000',
          serialNumber: 'AM-X2000-2024-001',
          typeId: 'equipment_type_002',
          workingPercentage: 98.0,
          monetaryCost: 175000.0,
          manufactureYear: 2024,
          acquisitionDate: '2024-03-15',
          installationDate: '2024-04-01',
          scientificManagerId: 'w4ku5n46mdvkv5bjuuml2j3w',
          manufacturerId: 'wjbwu31n6ifz8ijm7g7v2b2m',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          isFeatured: true,
          translations: [
            {
              locale: 'en',
              name: 'Updated Advanced Research Microscope',
              description:
                'Updated high-resolution microscope for advanced research',
              specification: 'Resolution: 0.05nm, Magnification: 2000x-200000x',
              usageContext: 'Advanced research laboratories, nanotechnology',
              risk: 'Low risk with enhanced safety features',
              comment: 'Updated model with improved capabilities',
            },
            {
              locale: 'fr',
              name: 'Microscope de Recherche Avancé Mis à Jour',
              description:
                'Microscope haute résolution mis à jour pour la recherche avancée',
              specification: 'Résolution: 0.05nm, Grossissement: 2000x-200000x',
              usageContext:
                'Laboratoires de recherche avancés, nanotechnologie',
              risk: 'Risque faible avec fonctionnalités de sécurité améliorées',
              comment: 'Modèle mis à jour avec capacités améliorées',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé ou clé étrangère non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const deleteEquipmentRoute = describeRoute({
  description: 'Supprimer un équipement',
  operationId: 'deleteEquipment',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Équipement supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

const equipmentsRoute = new Hono<{
  Variables: HonoVariables;
}>();

equipmentsRoute.get('/', getAllEquipmentsRoute, async (ctx) => {
  // For getAllEquipments, we allow both authenticated and unauthenticated access
  // Unauthenticated users get public equipments, authenticated users get filtered results
  const program = Effect.gen(function* () {
    const user = ctx.get('user');
    const equipmentService = yield* EquipmentsServiceLive;
    return yield* equipmentService.getAllEquipments(user?.id);
  });

  const result = await EquipmentsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

equipmentsRoute.get(
  '/:id',
  getEquipmentByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const user = ctx.get('user');
    const id = ctx.req.param('id');

    // Allow both authenticated and unauthenticated access
    // Unauthenticated users can access public equipments, authenticated users use permission system
    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.getEquipmentById(id, user?.id);
    });

    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.post(
  '/',
  createEquipmentRoute,
  validator('json', EquipmentInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.createEquipment({
        ...body,
        modifiedBy: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('equipment:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.put(
  '/:id',
  updateEquipmentRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', EquipmentInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.updateEquipment({
        id,
        equipment: {
          ...body,
          modifiedBy: user.id,
        },
      });
    }).pipe(
      withPolicy(permissionForResource('equipment', 'update', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.delete(
  '/:id',
  deleteEquipmentRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.deleteEquipment(id);
    }).pipe(
      withPolicy(permissionForResource('equipment', 'delete', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Equipment deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { equipmentsRoute };
