import {
  type AppPathnames,
  type AppPathnamesWithId,
  type SideMenuPathnames,
  pathnames,
} from '@/i18n/settings';
import type { SupportedLocale } from '@/types/locale';

function escapeRegex(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export const getRedirectPath = (
  fromUrl: string,
  locale: SupportedLocale,
):
  | {
      pathname: AppPathnamesWithId;
      params: { id: string };
    }
  | { pathname: SideMenuPathnames } => {
  const decodedUrl = decodeURIComponent(fromUrl);
  const fromPath = decodedUrl.startsWith('/') ? decodedUrl : `/${decodedUrl}`;

  for (const key in pathnames) {
    const pathDefinition = pathnames[key as AppPathnames];
    const localizedPath = pathDefinition[locale as keyof typeof pathDefinition];
    // Find parameter names in the localized path (e.g., [id])
    const paramNames = (localizedPath.match(/\[([^\]]+)\]/g) || []).map((p) =>
      p.slice(1, -1),
    );

    // Create regex pattern by escaping the path and replacing [param] with capture groups
    let regexString = escapeRegex(localizedPath);
    for (const paramName of paramNames) {
      regexString = regexString.replace(`\\[${paramName}\\]`, '([^/]+)');
    }

    const regex = new RegExp(`^${regexString}$`);
    const match = fromPath.match(regex);

    if (match) {
      // If there are parameters, extract them
      if (paramNames.length > 0) {
        const params = paramNames.reduce<{ id: string }>(
          (acc, paramName, index) => {
            if (paramName === 'id') {
              acc.id = match[index + 1];
            }
            return acc;
          },
          {} as { id: string },
        );

        return {
          pathname: key as AppPathnamesWithId,
          params,
        };
      }

      // No parameters, return just the pathname
      return {
        pathname: key as SideMenuPathnames,
      };
    }
  }

  return { pathname: '/' as const };
};
