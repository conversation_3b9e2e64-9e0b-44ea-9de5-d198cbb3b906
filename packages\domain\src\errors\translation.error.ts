import * as Data from 'effect/Data';

/**
 * Generic validation error for translation entities across the domain.
 * Supports multiple field validation errors and provides structured access
 * to individual field errors and comprehensive error information.
 *
 * Can be used by any entity that has translations (units, equipments,
 * infrastructures, etc.) to maintain consistency across the domain.
 */
export class TranslationValidationError extends Data.TaggedError(
  'TranslationValidationError',
)<{
  readonly entityType: string;
  readonly message: string;
  readonly fields: Record<string, string>;
}> {
  /**
   * Create a TranslationValidationError from ValidationErrors object
   * @param entityType - The type of entity being validated (e.g., 'Unit', 'Equipment')
   * @param validationErrors - Object containing field names mapped to error messages
   * @param primaryMessage - Optional primary message, defaults to first field error
   */
  static fromValidationErrors(
    entityType: string,
    validationErrors: Record<string, string>,
    primaryMessage?: string,
  ): TranslationValidationError {
    return new TranslationValidationError({
      entityType,
      message: primaryMessage || 'Validation failed',
      fields: validationErrors,
    });
  }

  /**
   * Create a TranslationValidationError for a single field
   * @param entityType - The type of entity being validated
   * @param fieldName - The field that failed validation
   * @param errorMessage - The error message for the field
   */
  static forField(
    entityType: string,
    fieldName: string,
    errorMessage: string,
  ): TranslationValidationError {
    return new TranslationValidationError({
      entityType,
      message: errorMessage,
      fields: { [fieldName]: errorMessage },
    });
  }

  /**
   * Get error message for a specific field
   * @param fieldName - The field name to get the error for
   * @returns The error message for the field, or undefined if no error exists
   */
  getFieldError(fieldName: string): string | undefined {
    return this.fields[fieldName];
  }

  /**
   * Check if a specific field has an error
   * @param fieldName - The field name to check
   * @returns True if the field has an error, false otherwise
   */
  hasFieldError(fieldName: string): boolean {
    return fieldName in this.fields;
  }

  /**
   * Get all field names that have errors
   * @returns Array of field names with errors
   */
  getErrorFields(): string[] {
    return Object.keys(this.fields);
  }

  /**
   * Get all error messages
   * @returns Array of all error messages
   */
  getAllErrorMessages(): string[] {
    return Object.values(this.fields);
  }

  /**
   * Get the number of field errors
   * @returns Number of fields with errors
   */
  getErrorCount(): number {
    return Object.keys(this.fields).length;
  }
}

/**
 * Error thrown when attempting to create a translation collection with duplicate locales.
 * This ensures the business invariant that each locale should appear only once
 * in a translation collection.
 */
export class DuplicateLocaleError extends Data.TaggedError(
  'DuplicateLocaleError',
)<{
  readonly duplicateLocales: readonly string[];
  readonly message: string;
}> {
  /**
   * Create a DuplicateLocaleError with the list of duplicate locales found
   * @param duplicateLocales - Array of locale codes that were duplicated
   */
  static create(duplicateLocales: readonly string[]): DuplicateLocaleError {
    const localeList = duplicateLocales.join(', ');
    return new DuplicateLocaleError({
      duplicateLocales,
      message: `Duplicate locales are not allowed in a translation collection. Found duplicates: ${localeList}`,
    });
  }
}
