import { DocumentationCategoryMigrationConverter } from '../converters/06-01-convert-documentation-category-inserts';

async function convertDocumentationCategoryData() {
  try {
    // Initialize converter
    const converter = new DocumentationCategoryMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertDocumentationCategoryData().catch(console.error);
