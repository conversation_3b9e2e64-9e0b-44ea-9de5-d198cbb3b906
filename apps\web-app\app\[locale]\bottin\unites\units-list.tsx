'use client';

import { unitsColumns } from '@/app/[locale]/bottin/unites/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/units';
import type { SupportedLocale } from '@/types/locale';
import type { UnitListItemDTO } from '@rie/api-contracts';

type UnitsListProps = {
  locale: SupportedLocale;
};

export const UnitsList = ({ locale }: UnitsListProps) => {
  return (
    <DirectoryList<'unit', 'list', UnitListItemDTO, Record<string, unknown>>
      directoryListKey="unit"
      view="list"
      locale={locale}
      columns={unitsColumns()}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="units"
    />
  );
};
