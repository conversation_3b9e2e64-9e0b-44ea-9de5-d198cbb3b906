import { CampusFormRequestDtoSchema } from '@rie/api-contracts';
import {
  CampusInputSchema,
  DbCampusI18NInputSchema,
} from '@rie/domain/schemas';
import type { CampusI18NInput, Writable } from '@rie/domain/types';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const CampusFormToDomainInput = Schema.transformOrFail(
  CampusFormRequestDtoSchema,
  CampusInputSchema,
  {
    strict: true,
    decode: (formDto, _, ast) => {
      return ParseResult.try({
        try: () => {
          const translationsMap = new Map<string, Writable<CampusI18NInput>>();

          const ensureLocale = (locale: string) => {
            if (!translationsMap.has(locale)) {
              translationsMap.set(locale, {
                locale,
                name: null,
              });
            }
          };

          for (const item of formDto.name) {
            ensureLocale(item.locale);
            const itemFound = translationsMap.get(item.locale);
            if (itemFound) {
              itemFound.name = item.value;
            }
          }

          const translations = Array.from(translationsMap.values()).map((t) =>
            Schema.decodeSync(DbCampusI18NInputSchema)(t),
          );

          return {
            isActive: formDto.isActive ?? true, // Default to true for new campuses
            modifiedBy: null,
            sadId: formDto.sadId ?? null,
            institutionId: formDto.jurisdiction.value,
            translations: translations,
          };
        },
        catch: (error) =>
          new ParseResult.Type(ast, formDto, (error as Error).message),
      });
    },
    encode: (domainInput, _, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(
          ast,
          domainInput,
          'Encoding from domain to form is not supported by this transformer',
        ),
      ),
  },
);
