{"content": "Vous pouvez trouver le contenu de la page à propos ici.", "title": {"manufacturiers": "Manufacturiers", "etablissements": "Établissements", "unites": "Unités", "personnes": "<PERSON><PERSON>", "locaux": "Lo<PERSON>ux", "batiments": "Bâtiments", "campus": "Campus", "projetsFinancement": "Projets de financement"}, "vendors": {"table": {"columns": {"name": "Nom", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions"}}}, "Vendors": {"Table": {"Columns": {"Name": "Nom", "DateEnd": "Date de fermeture", "LastUpdatedAt": "Dernière mise à jour", "Actions": "Actions"}}}, "vendor": {"table": {"columns": {"name": "Nom", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions"}}}, "institution": {"table": {"columns": {"name": "Nom", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "acronym": "Acronym", "establishmentType": "Type", "actions": "Actions"}}}, "unit": {"table": {"columns": {"name": "Nom", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "parentName": "Parent", "acronym": "Acronym"}}}, "people": {"table": {"columns": {"name": "Nom", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "email": "<PERSON><PERSON><PERSON>", "lastName": "Nom", "firstName": "Prénom"}}}, "room": {"table": {"columns": {"name": "Numéro de local", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "building": "Batiment", "jurisdiction": "Juridiction"}}}, "buildings": {"table": {"columns": {"name": "Nom du pavillon", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "campus": "Campus", "jurisdiction": "Juridiction"}}}, "campus": {"description": {"title": "Description"}, "table": {"columns": {"name": "Nom du campus", "createdAt": "<PERSON><PERSON><PERSON>", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "jurisdiction": "Juridiction"}}}, "fundingProjects": {"table": {"columns": {"name": "Titre du projet", "dateEnd": "Date de fermeture", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions", "infrastructure": "Infrastructure", "titulaire": "<PERSON><PERSON><PERSON><PERSON>"}}}, "description": {"manufacturiers": "Trouvez des informations sur les manufacturiers dans notre bottin.", "etablissements": "Explorez la liste des établissements de notre réseau.", "unites": "Découvrez les différentes unités au sein de notre organisation.", "personnes": "Recherchez des individus dans notre bottin complet.", "locaux": "Localisez et obtenez des détails sur nos installations locales.", "batiments": "Consultez les informations sur nos bâtiments et structures.", "campus": "Explorez nos emplacements et installations de campus.", "projetsFinancement": "Informez-vous sur nos projets de financement et initiatives en cours."}, "form": {"sections": {"description": {"title": "Description", "contactDetails": {"addAddress": "Ajouter un emplacement", "title": "Coordonnées", "fields": {"addressType": {"label": "Type de contact", "options": {"campus": "Campus", "civicAddress": "Adresse civique"}}, "phone": {"label": "Numéro de téléphone", "error": {"max": "Le numéro de téléphone ne peut pas dépasser 50 caractères"}}, "address": {"title": "Adresses", "civicAddress": {"title": "Adresse civique", "street": {"label": "Rue", "error": {"required": "Street is required"}}, "city": {"label": "Ville"}}, "campus": {"title": "Campus", "listedLocal": {"label": "Local répertorié"}, "listedBuilding": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": {"required": "Le bâtiment est requis"}}}}}, "addContact": "Ajouter un numéro de téléphone"}, "generalInfo": {"title": "Informations générales", "fields": {"building": {"label": "Bâtiment", "error": {"required": "Le bâtiment est requis"}}, "jurisdiction": {"label": "Juridiction", "error": {"required": "La juridiction est requise"}}, "name": {"label": "Nom en {locale}", "error": {"required": "Le nom est requis", "max": "Le nom ne doit pas contenir plus de {max} caractères"}}, "acronym": {"label": "Acronyme en {locale}", "error": {"max": "L'acronyme ne doit pas contenir plus de {max} caractères"}}, "type": {"label": "Type"}, "alias": {"label": "<PERSON>as en {locale}", "tooltip": "Autres ou anciens noms", "error": {"max": "Alias ne doit pas contenir plus de {max} charactères"}}, "pseudonym": {"label": "Pseudonyme", "tooltip": "Autres ou anciens noms ou numéros utilisés pour référer au même local", "error": {"max": "Pseudonyme ne doit pas contenir plus de {max} charactères"}}, "pseudonyme": {"label": "Pseudonyme", "tooltip": "Autres ou anciens noms ou numéros utilisés pour référer au même local", "error": {"max": "Pseudonyme ne doit pas contenir plus de {max} charactères"}}, "description": {"label": "Description", "error": {"max": "Description ne doit pas contenir plus de {max} charactères"}}, "dateEnd": {"label": "Date de fermeture"}, "establishmentType": {"label": "Type d'établissement", "error": {"required": "Le type d'établissement est requis"}}, "unitType": {"label": "Type d'unité", "options": {"Administrative": "Unité administrative", "Recherche": "Unité de recherche"}}, "relatedOrganizations": {"label": "Organisations associées"}, "parentUnit": {"label": "Unité parent", "error": {"required": "L'unité parent est requise"}}, "firstName": {"label": "Prénom", "error": {"required": "Le prénom est requis"}}, "lastName": {"label": "Nom de famille", "error": {"required": "Le nom de famille est requis"}}, "email": {"label": "<PERSON><PERSON><PERSON>", "addEmail": "Ajouter un courriel", "error": {"required": "Au moins un courriel est requis", "invalid": "<PERSON><PERSON><PERSON> est invalide"}}, "phone": {"label": "Numéro de téléphone", "addPhone": "Ajouter un numéro de téléphone", "error": {"required": "Au moins un numéro de téléphone est requis"}}, "localCapacity": {"label": "Capacités du local", "tooltip": "Information liée aux exigences en services bâtiment des équipements. Ces informations permettent la gestion des déménagements.", "options": {"area": "Superficie nette réelle (m²)", "capacity": "Capacité portante du plancher (kgf/m²)"}}}}, "affiliationDetails": {"title": "Détails de l'affiliation", "fields": {"unit": {"label": "Unité", "error": {"required": "L'unité est requise"}}, "affiliationType": {"label": "Type d'affiliation", "options": {"infrastructure": "Infrastructure", "unit": "Unité", "establishment": "Établissement"}}, "infrastructure": {"label": "Infrastructure", "error": {"required": "L'infrastructure est requise"}}, "establishment": {"label": "Établissement", "error": {"required": "L'établissement est requis"}}, "firstName": {"label": "Prénom", "error": {"required": "Le prénom est requis"}}, "lastName": {"label": "Nom de famille", "error": {"required": "Le nom de famille est requis"}}, "email": {"label": "<PERSON><PERSON><PERSON>", "addEmail": "Ajouter un courriel", "error": {"required": "Au moins un courriel est requis"}}, "phone": {"label": "Numéro de téléphone", "addPhone": "Ajouter un numéro de téléphone", "error": {"required": "Au moins un numéro de téléphone est requis"}}}}}, "manufacturer": {"generalInfo": {"fields": {"name": {"label": "Nom en {locale}", "error": {"required": "Le nom est requis", "max": "Le nom ne peut pas contenir plus de {max} caractères"}}}}}, "unit": {"fields": {"name": {"label": "Nom", "error": {"required": "Le nom est requis", "max": "Le nom ne peut pas dépasser 50 caractères"}}}}, "affiliations": {"title": "Affiliations", "affiliatedSection": {"title": "Affiliations", "addAffiliatedField": "Ajouter une organisation affiliée"}, "affiliatedPersons": {"title": "Personnes affiliées", "fields": {"jobTitle": {"label": "Titre du poste", "error": {"required": "Le titre du poste est requis"}}}}}, "person": {"fields": {"firstName": {"label": "Prénom", "error": {"required": "Le prénom est requis"}}, "lastName": {"label": "Nom de famille", "error": {"required": "Le nom de famille est requis"}}, "email": {"label": "<PERSON><PERSON><PERSON>", "addEmail": "Ajouter un courriel", "error": {"required": "Au moins un courriel est requis"}}, "phone": {"label": "Numéro de téléphone", "addPhone": "Ajouter un numéro de téléphone", "error": {"required": "Au moins un numéro de téléphone est requis"}}}}}}}