import type { DbUnit } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import * as R from 'remeda';
import {
  type DuplicateLocaleError,
  TranslationValidationError,
  UnitInvariantViolationError,
} from '../errors';
import { DbUnitDataSchema } from '../schemas';
import type { UnitData } from '../types';
import { UnitTranslation, UnitTranslationCollection } from '../value-objects';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface UnitAggregateState extends Omit<DbUnit, 'createdAt' | 'updatedAt'> {
  createdAt?: string;
  updatedAt?: string;
  translations: UnitTranslationCollection;
}

const UnitAggregateState = Data.case<UnitAggregateState>();

// Unit Aggregate Root
export class Unit {
  private constructor(private readonly data: UnitAggregateState) {}

  /**
   * Factory for creating a NEW Unit aggregate.
   * Use this when creating a unit from user input for the first time.
   */
  static create(props: {
    typeId: string;
    parentId?: string | null;
    guidId?: string | null;
    modifiedBy: string;
    translations: UnitTranslationCollection;
  }): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(
      UnitAggregateState({
        id: DbUtils.cuid2(),
        isActive: true,
        // Timestamps are omitted; they will be set by the database on INSERT.
        typeId: props.typeId,
        parentId: props.parentId ?? null,
        guidId: props.guidId ?? null,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
      }),
    );

    // Ensure the new unit is valid before returning it
    return pipe(
      newUnit.validateInvariants(),
      Effect.map(() => newUnit),
    );
  }

  /**
   * Factory for rehydrating a Unit aggregate from raw database data.
   */
  static fromDatabaseData(
    rawData: UnitData,
  ): Effect.Effect<
    Unit,
    | TranslationValidationError
    | ParseResult.ParseError
    | UnitInvariantViolationError
    | DuplicateLocaleError
  > {
    return pipe(
      Schema.decodeUnknown(DbUnitDataSchema)(rawData),
      Effect.mapError((parseError) =>
        TranslationValidationError.forField(
          'Unit',
          'database_validation',
          `Unit database data validation failed: ${parseError.message}`,
        ),
      ),
      Effect.flatMap((validated) =>
        pipe(
          validated.translations.map((t) => UnitTranslation.create(t)),
          Effect.all,
          Effect.flatMap(UnitTranslationCollection.create),
          Effect.map((translations) => ({ ...validated, translations })),
        ),
      ),
      Effect.map(
        (hydratedData) =>
          new Unit(
            UnitAggregateState({
              ...hydratedData,
              id: rawData.id, // ensure original id is preserved
            }),
          ),
      ),
      Effect.flatMap((unit) =>
        pipe(
          unit.validateInvariants(),
          Effect.map(() => unit),
        ),
      ),
    );
  }

  // --- Business Methods ---

  activate(
    modifiedBy: string,
  ): Effect.Effect<Unit, UnitInvariantViolationError> {
    if (this.data.isActive) {
      return Effect.fail(
        new UnitInvariantViolationError({
          unitId: this.data.id,
          reason: 'Cannot activate a unit that is already active',
        }),
      );
    }
    return Effect.succeed(
      new Unit(
        UnitAggregateState({
          ...this.data,
          isActive: true,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  deactivate(
    modifiedBy: string,
  ): Effect.Effect<Unit, UnitInvariantViolationError> {
    if (!this.data.isActive) {
      return Effect.fail(
        new UnitInvariantViolationError({
          unitId: this.data.id,
          reason: 'Cannot deactivate a unit that is already inactive',
        }),
      );
    }
    return Effect.succeed(
      new Unit(
        UnitAggregateState({
          ...this.data,
          isActive: false,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  /**
   * Updates the unit with new data following the "replace all" pattern.
   * All translations are replaced atomically.
   */
  update(props: {
    typeId: string;
    parentId: string | null;
    guidId?: string | null;
    isActive: boolean;
    modifiedBy: string;
    translations: UnitTranslationCollection;
  }): Effect.Effect<Unit, UnitInvariantViolationError> {
    const newUnit = new Unit(
      UnitAggregateState({
        ...this.data,
        typeId: props.typeId,
        parentId: props.parentId,
        guidId: props.guidId ?? null,
        isActive: props.isActive,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
        // `updatedAt` is no longer set here; the database trigger will handle it.
      }),
    );

    return pipe(
      newUnit.validateInvariants(),
      Effect.map(() => newUnit),
    );
  }

  // --- Query Methods ---

  get id(): string {
    return this.data.id;
  }

  /**
   * Gets a composite view of all translatable fields, with locale fallbacks.
   * Delegates the complex logic to the UnitTranslationCollection.
   */
  getCompositeTranslations(locale: string, fallbackLocale = 'en') {
    return this.data.translations.getCompositeTranslations(
      locale,
      fallbackLocale,
    );
  }

  /**
   * Gets the entire collection of UnitTranslation value objects.
   * Useful for views that need to display all available translations, like an edit form.
   */
  getAllTranslations(): readonly UnitTranslation[] {
    return this.data.translations.getAll();
  }

  isActive(): boolean {
    return this.data.isActive;
  }

  toRaw(): Effect.Effect<UnitData, ParseResult.ParseError> {
    const SourceUnitSchema = Schema.extend(
      DbUnitDataSchema.omit('createdAt', 'updatedAt'),
      Schema.Struct({
        createdAt: Schema.optional(Schema.String),
        updatedAt: Schema.optional(Schema.String),
      }),
    );

    // 3. Create the failable transformer
    const transformer = Schema.transformOrFail(
      SourceUnitSchema,
      DbUnitDataSchema,
      {
        decode: (source, _, ast) => {
          if (source.createdAt && source.updatedAt) {
            // The cast is safe because we have checked the properties exist.
            return ParseResult.succeed(source as UnitData);
          }
          return ParseResult.fail(
            new ParseResult.Type(
              ast,
              source,
              'Cannot serialize for persistence because createdAt or updatedAt is missing',
            ),
          );
        },
        encode: (val, _options, ast) =>
          ParseResult.fail(
            new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
          ),
      },
    );

    const unitData = R.omit(this.data, ['translations']);

    return pipe(
      this.data.translations.toPersistenceArray(),
      Effect.flatMap((persistableTranslations) =>
        Schema.decode(transformer)({
          ...unitData,
          translations: persistableTranslations,
        }),
      ),
    );
  }

  // --- Invariant Validation ---

  private validateInvariants(): Effect.Effect<
    void,
    UnitInvariantViolationError
  > {
    // Check that unit has at least one translation
    if (this.data.translations.isEmpty()) {
      return Effect.fail(
        new UnitInvariantViolationError({
          unitId: this.data.id,
          reason: 'Unit must have at least one translation',
        }),
      );
    }

    // Check that at least one translation has a name
    const hasValidName = this.data.translations.getAll().some((t) => {
      const name = t.getName();
      return name !== null && name !== undefined && name.trim() !== '';
    });

    if (!hasValidName) {
      return Effect.fail(
        new UnitInvariantViolationError({
          unitId: this.data.id,
          reason: 'Unit must have at least one translation with a valid name',
        }),
      );
    }

    // Check for self-parenting
    if (this.data.parentId && this.data.parentId === this.data.id) {
      return Effect.fail(
        new UnitInvariantViolationError({
          unitId: this.data.id,
          reason: 'A unit cannot be its own parent',
        }),
      );
    }

    return Effect.succeed(void 0);
  }
}
