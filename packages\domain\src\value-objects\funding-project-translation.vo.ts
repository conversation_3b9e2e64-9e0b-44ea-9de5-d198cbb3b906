import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type { ParseError } from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

interface FundingProjectTranslationProps {
    locale: string;
    name: string;
    description: string | null;
}

const FundingProjectTranslationData = Data.case<FundingProjectTranslationProps>();

// Schema for validation
const FundingProjectTranslationSchema = Schema.Struct({
    locale: Schema.String,
    name: Schema.String,
    description: Schema.NullishOr(Schema.String),
});

/**
 * Value Object representing a single FundingProject translation.
 * Contains the localized fields for a funding project in a specific locale.
 */
export class FundingProjectTranslation {
    private constructor(private readonly data: FundingProjectTranslationProps) { }

    static create(
        input: unknown,
    ): Effect.Effect<FundingProjectTranslation, ParseError> {
        return pipe(
            Schema.decodeUnknown(FundingProjectTranslationSchema)(input),
            Effect.map(
                (validatedData) =>
                    new FundingProjectTranslation(
                        FundingProjectTranslationData({
                            locale: validatedData.locale,
                            name: validatedData.name,
                            description: validatedData.description ?? null,
                        })
                    ),
            ),
        );
    }

    get locale(): string {
        return this.data.locale;
    }

    get name(): string {
        return this.data.name;
    }

    get description(): string | null {
        return this.data.description;
    }

    /**
     * Converts this value object to its raw data representation.
     * Used for serialization and persistence.
     */
    toRaw(): FundingProjectTranslationProps {
        return { ...this.data };
    }

    /**
     * Creates a new instance from raw data.
     * Used for deserialization from the database.
     */
    static fromRaw(
        data: FundingProjectTranslationProps,
    ): Effect.Effect<FundingProjectTranslation, ParseError> {
        return FundingProjectTranslation.create(data);
    }
}