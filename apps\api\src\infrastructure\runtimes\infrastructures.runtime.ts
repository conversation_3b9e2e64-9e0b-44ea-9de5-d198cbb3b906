import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  InfrastructuresServiceLive,
  RolesServiceLive,
  UserPermissionsServiceLive,
  UsersServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const InfrastructuresServicesLayer = Layer.mergeAll(
  InfrastructuresRepositoryLive.Default,
  InfrastructuresServiceLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  RolesServiceLive.Default,
  UsersServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const InfrastructuresRuntime = ManagedRuntime.make(
  InfrastructuresServicesLayer,
);
