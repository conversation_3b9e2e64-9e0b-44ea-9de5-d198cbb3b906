import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const initialColumnVisibility = {
  name: true,
  principalResearcher: true,
  synchroId: true,
} as const;

export const projectFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const projectFormDefaultValues = (
  locale: SupportedLocale,
): FinancingProjectFormSchema => ({
  associateResearchers: [],
  name: [{ locale, value: '' }],
  principalResearcher: { label: '', value: '' },
  financedInfrastructures: [],
  identificationIds: [],
  description: [{ locale, value: '' }],
  obtainingYear: 0,
  closingDate: new Date(),
  synchroId: '',
  financingProjectType: { label: '', value: '' },
  purchasedEquipment: [],
  identificationFci: '',
});
