# Your First Contribution

This guide walks you through making your first contribution to the RIE project. We'll start with a simple change to help you understand the workflow.

## 🎯 Goal

By the end of this guide, you'll have:

- Made a small code change
- Tested your change locally
- Created a pull request
- Understood the development workflow

## 🚀 Quick Start Contribution

Let's make a simple but meaningful first contribution by improving documentation or fixing a small issue.

### Step 1: Find Something to Work On

#### Option A: Documentation Improvement

Look for typos, unclear explanations, or missing information in:

- README files
- Code comments
- Documentation files

#### Option B: Small Bug Fix

Check for issues labeled:

- `good first issue`
- `documentation`
- `help wanted`

#### Option C: Code Quality Improvement

- Add missing TypeScript types
- Improve error messages
- Add unit tests

### Step 2: Set Up Your Branch

```bash
# Make sure you're on main and up to date
git checkout main
git pull origin main

# Create a new branch for your contribution
git checkout -b feat/your-feature-name
# or
git checkout -b fix/your-bug-fix
# or
git checkout -b docs/your-documentation-improvement
```

### Branch Naming Convention

- `feat/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation changes
- `refactor/` - Code refactoring
- `test/` - Adding tests
- `chore/` - Maintenance tasks

## 📝 Example: Adding a Utility Function

Let's walk through adding a simple utility function as an example.

### Step 1: Identify the Need

Suppose we need a function to format user names consistently across the app.

### Step 2: Choose the Right Location

```bash
# Utility functions go in the utils package
cd packages/utils/src
```

### Step 3: Create the Function

Create `packages/utils/src/format-name.ts`:

```typescript
/**
 * Formats a user's name for display
 * @param firstName - User's first name
 * @param lastName - User's last name
 * @param options - Formatting options
 * @returns Formatted name string
 */
export interface FormatNameOptions {
  includeMiddleInitial?: boolean;
  lastNameFirst?: boolean;
}

export function formatName(
  firstName: string,
  lastName: string,
  options: FormatNameOptions = {}
): string {
  const { lastNameFirst = false } = options;
  
  if (!firstName || !lastName) {
    return firstName || lastName || '';
  }
  
  if (lastNameFirst) {
    return `${lastName}, ${firstName}`;
  }
  
  return `${firstName} ${lastName}`;
}
```

### Step 4: Export the Function

Update `packages/utils/src/index.ts`:

```typescript
// ... existing exports
export { formatName, type FormatNameOptions } from './format-name';
```

### Step 5: Write Tests

Create `packages/utils/src/format-name.test.ts`:

```typescript
import { describe, it, expect } from 'vitest';
import { formatName } from './format-name';

describe('formatName', () => {
  it('should format name in first-last order by default', () => {
    expect(formatName('John', 'Doe')).toBe('John Doe');
  });

  it('should format name in last-first order when specified', () => {
    expect(formatName('John', 'Doe', { lastNameFirst: true })).toBe('Doe, John');
  });

  it('should handle missing first name', () => {
    expect(formatName('', 'Doe')).toBe('Doe');
  });

  it('should handle missing last name', () => {
    expect(formatName('John', '')).toBe('John');
  });

  it('should handle both names missing', () => {
    expect(formatName('', '')).toBe('');
  });
});
```

### Step 6: Test Your Changes

```bash
# Run tests for the utils package
cd packages/utils
pnpm test

# Run all tests to make sure nothing broke
cd ../..
pnpm test

# Check code quality
pnpm lint

# Build to ensure everything compiles
pnpm build:packages
```

## 🧪 Testing Your Changes

### Unit Tests

```bash
# Run tests for specific package
cd packages/utils
pnpm test

# Run tests in watch mode while developing
pnpm test:watch

# Run with coverage
pnpm test:coverage
```

### Integration Testing

```bash
# Test the entire application
pnpm dev

# Verify your changes work in the running app
# Visit http://localhost:3000
```

### Code Quality Checks

```bash
# Lint your code
pnpm lint

# Fix auto-fixable issues
pnpm lint:fix

# Format code
pnpm format

# Type checking
turbo run type-check
```

## 📤 Submitting Your Changes

### Step 1: Commit Your Changes

```bash
# Stage your changes
git add .

# Commit with a descriptive message
git commit -m "feat(utils): add formatName utility function

- Add formatName function to format user names consistently
- Support both first-last and last-first ordering
- Include comprehensive tests
- Export from utils package index"
```

### Commit Message Convention

```bash
type(scope): brief description

- Detailed explanation of what changed
- Why the change was made
- Any breaking changes or important notes
```

Types:

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

### Step 2: Push Your Branch

```bash
# Push your branch to origin
git push origin feat/your-feature-name
```

### Step 3: Create a Pull Request

1. Go to the repository on GitHub
2. Click "Compare & pull request" for your branch
3. Fill out the PR template:

```markdown
## Description
Brief description of what this PR does.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [x] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [x] Unit tests pass
- [x] Integration tests pass
- [x] Manual testing completed

## Checklist
- [x] My code follows the project's style guidelines
- [x] I have performed a self-review of my code
- [x] I have commented my code, particularly in hard-to-understand areas
- [x] I have made corresponding changes to the documentation
- [x] My changes generate no new warnings
- [x] I have added tests that prove my fix is effective or that my feature works
```

## 🔄 Code Review Process

### What to Expect

1. **Automated Checks**: CI will run tests, linting, and type checking
2. **Code Review**: Team members will review your code
3. **Feedback**: You may receive suggestions for improvements
4. **Iteration**: Make requested changes and push updates
5. **Approval**: Once approved, your PR will be merged

### Responding to Feedback

```bash
# Make requested changes
# ... edit files ...

# Commit and push updates
git add .
git commit -m "fix: address code review feedback"
git push origin feat/your-feature-name
```

### Common Review Comments

- **"Add tests"**: Include unit tests for your changes
- **"Update documentation"**: Add or update relevant docs
- **"Follow naming conventions"**: Use consistent naming patterns
- **"Handle edge cases"**: Consider error conditions and edge cases
- **"Type safety"**: Add proper TypeScript types

## 🎉 After Your PR is Merged

### Clean Up

```bash
# Switch back to main
git checkout main

# Pull the latest changes (including your merged PR)
git pull origin main

# Delete your feature branch
git branch -d feat/your-feature-name

# Delete the remote branch (optional)
git push origin --delete feat/your-feature-name
```

### What's Next?

1. **Celebrate!** 🎉 You've made your first contribution!
2. **Look for more issues** to work on
3. **Join team discussions** about features and improvements
4. **Help review other PRs** to learn from others' code

## 🆘 Getting Help

### If You're Stuck

1. **Check the documentation** in this `docs/` folder
2. **Look at existing code** for patterns and examples
3. **Ask questions** in team chat or GitHub discussions
4. **Create a draft PR** early to get feedback

### Common Issues

#### Tests Failing

```bash
# Run tests locally first
pnpm test

# Check specific test output
cd packages/utils
pnpm test -- --reporter=verbose
```

#### Linting Errors

```bash
# Fix auto-fixable issues
pnpm lint:fix

# Check remaining issues
pnpm lint
```

#### Type Errors

```bash
# Check TypeScript errors
turbo run type-check

# Or in specific package
cd packages/utils
pnpm type-check
```

#### Merge Conflicts

```bash
# Update your branch with latest main
git checkout main
git pull origin main
git checkout feat/your-feature-name
git merge main

# Resolve conflicts in your editor
# Then commit the merge
git add .
git commit -m "resolve merge conflicts"
```

## 📚 Learning Resources

### Project-Specific

- [Architecture Overview](../architecture/README.md)
- [API Development Guide](../guides/api-development.md)
- [Frontend Development Guide](../guides/frontend-development.md)

### General Development

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Effect-ts Documentation](https://effect.website/)

---

**Congratulations on making your first contribution!** 🎉

The more you contribute, the more familiar you'll become with the codebase and development patterns. Don't hesitate to ask questions and learn from the team.
