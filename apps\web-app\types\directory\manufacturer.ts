import type { manufacturerFormSections } from '@/constants/directory/manufacturer';
import type {
  AddressPayload,
  EntityLocaleDictionary,
  EntityLocation,
} from '@/types/common';
import type { PhoneType } from '@/types/equipment';

export type Manufacturer = ManufacturerFull;

export type ManufacturerFull = {
  acronym: null | string;
  acronyms: EntityLocaleDictionary;
  createdAt: string;
  dateBegin: null | string;
  dateEnd: null | string;
  emplacements: EntityLocation[] | null;
  id: string;
  lastUpdatedAt: string;
  names: EntityLocaleDictionary;
  organizationId: number;
  parent: Manufacturer | null;
  pseudonymes: EntityLocaleDictionary;
  telephones: RestPhoneType[];
  text: string;
  uid: null | string;
};

export type RestPhoneType = {
  number: string;
  text: string;
  descriptions: EntityLocaleDictionary;
} & Omit<PhoneType, 'fullNumber' | 'personId'>;

export type ManufacturerPostPayload = {
  acronyms?: EntityLocaleDictionary;
  dateEnd?: string;
  descriptions?: EntityLocaleDictionary;
  emplacements?: AddressPayload[] | null;
  names: EntityLocaleDictionary;
  pseudonymes?: EntityLocaleDictionary;
  telephones?: {
    descriptions: EntityLocaleDictionary;
    number: string;
  }[];
};

export type ManufacturerFormSectionKey = keyof typeof manufacturerFormSections;
