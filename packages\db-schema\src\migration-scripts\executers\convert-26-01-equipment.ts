import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { EquipmentMigrationConverter } from '../converters/26-01-convert-equipment-inserts';

async function convertEquipmentData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new EquipmentMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertEquipmentData().catch(console.error);
