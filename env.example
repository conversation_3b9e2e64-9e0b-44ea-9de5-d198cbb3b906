# Server
NODE_ENV=development
PG_DATABASE_URL=postgres://rie:rie123@localhost:5432/riedb
BETTER_AUTH_SECRET=LYx4Ufch9md6R5eWrAefWQVip8ULSoXE
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=2f690ab9393b1287d38feb2dce23234118b4a2b4
MICROSOFT_CLIENT_ID=a93219e8-f9a3-4221-85d6-32b06ee72251
MICROSOFT_CLIENT_SECRET=****************************************
DATABASE_CONNECTION_SECURITY=none

# Client
NEXT_PUBLIC_RIE_API_URL=https://rie-devel.cen.umontreal.ca/api/ # TODO: To be removed, use NEXT_PUBLIC_API_BASE_URL instead
NEXT_PUBLIC_ORIGIN_URL=http://localhost:3000
NEXT_PUBLIC_RIE_AUTH_URL=https://rie-devel.cen.umontreal.ca/api/auth # TODO: To be removed
NEXT_PUBLIC_POSTHOG_KEY=phc_mEE5kxvnVvBdFiXNOUX0Yn3p3WtFmFprU1bCFu1XjXB
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
NEXT_PUBLIC_BASE_PATH=/rie
NEXT_PUBLIC_VERSION=1.0.0
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000/api
RIE_AUTH_CLIENT_ID=testclient # TODO: To be removed
RIE_AUTH_CLIENT_SECRET=testpass # TODO: To be removed

