/**
 * Shared constants and configurations for infrastructures repository
 * Eliminates code duplication across query methods
 */
import { infrastructures } from '@rie/db-schema/schemas';
import { RELATED_ENTITY_TRANSLATION_COLUMNS } from './equipments.constants';

// Standard infrastructure columns selection
export const BASE_INFRASTRUCTURE_COLUMNS = {
  id: true,
  guidId: true,
  isActive: true,
  website: true,
  isFeatured: true,
  createdAt: true,
  updatedAt: true,
} as const;

// All infrastructure columns (currently same as base, but separated for future extensibility)
export const INFRASTRUCTURE_COLUMNS = {
  ...BASE_INFRASTRUCTURE_COLUMNS,
} as const;

// Standard translation columns for infrastructure translations
export const INFRASTRUCTURE_TRANSLATION_COLUMNS = {
  id: true,
  locale: true,
  name: true,
  description: true,
  otherNames: true,
  acronyms: true,
} as const;

// Standard translation columns for related entities
export const INFRASTRUCTURE_RELATED_ENTITY_TRANSLATION_COLUMNS = {
  id: true,
  locale: true,
  name: true,
  description: true,
} as const;

// Person columns for managers
export const INFRASTRUCTURE_PERSON_COLUMNS = {
  id: true,
  firstName: true,
  lastName: true,
} as const;

// Standard relation configuration for type/status/visibility
export const INFRASTRUCTURE_TRANSLATABLE_ENTITY_RELATION = {
  columns: {
    id: true,
  },
  with: {
    translations: {
      columns: INFRASTRUCTURE_RELATED_ENTITY_TRANSLATION_COLUMNS,
    },
  },
} as const;

// Infrastructure translations relation
export const INFRASTRUCTURE_TRANSLATIONS_RELATION = {
  columns: INFRASTRUCTURE_TRANSLATION_COLUMNS,
} as const;

// Person relation for managers
export const INFRASTRUCTURE_PERSON_RELATION = {
  columns: INFRASTRUCTURE_PERSON_COLUMNS,
} as const;

// User columns for modifiedBy relation
export const INFRASTRUCTURE_USER_COLUMNS = {
  id: true,
  firstName: true,
  lastName: true,
} as const;

// ModifiedBy relation
export const INFRASTRUCTURE_MODIFIED_BY_RELATION = {
  columns: INFRASTRUCTURE_USER_COLUMNS,
} as const;

// Manager relations
export const INFRASTRUCTURE_SCIENTIFIC_MANAGER_RELATION = {
  with: {
    person: INFRASTRUCTURE_PERSON_RELATION,
  },
} as const;

export const INFRASTRUCTURE_OPERATIONAL_MANAGER_RELATION = {
  with: {
    person: INFRASTRUCTURE_PERSON_RELATION,
  },
} as const;

export const INFRASTRUCTURE_SST_MANAGER_RELATION = {
  with: {
    person: INFRASTRUCTURE_PERSON_RELATION,
  },
} as const;

// Address relation
export const ADDRESS_RELATION = {
  columns: {
    id: true,
    addressType: true,
    campusAddressId: true,
    civicAddressId: true,
  },
} as const;

// Unit relation (minimal)
export const INFRASTRUCTURE_UNIT_RELATION_MINIMAL = {
  columns: {
    id: true,
  },
  with: {
    translations: {
      columns: INFRASTRUCTURE_RELATED_ENTITY_TRANSLATION_COLUMNS,
    },
  },
} as const;

// Equipment relation (minimal for access tree optimization)
export const EQUIPMENT_RELATION_MINIMAL = {
  columns: {
    id: true,
  },
  with: {
    translations: {
      columns: RELATED_ENTITY_TRANSLATION_COLUMNS,
    },
  },
} as const;

// Base relations used across most queries
export const INFRASTRUCTURE_RELATIONS_MINIMAL = {
  translations: INFRASTRUCTURE_TRANSLATIONS_RELATION,
} as const;

// Full relations for detailed queries
export const INFRASTRUCTURE_RELATIONS_FULL = {
  translations: INFRASTRUCTURE_TRANSLATIONS_RELATION,
  type: INFRASTRUCTURE_TRANSLATABLE_ENTITY_RELATION,
  status: INFRASTRUCTURE_TRANSLATABLE_ENTITY_RELATION,
  visibility: INFRASTRUCTURE_TRANSLATABLE_ENTITY_RELATION,
  unit: INFRASTRUCTURE_UNIT_RELATION_MINIMAL,
  modifiedBy: INFRASTRUCTURE_MODIFIED_BY_RELATION,
  address: ADDRESS_RELATION,
  scientificManagers: INFRASTRUCTURE_SCIENTIFIC_MANAGER_RELATION,
  operationalManagers: INFRASTRUCTURE_OPERATIONAL_MANAGER_RELATION,
  sstManagers: INFRASTRUCTURE_SST_MANAGER_RELATION,
} as const;

// Infrastructure select object for raw SQL queries
export const INFRASTRUCTURE_SELECT_OBJECT = {
  id: infrastructures.id,
  guidId: infrastructures.guidId,
  isActive: infrastructures.isActive,
  typeId: infrastructures.typeId,
  addressId: infrastructures.addressId,
  statusId: infrastructures.statusId,
  unitId: infrastructures.unitId,
  website: infrastructures.website,
  isFeatured: infrastructures.isFeatured,
  visibilityId: infrastructures.visibilityId,
  createdAt: infrastructures.createdAt,
  updatedAt: infrastructures.updatedAt,
  modifiedBy: infrastructures.modifiedBy,
} as const;
