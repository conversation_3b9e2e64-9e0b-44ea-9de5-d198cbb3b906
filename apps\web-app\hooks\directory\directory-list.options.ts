import {
  type DirectoryListResultType,
  type DirectoryResourceResultType,
  getDirectoryById,
  getDirectoryList,
} from '@/services/directory/directory-list.service';
import type { DirectoryListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getDirectoryListOptions = <
  Key extends DirectoryListKey,
  locale extends SupportedLocale,
  View extends CollectionViewParamType['view'],
>({
  directoryListKey,
  locale,
  view,
}: {
  directoryListKey: Key;
  locale: locale;
  view: View;
}) => {
  return queryOptions<DirectoryListResultType<Key, View>>({
    queryFn: () => getDirectoryList({ directoryListKey, view }),
    queryKey: ['directory', directoryListKey, locale],
  });
};

interface GetDirectoryByIdParams<Key extends DirectoryListKey> {
  directoryListKey: Key;
  id: string;
  view: ResourceViewType;
}

export const getDirectoryByIdOptions = <
  Key extends DirectoryListKey,
  View extends ResourceViewType,
>({
  directoryListKey,
  id,
  view,
}: GetDirectoryByIdParams<Key> & { view: View }) => {
  return queryOptions<DirectoryResourceResultType<Key, View>>({
    queryFn: () => getDirectoryById({ directoryListKey, id, view }),
    queryKey: ['directory', directoryListKey, id],
    enabled: !!id,
  });
};
