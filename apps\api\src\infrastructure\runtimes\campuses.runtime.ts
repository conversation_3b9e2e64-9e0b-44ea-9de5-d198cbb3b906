import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  CampusesRepositoryLive,
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  InstitutionsRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  CampusesServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const CampusesServicesLayer = Layer.mergeAll(
  CampusesRepositoryLive.Default,
  CampusesServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  InstitutionsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const CampusesRuntime = ManagedRuntime.make(CampusesServicesLayer);
