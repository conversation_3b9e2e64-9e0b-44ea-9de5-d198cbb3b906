'use client';

import { permissionsColumns } from '@/app/[locale]/admin/permissions/columns';
import { PermissionsTable } from '@/components/permissions-table/permissions-table';
import { initialColumnVisibility } from '@/constants/permissions';
import { useGetAllPermissions } from '@/hooks/admin/permissions/permissions.hook';
import type { SupportedLocale } from '@/types/locale';

type PermissionsListProps = {
  locale: SupportedLocale;
};

export const PermissionsList = ({ locale }: PermissionsListProps) => {
  const { data: permissions, isLoading } = useGetAllPermissions<'list'>({
    view: 'list',
  });

  return (
    <PermissionsTable
      columns={permissionsColumns(locale)}
      data={permissions ?? []}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      resourceName="permissions"
    />
  );
};
