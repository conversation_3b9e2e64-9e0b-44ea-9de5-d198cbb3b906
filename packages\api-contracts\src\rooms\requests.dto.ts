import * as Schema from 'effect/Schema';
import { SelectOptionDtoSchema } from '../common';

/**
 * Describes the entire request body for creating or updating a room.
 * This is the shape of the data the client will send to the API.
 **/

export const RoomFormRequestDtoSchema = Schema.Struct({
    id: Schema.optional(Schema.String),
    isActive: Schema.optional(Schema.Boolean),
    number: Schema.String,
    area: Schema.optional(Schema.NullOr(Schema.Number)),
    floorLoad: Schema.optional(Schema.NullOr(Schema.Number)),
    building: SelectOptionDtoSchema,
});

export type RoomFormRequestDTO = Schema.Schema.Type<
    typeof RoomFormRequestDtoSchema
>;