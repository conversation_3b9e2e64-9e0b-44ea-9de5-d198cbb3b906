import { BuildingFormRequestDtoSchema } from '@rie/api-contracts';
import { BuildingInputSchema } from '@rie/domain/schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const BuildingFormToDomainInput = Schema.transformOrFail(
    BuildingFormRequestDtoSchema,
    BuildingInputSchema,
    {
        strict: true,
        decode: (formDto, _, ast) => {
            return ParseResult.try({
                try: () => {
                    // Transform name array - filter out null values and convert to required format
                    const nameArray = formDto.name
                        .filter((item) => item.value !== null && item.value !== undefined)
                        .map((item) => ({
                            locale: item.locale,
                            value: item.value as string, // safe cast since we filtered nulls
                        }));

                    // Transform alias array - filter out null values and convert to required format
                    const aliasArray = formDto.alias
                        .filter((item) => item.value !== null && item.value !== undefined)
                        .map((item) => ({
                            locale: item.locale,
                            value: item.value as string, // safe cast since we filtered nulls
                        }));

                    return {
                        id: formDto.id,
                        name: nameArray,
                        alias: aliasArray,
                        campus: {
                            label: formDto.campus.label,
                            value: formDto.campus.value,
                        },
                        jurisdiction: {
                            label: formDto.jurisdiction.label,
                            value: formDto.jurisdiction.value,
                        },
                        civicAddresses: [],
                    };
                },
                catch: (error) =>
                    new ParseResult.Type(ast, formDto, (error as Error).message),
            });
        },
        encode: (domainInput, _, ast) =>
            ParseResult.fail(
                new ParseResult.Forbidden(
                    ast,
                    domainInput,
                    'Encoding from domain to form is not supported by this transformer',
                ),
            ),
    },
);