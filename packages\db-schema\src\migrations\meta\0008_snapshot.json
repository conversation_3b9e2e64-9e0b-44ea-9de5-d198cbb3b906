{"id": "61ede652-88e3-4897-bdc5-e9b7d844084b", "prevId": "f1f244db-6eee-4537-819c-a269792d65d7", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permission_group_permissions": {"name": "permission_group_permissions", "schema": "", "columns": {"group_id": {"name": "group_id", "type": "text", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"permission_group_permissions_group_id_permission_groups_id_fk": {"name": "permission_group_permissions_group_id_permission_groups_id_fk", "tableFrom": "permission_group_permissions", "tableTo": "permission_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "permission_group_permissions_permission_id_permissions_id_fk": {"name": "permission_group_permissions_permission_id_permissions_id_fk", "tableFrom": "permission_group_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permission_groups": {"name": "permission_groups", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permission_groups_name_unique": {"name": "permission_groups_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "domain": {"name": "domain", "type": "permission_domain", "typeSchema": "public", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "permission_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_inheritance": {"name": "role_inheritance", "schema": "", "columns": {"child_role_id": {"name": "child_role_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_role_id": {"name": "parent_role_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_inheritance_child_role_id_roles_id_fk": {"name": "role_inheritance_child_role_id_roles_id_fk", "tableFrom": "role_inheritance", "tableTo": "roles", "columnsFrom": ["child_role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_inheritance_parent_role_id_roles_id_fk": {"name": "role_inheritance_parent_role_id_roles_id_fk", "tableFrom": "role_inheritance", "tableTo": "roles", "columnsFrom": ["parent_role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permission_groups": {"name": "role_permission_groups", "schema": "", "columns": {"role_id": {"name": "role_id", "type": "text", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_permission_groups_role_id_roles_id_fk": {"name": "role_permission_groups_role_id_roles_id_fk", "tableFrom": "role_permission_groups", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permission_groups_group_id_permission_groups_id_fk": {"name": "role_permission_groups_group_id_permission_groups_id_fk", "tableFrom": "role_permission_groups", "tableTo": "permission_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"role_id": {"name": "role_id", "type": "text", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permissions_permission_id_permissions_id_fk": {"name": "role_permissions_permission_id_permissions_id_fk", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"roles_name_unique": {"name": "roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_permissions": {"name": "user_permissions", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "text", "primaryKey": false, "notNull": true}, "granted": {"name": "granted", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"user_permissions_user_id_users_id_fk": {"name": "user_permissions_user_id_users_id_fk", "tableFrom": "user_permissions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_permissions_permission_id_permissions_id_fk": {"name": "user_permissions_permission_id_permissions_id_fk", "tableFrom": "user_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "text", "primaryKey": false, "notNull": true}, "resource_type": {"name": "resource_type", "type": "permission_domain", "typeSchema": "public", "primaryKey": false, "notNull": false}, "resource_id": {"name": "resource_id", "type": "text", "primaryKey": false, "notNull": false}, "granted_by": {"name": "granted_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_role_id_roles_id_fk": {"name": "user_roles_role_id_roles_id_fk", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_granted_by_users_id_fk": {"name": "user_roles_granted_by_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "address_type": {"name": "address_type", "type": "address_types", "typeSchema": "public", "primaryKey": false, "notNull": true}, "campus_address_id": {"name": "campus_address_id", "type": "text", "primaryKey": false, "notNull": false}, "civic_address_id": {"name": "civic_address_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"addresses_campus_address_id_campus_addresses_id_fk": {"name": "addresses_campus_address_id_campus_addresses_id_fk", "tableFrom": "addresses", "tableTo": "campus_addresses", "columnsFrom": ["campus_address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "addresses_civic_address_id_civic_addresses_id_fk": {"name": "addresses_civic_address_id_civic_addresses_id_fk", "tableFrom": "addresses", "tableTo": "civic_addresses", "columnsFrom": ["civic_address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campus_addresses": {"name": "campus_addresses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "building_id": {"name": "building_id", "type": "text", "primaryKey": false, "notNull": false}, "room_id": {"name": "room_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"campus_addresses_building_id_buildings_id_fk": {"name": "campus_addresses_building_id_buildings_id_fk", "tableFrom": "campus_addresses", "tableTo": "buildings", "columnsFrom": ["building_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "campus_addresses_room_id_rooms_id_fk": {"name": "campus_addresses_room_id_rooms_id_fk", "tableFrom": "campus_addresses", "tableTo": "rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.civic_addresses": {"name": "civic_addresses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "street1": {"name": "street1", "type": "text", "primaryKey": false, "notNull": true}, "street2": {"name": "street2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": true}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": true}, "place_id": {"name": "place_id", "type": "text", "primaryKey": false, "notNull": true}, "lat": {"name": "lat", "type": "text", "primaryKey": false, "notNull": false}, "lon": {"name": "lon", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.application_sectors": {"name": "application_sectors", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"application_sectors_modified_by_users_id_fk": {"name": "application_sectors_modified_by_users_id_fk", "tableFrom": "application_sectors", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.application_sectors_i18n": {"name": "application_sectors_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"application_sectors_i18n_data_id_application_sectors_id_fk": {"name": "application_sectors_i18n_data_id_application_sectors_id_fk", "tableFrom": "application_sectors_i18n", "tableTo": "application_sectors", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "application_sectors_i18n_locale_locales_code_fk": {"name": "application_sectors_i18n_locale_locales_code_fk", "tableFrom": "application_sectors_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.buildings": {"name": "buildings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "campus_id": {"name": "campus_id", "type": "text", "primaryKey": false, "notNull": false}, "civic_address_id": {"name": "civic_address_id", "type": "text", "primaryKey": false, "notNull": false}, "sad_id": {"name": "sad_id", "type": "text", "primaryKey": false, "notNull": false}, "di_id": {"name": "di_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"buildings_campus_id_campuses_id_fk": {"name": "buildings_campus_id_campuses_id_fk", "tableFrom": "buildings", "tableTo": "campuses", "columnsFrom": ["campus_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "buildings_civic_address_id_civic_addresses_id_fk": {"name": "buildings_civic_address_id_civic_addresses_id_fk", "tableFrom": "buildings", "tableTo": "civic_addresses", "columnsFrom": ["civic_address_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "buildings_modified_by_users_id_fk": {"name": "buildings_modified_by_users_id_fk", "tableFrom": "buildings", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.buildings_i18n": {"name": "buildings_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "other_names": {"name": "other_names", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"buildings_i18n_data_id_buildings_id_fk": {"name": "buildings_i18n_data_id_buildings_id_fk", "tableFrom": "buildings_i18n", "tableTo": "buildings", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "buildings_i18n_locale_locales_code_fk": {"name": "buildings_i18n_locale_locales_code_fk", "tableFrom": "buildings_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campuses": {"name": "campuses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sad_id": {"name": "sad_id", "type": "text", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"campuses_institution_id_institutions_id_fk": {"name": "campuses_institution_id_institutions_id_fk", "tableFrom": "campuses", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "campuses_modified_by_users_id_fk": {"name": "campuses_modified_by_users_id_fk", "tableFrom": "campuses", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campuses_i18n": {"name": "campuses_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"campuses_i18n_data_id_campuses_id_fk": {"name": "campuses_i18n_data_id_campuses_id_fk", "tableFrom": "campuses_i18n", "tableTo": "campuses", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "campuses_i18n_locale_locales_code_fk": {"name": "campuses_i18n_locale_locales_code_fk", "tableFrom": "campuses_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documentation_categories": {"name": "documentation_categories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"documentation_categories_modified_by_users_id_fk": {"name": "documentation_categories_modified_by_users_id_fk", "tableFrom": "documentation_categories", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documentation_categories_i18n": {"name": "documentation_categories_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"documentation_categories_i18n_data_id_documentation_categories_id_fk": {"name": "documentation_categories_i18n_data_id_documentation_categories_id_fk", "tableFrom": "documentation_categories_i18n", "tableTo": "documentation_categories", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "documentation_categories_i18n_locale_locales_code_fk": {"name": "documentation_categories_i18n_locale_locales_code_fk", "tableFrom": "documentation_categories_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_application_sectors": {"name": "equipment_associated_application_sectors", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "application_sector_id": {"name": "application_sector_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_application_sectors_equipment_id_equipments_id_fk": {"name": "equipment_associated_application_sectors_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_application_sectors", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_application_sectors_application_sector_id_application_sectors_id_fk": {"name": "equipment_associated_application_sectors_application_sector_id_application_sectors_id_fk", "tableFrom": "equipment_associated_application_sectors", "tableTo": "application_sectors", "columnsFrom": ["application_sector_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_categories": {"name": "equipment_associated_categories", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_categories_equipment_id_equipments_id_fk": {"name": "equipment_associated_categories_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_categories", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_categories_category_id_equipment_categories_id_fk": {"name": "equipment_associated_categories_category_id_equipment_categories_id_fk", "tableFrom": "equipment_associated_categories", "tableTo": "equipment_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_dimensions": {"name": "equipment_associated_dimensions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "double precision", "primaryKey": false, "notNull": true}, "unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_dimensions_equipment_id_equipments_id_fk": {"name": "equipment_associated_dimensions_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_dimensions", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_dimensions_unit_id_equipment_measurement_units_id_fk": {"name": "equipment_associated_dimensions_unit_id_equipment_measurement_units_id_fk", "tableFrom": "equipment_associated_dimensions", "tableTo": "equipment_measurement_units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_documents": {"name": "equipment_associated_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_associated_documents_equipment_id_equipments_id_fk": {"name": "equipment_associated_documents_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_documents", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_documents_document_id_media_id_fk": {"name": "equipment_associated_documents_document_id_media_id_fk", "tableFrom": "equipment_associated_documents", "tableTo": "media", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_documents_category_id_documentation_categories_id_fk": {"name": "equipment_associated_documents_category_id_documentation_categories_id_fk", "tableFrom": "equipment_associated_documents", "tableTo": "documentation_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_excellence_hubs": {"name": "equipment_associated_excellence_hubs", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "excellence_hub_id": {"name": "excellence_hub_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_excellence_hubs_equipment_id_equipments_id_fk": {"name": "equipment_associated_excellence_hubs_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_excellence_hubs", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_excellence_hubs_excellence_hub_id_excellence_hubs_id_fk": {"name": "equipment_associated_excellence_hubs_excellence_hub_id_excellence_hubs_id_fk", "tableFrom": "equipment_associated_excellence_hubs", "tableTo": "excellence_hubs", "columnsFrom": ["excellence_hub_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_funding_projects": {"name": "equipment_associated_funding_projects", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "funding_project_id": {"name": "funding_project_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_funding_projects_equipment_id_equipments_id_fk": {"name": "equipment_associated_funding_projects_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_funding_projects", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_funding_projects_funding_project_id_funding_projects_id_fk": {"name": "equipment_associated_funding_projects_funding_project_id_funding_projects_id_fk", "tableFrom": "equipment_associated_funding_projects", "tableTo": "funding_projects", "columnsFrom": ["funding_project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_lifecycles": {"name": "equipment_associated_lifecycles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": true}, "time_unit_id": {"name": "time_unit_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_associated_lifecycles_equipment_id_equipments_id_fk": {"name": "equipment_associated_lifecycles_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_lifecycles", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_lifecycles_time_unit_id_time_units_id_fk": {"name": "equipment_associated_lifecycles_time_unit_id_time_units_id_fk", "tableFrom": "equipment_associated_lifecycles", "tableTo": "time_units", "columnsFrom": ["time_unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_lifecycles_modified_by_users_id_fk": {"name": "equipment_associated_lifecycles_modified_by_users_id_fk", "tableFrom": "equipment_associated_lifecycles", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_maintenance_frequency": {"name": "equipment_associated_maintenance_frequency", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "double precision", "primaryKey": false, "notNull": true}, "time_unit_id": {"name": "time_unit_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_maintenance_frequency_equipment_id_equipments_id_fk": {"name": "equipment_associated_maintenance_frequency_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_maintenance_frequency", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_maintenance_frequency_time_unit_id_time_units_id_fk": {"name": "equipment_associated_maintenance_frequency_time_unit_id_time_units_id_fk", "tableFrom": "equipment_associated_maintenance_frequency", "tableTo": "time_units", "columnsFrom": ["time_unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_measurement_precisions": {"name": "equipment_associated_measurement_precisions", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "equipment_precision_id": {"name": "equipment_precision_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_measurement_precisions_equipment_id_equipments_id_fk": {"name": "equipment_associated_measurement_precisions_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_measurement_precisions", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_measurement_precisions_equipment_precision_id_equipment_measurement_precisions_id_fk": {"name": "equipment_associated_measurement_precisions_equipment_precision_id_equipment_measurement_precisions_id_fk", "tableFrom": "equipment_associated_measurement_precisions", "tableTo": "equipment_measurement_precisions", "columnsFrom": ["equipment_precision_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_media": {"name": "equipment_associated_media", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "media_id": {"name": "media_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_media_equipment_id_equipments_id_fk": {"name": "equipment_associated_media_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_media", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_media_media_id_media_id_fk": {"name": "equipment_associated_media_media_id_media_id_fk", "tableFrom": "equipment_associated_media", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_operational_managers": {"name": "equipment_associated_operational_managers", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_operational_managers_equipment_id_equipments_id_fk": {"name": "equipment_associated_operational_managers_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_operational_managers", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_operational_managers_person_id_people_id_fk": {"name": "equipment_associated_operational_managers_person_id_people_id_fk", "tableFrom": "equipment_associated_operational_managers", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_relationships": {"name": "equipment_associated_relationships", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "related_equipment_id": {"name": "related_equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "relationship_type_id": {"name": "relationship_type_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_relationships_equipment_id_equipments_id_fk": {"name": "equipment_associated_relationships_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_relationships", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_relationships_related_equipment_id_equipments_id_fk": {"name": "equipment_associated_relationships_related_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_relationships", "tableTo": "equipments", "columnsFrom": ["related_equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_relationships_relationship_type_id_equipment_relationship_types_id_fk": {"name": "equipment_associated_relationships_relationship_type_id_equipment_relationship_types_id_fk", "tableFrom": "equipment_associated_relationships", "tableTo": "equipment_relationship_types", "columnsFrom": ["relationship_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_repairers": {"name": "equipment_associated_repairers", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_repairers_equipment_id_equipments_id_fk": {"name": "equipment_associated_repairers_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_repairers", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_repairers_vendor_id_vendors_id_fk": {"name": "equipment_associated_repairers_vendor_id_vendors_id_fk", "tableFrom": "equipment_associated_repairers", "tableTo": "vendors", "columnsFrom": ["vendor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_research_fields": {"name": "equipment_associated_research_fields", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "research_field_id": {"name": "research_field_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_research_fields_equipment_id_equipments_id_fk": {"name": "equipment_associated_research_fields_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_research_fields", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_research_fields_research_field_id_research_fields_id_fk": {"name": "equipment_associated_research_fields_research_field_id_research_fields_id_fk", "tableFrom": "equipment_associated_research_fields", "tableTo": "research_fields", "columnsFrom": ["research_field_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_retailers": {"name": "equipment_associated_retailers", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": false}, "retailer_id": {"name": "retailer_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_associated_retailers_equipment_id_vendors_id_fk": {"name": "equipment_associated_retailers_equipment_id_vendors_id_fk", "tableFrom": "equipment_associated_retailers", "tableTo": "vendors", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_retailers_retailer_id_vendors_id_fk": {"name": "equipment_associated_retailers_retailer_id_vendors_id_fk", "tableFrom": "equipment_associated_retailers", "tableTo": "vendors", "columnsFrom": ["retailer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_sst_managers": {"name": "equipment_associated_sst_managers", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_sst_managers_equipment_id_equipments_id_fk": {"name": "equipment_associated_sst_managers_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_sst_managers", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_sst_managers_person_id_people_id_fk": {"name": "equipment_associated_sst_managers_person_id_people_id_fk", "tableFrom": "equipment_associated_sst_managers", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_associated_techniques": {"name": "equipment_associated_techniques", "schema": "", "columns": {"equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "technique_id": {"name": "technique_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_associated_techniques_equipment_id_equipments_id_fk": {"name": "equipment_associated_techniques_equipment_id_equipments_id_fk", "tableFrom": "equipment_associated_techniques", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_associated_techniques_technique_id_techniques_id_fk": {"name": "equipment_associated_techniques_technique_id_techniques_id_fk", "tableFrom": "equipment_associated_techniques", "tableTo": "techniques", "columnsFrom": ["technique_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_categories": {"name": "equipment_categories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_categories_modified_by_users_id_fk": {"name": "equipment_categories_modified_by_users_id_fk", "tableFrom": "equipment_categories", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_categories_i18n": {"name": "equipment_categories_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_equipment_categories_i18n_data_id": {"name": "idx_equipment_categories_i18n_data_id", "columns": [{"expression": "data_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"equipment_categories_i18n_data_id_equipment_categories_id_fk": {"name": "equipment_categories_i18n_data_id_equipment_categories_id_fk", "tableFrom": "equipment_categories_i18n", "tableTo": "equipment_categories", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_categories_i18n_locale_locales_code_fk": {"name": "equipment_categories_i18n_locale_locales_code_fk", "tableFrom": "equipment_categories_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_category_parents": {"name": "equipment_category_parents", "schema": "", "columns": {"child_id": {"name": "child_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"equipment_category_parents_child_id_equipment_categories_id_fk": {"name": "equipment_category_parents_child_id_equipment_categories_id_fk", "tableFrom": "equipment_category_parents", "tableTo": "equipment_categories", "columnsFrom": ["child_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_category_parents_parent_id_equipment_categories_id_fk": {"name": "equipment_category_parents_parent_id_equipment_categories_id_fk", "tableFrom": "equipment_category_parents", "tableTo": "equipment_categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_measurement_precisions": {"name": "equipment_measurement_precisions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "value": {"name": "value", "type": "double precision", "primaryKey": false, "notNull": true}, "measurement_unit_id": {"name": "measurement_unit_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_measurement_precisions_measurement_unit_id_equipment_measurement_units_id_fk": {"name": "equipment_measurement_precisions_measurement_unit_id_equipment_measurement_units_id_fk", "tableFrom": "equipment_measurement_precisions", "tableTo": "equipment_measurement_units", "columnsFrom": ["measurement_unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_measurement_precisions_modified_by_users_id_fk": {"name": "equipment_measurement_precisions_modified_by_users_id_fk", "tableFrom": "equipment_measurement_precisions", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_measurement_units": {"name": "equipment_measurement_units", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_measurement_units_modified_by_users_id_fk": {"name": "equipment_measurement_units_modified_by_users_id_fk", "tableFrom": "equipment_measurement_units", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_measurement_units_i18n": {"name": "equipment_measurement_units_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_measurement_units_i18n_data_id_equipment_measurement_units_id_fk": {"name": "equipment_measurement_units_i18n_data_id_equipment_measurement_units_id_fk", "tableFrom": "equipment_measurement_units_i18n", "tableTo": "equipment_measurement_units", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_measurement_units_i18n_locale_locales_code_fk": {"name": "equipment_measurement_units_i18n_locale_locales_code_fk", "tableFrom": "equipment_measurement_units_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_relationship_types": {"name": "equipment_relationship_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_relationship_types_modified_by_users_id_fk": {"name": "equipment_relationship_types_modified_by_users_id_fk", "tableFrom": "equipment_relationship_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_relationship_types_i18n": {"name": "equipment_relationship_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_relationship_types_i18n_data_id_equipment_relationship_types_id_fk": {"name": "equipment_relationship_types_i18n_data_id_equipment_relationship_types_id_fk", "tableFrom": "equipment_relationship_types_i18n", "tableTo": "equipment_relationship_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_relationship_types_i18n_locale_locales_code_fk": {"name": "equipment_relationship_types_i18n_locale_locales_code_fk", "tableFrom": "equipment_relationship_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_relationships": {"name": "equipment_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_relationships_type_id_equipment_relationship_types_id_fk": {"name": "equipment_relationships_type_id_equipment_relationship_types_id_fk", "tableFrom": "equipment_relationships", "tableTo": "equipment_relationship_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_relationships_modified_by_users_id_fk": {"name": "equipment_relationships_modified_by_users_id_fk", "tableFrom": "equipment_relationships", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_relationships_i18n": {"name": "equipment_relationships_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_relationships_i18n_data_id_equipment_relationships_id_fk": {"name": "equipment_relationships_i18n_data_id_equipment_relationships_id_fk", "tableFrom": "equipment_relationships_i18n", "tableTo": "equipment_relationships", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_relationships_i18n_locale_locales_code_fk": {"name": "equipment_relationships_i18n_locale_locales_code_fk", "tableFrom": "equipment_relationships_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_statuses": {"name": "equipment_statuses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_statuses_modified_by_users_id_fk": {"name": "equipment_statuses_modified_by_users_id_fk", "tableFrom": "equipment_statuses", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_statuses_i18n": {"name": "equipment_statuses_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_equipment_statuses_i18n_data_id": {"name": "idx_equipment_statuses_i18n_data_id", "columns": [{"expression": "data_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"equipment_statuses_i18n_data_id_equipment_statuses_id_fk": {"name": "equipment_statuses_i18n_data_id_equipment_statuses_id_fk", "tableFrom": "equipment_statuses_i18n", "tableTo": "equipment_statuses", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_statuses_i18n_locale_locales_code_fk": {"name": "equipment_statuses_i18n_locale_locales_code_fk", "tableFrom": "equipment_statuses_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_types": {"name": "equipment_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipment_types_modified_by_users_id_fk": {"name": "equipment_types_modified_by_users_id_fk", "tableFrom": "equipment_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_types_i18n": {"name": "equipment_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_equipment_types_i18n_data_id": {"name": "idx_equipment_types_i18n_data_id", "columns": [{"expression": "data_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"equipment_types_i18n_data_id_equipment_types_id_fk": {"name": "equipment_types_i18n_data_id_equipment_types_id_fk", "tableFrom": "equipment_types_i18n", "tableTo": "equipment_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipment_types_i18n_locale_locales_code_fk": {"name": "equipment_types_i18n_locale_locales_code_fk", "tableFrom": "equipment_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipments": {"name": "equipments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "guid_id": {"name": "guid_id", "type": "text", "primaryKey": false, "notNull": false}, "campus_address_id": {"name": "campus_address_id", "type": "text", "primaryKey": false, "notNull": false}, "is_campus_address_confidential": {"name": "is_campus_address_confidential", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": false}, "serial_number": {"name": "serial_number", "type": "text", "primaryKey": false, "notNull": false}, "homologation_number": {"name": "homologation_number", "type": "text", "primaryKey": false, "notNull": false}, "inventory_number": {"name": "inventory_number", "type": "text", "primaryKey": false, "notNull": false}, "doi": {"name": "doi", "type": "text", "primaryKey": false, "notNull": false}, "use_in_clinical_trial": {"name": "use_in_clinical_trial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_hidden": {"name": "is_hidden", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "status_id": {"name": "status_id", "type": "text", "primaryKey": false, "notNull": false}, "working_percentage": {"name": "working_percentage", "type": "double precision", "primaryKey": false, "notNull": false}, "monetary_cost": {"name": "monetary_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "in_kind_cost": {"name": "in_kind_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "manufacture_year": {"name": "manufacture_year", "type": "integer", "primaryKey": false, "notNull": false}, "acquisition_date": {"name": "acquisition_date", "type": "date", "primaryKey": false, "notNull": false}, "installation_date": {"name": "installation_date", "type": "date", "primaryKey": false, "notNull": false}, "decommissioning_date": {"name": "decommissioning_date", "type": "date", "primaryKey": false, "notNull": false}, "scientific_manager_id": {"name": "scientific_manager_id", "type": "text", "primaryKey": false, "notNull": true}, "manufacturer_id": {"name": "manufacturer_id", "type": "text", "primaryKey": false, "notNull": false}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": false}, "infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "institution_id": {"name": "institution_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipments_guid_id_guids_id_fk": {"name": "equipments_guid_id_guids_id_fk", "tableFrom": "equipments", "tableTo": "guids", "columnsFrom": ["guid_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "equipments_campus_address_id_campus_addresses_id_fk": {"name": "equipments_campus_address_id_campus_addresses_id_fk", "tableFrom": "equipments", "tableTo": "campus_addresses", "columnsFrom": ["campus_address_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipments_type_id_equipment_types_id_fk": {"name": "equipments_type_id_equipment_types_id_fk", "tableFrom": "equipments", "tableTo": "equipment_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "equipments_status_id_equipment_statuses_id_fk": {"name": "equipments_status_id_equipment_statuses_id_fk", "tableFrom": "equipments", "tableTo": "equipment_statuses", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "equipments_scientific_manager_id_people_id_fk": {"name": "equipments_scientific_manager_id_people_id_fk", "tableFrom": "equipments", "tableTo": "people", "columnsFrom": ["scientific_manager_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "equipments_manufacturer_id_vendors_id_fk": {"name": "equipments_manufacturer_id_vendors_id_fk", "tableFrom": "equipments", "tableTo": "vendors", "columnsFrom": ["manufacturer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "equipments_supplier_id_vendors_id_fk": {"name": "equipments_supplier_id_vendors_id_fk", "tableFrom": "equipments", "tableTo": "vendors", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "equipments_infrastructure_id_infrastructures_id_fk": {"name": "equipments_infrastructure_id_infrastructures_id_fk", "tableFrom": "equipments", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "equipments_institution_id_institutions_id_fk": {"name": "equipments_institution_id_institutions_id_fk", "tableFrom": "equipments", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "equipments_modified_by_users_id_fk": {"name": "equipments_modified_by_users_id_fk", "tableFrom": "equipments", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipments_i18n": {"name": "equipments_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "specification": {"name": "specification", "type": "text", "primaryKey": false, "notNull": false}, "usage_context": {"name": "usage_context", "type": "text", "primaryKey": false, "notNull": false}, "risk": {"name": "risk", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"equipments_i18n_data_id_equipments_id_fk": {"name": "equipments_i18n_data_id_equipments_id_fk", "tableFrom": "equipments_i18n", "tableTo": "equipments", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "equipments_i18n_locale_locales_code_fk": {"name": "equipments_i18n_locale_locales_code_fk", "tableFrom": "equipments_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.time_units": {"name": "time_units", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"time_units_modified_by_users_id_fk": {"name": "time_units_modified_by_users_id_fk", "tableFrom": "time_units", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.time_units_i18n": {"name": "time_units_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"time_units_i18n_data_id_time_units_id_fk": {"name": "time_units_i18n_data_id_time_units_id_fk", "tableFrom": "time_units_i18n", "tableTo": "time_units", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "time_units_i18n_locale_locales_code_fk": {"name": "time_units_i18n_locale_locales_code_fk", "tableFrom": "time_units_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.excellence_hubs": {"name": "excellence_hubs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"excellence_hubs_modified_by_users_id_fk": {"name": "excellence_hubs_modified_by_users_id_fk", "tableFrom": "excellence_hubs", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.excellence_hubs_i18n": {"name": "excellence_hubs_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"excellence_hubs_i18n_data_id_excellence_hubs_id_fk": {"name": "excellence_hubs_i18n_data_id_excellence_hubs_id_fk", "tableFrom": "excellence_hubs_i18n", "tableTo": "excellence_hubs", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "excellence_hubs_i18n_locale_locales_code_fk": {"name": "excellence_hubs_i18n_locale_locales_code_fk", "tableFrom": "excellence_hubs_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_associated_identifiers": {"name": "funding_project_associated_identifiers", "schema": "", "columns": {"funding_project_id": {"name": "funding_project_id", "type": "text", "primaryKey": false, "notNull": true}, "identifier_type_id": {"name": "identifier_type_id", "type": "text", "primaryKey": false, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_associated_identifiers_funding_project_id_funding_projects_id_fk": {"name": "funding_project_associated_identifiers_funding_project_id_funding_projects_id_fk", "tableFrom": "funding_project_associated_identifiers", "tableTo": "funding_projects", "columnsFrom": ["funding_project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_associated_identifiers_identifier_type_id_funding_project_identifier_types_id_fk": {"name": "funding_project_associated_identifiers_identifier_type_id_funding_project_identifier_types_id_fk", "tableFrom": "funding_project_associated_identifiers", "tableTo": "funding_project_identifier_types", "columnsFrom": ["identifier_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_associated_identifiers_modified_by_users_id_fk": {"name": "funding_project_associated_identifiers_modified_by_users_id_fk", "tableFrom": "funding_project_associated_identifiers", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_associated_people": {"name": "funding_project_associated_people", "schema": "", "columns": {"funding_project_id": {"name": "funding_project_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_associated_people_funding_project_id_funding_projects_id_fk": {"name": "funding_project_associated_people_funding_project_id_funding_projects_id_fk", "tableFrom": "funding_project_associated_people", "tableTo": "funding_projects", "columnsFrom": ["funding_project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_associated_people_person_id_people_id_fk": {"name": "funding_project_associated_people_person_id_people_id_fk", "tableFrom": "funding_project_associated_people", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_associated_people_modified_by_users_id_fk": {"name": "funding_project_associated_people_modified_by_users_id_fk", "tableFrom": "funding_project_associated_people", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_identifier_types": {"name": "funding_project_identifier_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_identifier_types_modified_by_users_id_fk": {"name": "funding_project_identifier_types_modified_by_users_id_fk", "tableFrom": "funding_project_identifier_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_identifier_types_i18n": {"name": "funding_project_identifier_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_identifier_types_i18n_data_id_funding_project_identifier_types_id_fk": {"name": "funding_project_identifier_types_i18n_data_id_funding_project_identifier_types_id_fk", "tableFrom": "funding_project_identifier_types_i18n", "tableTo": "funding_project_identifier_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_identifier_types_i18n_locale_locales_code_fk": {"name": "funding_project_identifier_types_i18n_locale_locales_code_fk", "tableFrom": "funding_project_identifier_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_infrastructures": {"name": "funding_project_infrastructures", "schema": "", "columns": {"funding_project_id": {"name": "funding_project_id", "type": "text", "primaryKey": false, "notNull": true}, "infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_infrastructures_funding_project_id_funding_projects_id_fk": {"name": "funding_project_infrastructures_funding_project_id_funding_projects_id_fk", "tableFrom": "funding_project_infrastructures", "tableTo": "funding_projects", "columnsFrom": ["funding_project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_infrastructures_infrastructure_id_infrastructures_id_fk": {"name": "funding_project_infrastructures_infrastructure_id_infrastructures_id_fk", "tableFrom": "funding_project_infrastructures", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_infrastructures_modified_by_users_id_fk": {"name": "funding_project_infrastructures_modified_by_users_id_fk", "tableFrom": "funding_project_infrastructures", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_types": {"name": "funding_project_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_types_modified_by_users_id_fk": {"name": "funding_project_types_modified_by_users_id_fk", "tableFrom": "funding_project_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_project_types_i18n": {"name": "funding_project_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_project_types_i18n_data_id_funding_project_types_id_fk": {"name": "funding_project_types_i18n_data_id_funding_project_types_id_fk", "tableFrom": "funding_project_types_i18n", "tableTo": "funding_project_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_project_types_i18n_locale_locales_code_fk": {"name": "funding_project_types_i18n_locale_locales_code_fk", "tableFrom": "funding_project_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_projects": {"name": "funding_projects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "holder_id": {"name": "holder_id", "type": "text", "primaryKey": false, "notNull": true}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "fci_id": {"name": "fci_id", "type": "text", "primaryKey": false, "notNull": false}, "synchro_id": {"name": "synchro_id", "type": "text", "primaryKey": false, "notNull": true}, "obtaining_year": {"name": "obtaining_year", "type": "integer", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_projects_holder_id_people_id_fk": {"name": "funding_projects_holder_id_people_id_fk", "tableFrom": "funding_projects", "tableTo": "people", "columnsFrom": ["holder_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "funding_projects_type_id_funding_project_types_id_fk": {"name": "funding_projects_type_id_funding_project_types_id_fk", "tableFrom": "funding_projects", "tableTo": "funding_project_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "funding_projects_modified_by_users_id_fk": {"name": "funding_projects_modified_by_users_id_fk", "tableFrom": "funding_projects", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.funding_projects_i18n": {"name": "funding_projects_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"funding_projects_i18n_data_id_funding_projects_id_fk": {"name": "funding_projects_i18n_data_id_funding_projects_id_fk", "tableFrom": "funding_projects_i18n", "tableTo": "funding_projects", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "funding_projects_i18n_locale_locales_code_fk": {"name": "funding_projects_i18n_locale_locales_code_fk", "tableFrom": "funding_projects_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.guids": {"name": "guids", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"guids_modified_by_users_id_fk": {"name": "guids_modified_by_users_id_fk", "tableFrom": "guids", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"guids_uuid_unique": {"name": "guids_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_associated_innovation_labs": {"name": "infrastructure_associated_innovation_labs", "schema": "", "columns": {"infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "innovation_lab_id": {"name": "innovation_lab_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"infrastructure_associated_innovation_labs_infrastructure_id_infrastructures_id_fk": {"name": "infrastructure_associated_innovation_labs_infrastructure_id_infrastructures_id_fk", "tableFrom": "infrastructure_associated_innovation_labs", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_innovation_labs_innovation_lab_id_innovation_labs_id_fk": {"name": "infrastructure_associated_innovation_labs_innovation_lab_id_innovation_labs_id_fk", "tableFrom": "infrastructure_associated_innovation_labs", "tableTo": "innovation_labs", "columnsFrom": ["innovation_lab_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_associated_operational_managers": {"name": "infrastructure_associated_operational_managers", "schema": "", "columns": {"infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"infrastructure_associated_operational_managers_infrastructure_id_infrastructures_id_fk": {"name": "infrastructure_associated_operational_managers_infrastructure_id_infrastructures_id_fk", "tableFrom": "infrastructure_associated_operational_managers", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_operational_managers_person_id_people_id_fk": {"name": "infrastructure_associated_operational_managers_person_id_people_id_fk", "tableFrom": "infrastructure_associated_operational_managers", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_associated_people": {"name": "infrastructure_associated_people", "schema": "", "columns": {"infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "role_type_id": {"name": "role_type_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"infrastructure_associated_people_infrastructure_id_infrastructures_id_fk": {"name": "infrastructure_associated_people_infrastructure_id_infrastructures_id_fk", "tableFrom": "infrastructure_associated_people", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_people_person_id_people_id_fk": {"name": "infrastructure_associated_people_person_id_people_id_fk", "tableFrom": "infrastructure_associated_people", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_people_role_type_id_infrastructure_role_types_id_fk": {"name": "infrastructure_associated_people_role_type_id_infrastructure_role_types_id_fk", "tableFrom": "infrastructure_associated_people", "tableTo": "infrastructure_role_types", "columnsFrom": ["role_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_associated_scientific_managers": {"name": "infrastructure_associated_scientific_managers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"infrastructure_associated_scientific_managers_infrastructure_id_infrastructures_id_fk": {"name": "infrastructure_associated_scientific_managers_infrastructure_id_infrastructures_id_fk", "tableFrom": "infrastructure_associated_scientific_managers", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_scientific_managers_person_id_people_id_fk": {"name": "infrastructure_associated_scientific_managers_person_id_people_id_fk", "tableFrom": "infrastructure_associated_scientific_managers", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_associated_sst_managers": {"name": "infrastructure_associated_sst_managers", "schema": "", "columns": {"infrastructure_id": {"name": "infrastructure_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"infrastructure_associated_sst_managers_infrastructure_id_infrastructures_id_fk": {"name": "infrastructure_associated_sst_managers_infrastructure_id_infrastructures_id_fk", "tableFrom": "infrastructure_associated_sst_managers", "tableTo": "infrastructures", "columnsFrom": ["infrastructure_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_associated_sst_managers_person_id_people_id_fk": {"name": "infrastructure_associated_sst_managers_person_id_people_id_fk", "tableFrom": "infrastructure_associated_sst_managers", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_role_types": {"name": "infrastructure_role_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructure_role_types_modified_by_users_id_fk": {"name": "infrastructure_role_types_modified_by_users_id_fk", "tableFrom": "infrastructure_role_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_statuses": {"name": "infrastructure_statuses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructure_statuses_modified_by_users_id_fk": {"name": "infrastructure_statuses_modified_by_users_id_fk", "tableFrom": "infrastructure_statuses", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_statuses_i18n": {"name": "infrastructure_statuses_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructure_statuses_i18n_data_id_infrastructure_statuses_id_fk": {"name": "infrastructure_statuses_i18n_data_id_infrastructure_statuses_id_fk", "tableFrom": "infrastructure_statuses_i18n", "tableTo": "infrastructure_statuses", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_statuses_i18n_locale_locales_code_fk": {"name": "infrastructure_statuses_i18n_locale_locales_code_fk", "tableFrom": "infrastructure_statuses_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_types": {"name": "infrastructure_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructure_types_modified_by_users_id_fk": {"name": "infrastructure_types_modified_by_users_id_fk", "tableFrom": "infrastructure_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructure_types_i18n": {"name": "infrastructure_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructure_types_i18n_data_id_infrastructure_types_id_fk": {"name": "infrastructure_types_i18n_data_id_infrastructure_types_id_fk", "tableFrom": "infrastructure_types_i18n", "tableTo": "infrastructure_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructure_types_i18n_locale_locales_code_fk": {"name": "infrastructure_types_i18n_locale_locales_code_fk", "tableFrom": "infrastructure_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructures": {"name": "infrastructures", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "guid_id": {"name": "guid_id", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "text", "primaryKey": false, "notNull": false}, "status_id": {"name": "status_id", "type": "text", "primaryKey": false, "notNull": true}, "unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "visibility_id": {"name": "visibility_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructures_guid_id_guids_id_fk": {"name": "infrastructures_guid_id_guids_id_fk", "tableFrom": "infrastructures", "tableTo": "guids", "columnsFrom": ["guid_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "infrastructures_type_id_infrastructure_types_id_fk": {"name": "infrastructures_type_id_infrastructure_types_id_fk", "tableFrom": "infrastructures", "tableTo": "infrastructure_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "infrastructures_address_id_addresses_id_fk": {"name": "infrastructures_address_id_addresses_id_fk", "tableFrom": "infrastructures", "tableTo": "addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructures_status_id_infrastructure_statuses_id_fk": {"name": "infrastructures_status_id_infrastructure_statuses_id_fk", "tableFrom": "infrastructures", "tableTo": "infrastructure_statuses", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "infrastructures_unit_id_units_id_fk": {"name": "infrastructures_unit_id_units_id_fk", "tableFrom": "infrastructures", "tableTo": "units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "infrastructures_visibility_id_visibilities_id_fk": {"name": "infrastructures_visibility_id_visibilities_id_fk", "tableFrom": "infrastructures", "tableTo": "visibilities", "columnsFrom": ["visibility_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "infrastructures_modified_by_users_id_fk": {"name": "infrastructures_modified_by_users_id_fk", "tableFrom": "infrastructures", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.infrastructures_i18n": {"name": "infrastructures_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "other_names": {"name": "other_names", "type": "text", "primaryKey": false, "notNull": false}, "acronyms": {"name": "acronyms", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"infrastructures_i18n_data_id_infrastructures_id_fk": {"name": "infrastructures_i18n_data_id_infrastructures_id_fk", "tableFrom": "infrastructures_i18n", "tableTo": "infrastructures", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "infrastructures_i18n_locale_locales_code_fk": {"name": "infrastructures_i18n_locale_locales_code_fk", "tableFrom": "infrastructures_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.innovation_labs": {"name": "innovation_labs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"innovation_labs_modified_by_users_id_fk": {"name": "innovation_labs_modified_by_users_id_fk", "tableFrom": "innovation_labs", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.innovation_labs_i18n": {"name": "innovation_labs_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"innovation_labs_i18n_data_id_innovation_labs_id_fk": {"name": "innovation_labs_i18n_data_id_innovation_labs_id_fk", "tableFrom": "innovation_labs_i18n", "tableTo": "innovation_labs", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "innovation_labs_i18n_locale_locales_code_fk": {"name": "innovation_labs_i18n_locale_locales_code_fk", "tableFrom": "innovation_labs_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_associated_people": {"name": "institution_associated_people", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "institution_id": {"name": "institution_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "role_type_id": {"name": "role_type_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"institution_associated_people_institution_id_institutions_id_fk": {"name": "institution_associated_people_institution_id_institutions_id_fk", "tableFrom": "institution_associated_people", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institution_associated_people_person_id_people_id_fk": {"name": "institution_associated_people_person_id_people_id_fk", "tableFrom": "institution_associated_people", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institution_associated_people_role_type_id_institution_role_types_id_fk": {"name": "institution_associated_people_role_type_id_institution_role_types_id_fk", "tableFrom": "institution_associated_people", "tableTo": "institution_role_types", "columnsFrom": ["role_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_associated_units": {"name": "institution_associated_units", "schema": "", "columns": {"institution_id": {"name": "institution_id", "type": "text", "primaryKey": false, "notNull": true}, "unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"institution_associated_units_institution_id_institutions_id_fk": {"name": "institution_associated_units_institution_id_institutions_id_fk", "tableFrom": "institution_associated_units", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institution_associated_units_unit_id_units_id_fk": {"name": "institution_associated_units_unit_id_units_id_fk", "tableFrom": "institution_associated_units", "tableTo": "units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_role_types": {"name": "institution_role_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_role_types_i18n": {"name": "institution_role_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"institution_role_types_i18n_data_id_institution_role_types_id_fk": {"name": "institution_role_types_i18n_data_id_institution_role_types_id_fk", "tableFrom": "institution_role_types_i18n", "tableTo": "institution_role_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institution_role_types_i18n_locale_locales_code_fk": {"name": "institution_role_types_i18n_locale_locales_code_fk", "tableFrom": "institution_role_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_types": {"name": "institution_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_types_i18n": {"name": "institution_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"institution_types_i18n_data_id_institution_types_id_fk": {"name": "institution_types_i18n_data_id_institution_types_id_fk", "tableFrom": "institution_types_i18n", "tableTo": "institution_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institution_types_i18n_locale_locales_code_fk": {"name": "institution_types_i18n_locale_locales_code_fk", "tableFrom": "institution_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institutions": {"name": "institutions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "guid_id": {"name": "guid_id", "type": "text", "primaryKey": false, "notNull": false}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"institutions_guid_id_guids_id_fk": {"name": "institutions_guid_id_guids_id_fk", "tableFrom": "institutions", "tableTo": "guids", "columnsFrom": ["guid_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institutions_type_id_institution_types_id_fk": {"name": "institutions_type_id_institution_types_id_fk", "tableFrom": "institutions", "tableTo": "institution_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institutions_modified_by_users_id_fk": {"name": "institutions_modified_by_users_id_fk", "tableFrom": "institutions", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institutions_i18n": {"name": "institutions_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "acronyms": {"name": "acronyms", "type": "text", "primaryKey": false, "notNull": false}, "other_names": {"name": "other_names", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"institutions_i18n_data_id_institutions_id_fk": {"name": "institutions_i18n_data_id_institutions_id_fk", "tableFrom": "institutions_i18n", "tableTo": "institutions", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "institutions_i18n_locale_locales_code_fk": {"name": "institutions_i18n_locale_locales_code_fk", "tableFrom": "institutions_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locales": {"name": "locales", "schema": "", "columns": {"code": {"name": "code", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locales_i18n": {"name": "locales_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"locales_i18n_data_id_locales_code_fk": {"name": "locales_i18n_data_id_locales_code_fk", "tableFrom": "locales_i18n", "tableTo": "locales", "columnsFrom": ["data_id"], "columnsTo": ["code"], "onDelete": "cascade", "onUpdate": "cascade"}, "locales_i18n_locale_locales_code_fk": {"name": "locales_i18n_locale_locales_code_fk", "tableFrom": "locales_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"media_modified_by_users_id_fk": {"name": "media_modified_by_users_id_fk", "tableFrom": "media", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_i18n": {"name": "media_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"media_i18n_data_id_media_id_fk": {"name": "media_i18n_data_id_media_id_fk", "tableFrom": "media_i18n", "tableTo": "media", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_types": {"name": "media_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_types_i18n": {"name": "media_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"media_types_i18n_data_id_media_types_id_fk": {"name": "media_types_i18n_data_id_media_types_id_fk", "tableFrom": "media_types_i18n", "tableTo": "media_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people": {"name": "people", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "guid_id": {"name": "guid_id", "type": "text", "primaryKey": false, "notNull": false}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"people_guid_id_guids_id_fk": {"name": "people_guid_id_guids_id_fk", "tableFrom": "people", "tableTo": "guids", "columnsFrom": ["guid_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "people_user_id_users_id_fk": {"name": "people_user_id_users_id_fk", "tableFrom": "people", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "people_modified_by_users_id_fk": {"name": "people_modified_by_users_id_fk", "tableFrom": "people", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people_associated_emails": {"name": "people_associated_emails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"people_associated_emails_person_id_people_id_fk": {"name": "people_associated_emails_person_id_people_id_fk", "tableFrom": "people_associated_emails", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"people_associated_emails_address_unique": {"name": "people_associated_emails_address_unique", "nullsNotDistinct": false, "columns": ["address"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people_associated_media": {"name": "people_associated_media", "schema": "", "columns": {"media_id": {"name": "media_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"people_associated_media_media_id_media_id_fk": {"name": "people_associated_media_media_id_media_id_fk", "tableFrom": "people_associated_media", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "people_associated_media_person_id_people_id_fk": {"name": "people_associated_media_person_id_people_id_fk", "tableFrom": "people_associated_media", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people_associated_phones": {"name": "people_associated_phones", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"people_associated_phones_person_id_people_id_fk": {"name": "people_associated_phones_person_id_people_id_fk", "tableFrom": "people_associated_phones", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people_role_types": {"name": "people_role_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"people_role_types_modified_by_users_id_fk": {"name": "people_role_types_modified_by_users_id_fk", "tableFrom": "people_role_types", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.people_role_types_i18n": {"name": "people_role_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"people_role_types_i18n_data_id_people_role_types_id_fk": {"name": "people_role_types_i18n_data_id_people_role_types_id_fk", "tableFrom": "people_role_types_i18n", "tableTo": "people_role_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "people_role_types_i18n_locale_locales_code_fk": {"name": "people_role_types_i18n_locale_locales_code_fk", "tableFrom": "people_role_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.research_fields": {"name": "research_fields", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"research_fields_modified_by_users_id_fk": {"name": "research_fields_modified_by_users_id_fk", "tableFrom": "research_fields", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.research_fields_i18n": {"name": "research_fields_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"research_fields_i18n_data_id_research_fields_id_fk": {"name": "research_fields_i18n_data_id_research_fields_id_fk", "tableFrom": "research_fields_i18n", "tableTo": "research_fields", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "research_fields_i18n_locale_locales_code_fk": {"name": "research_fields_i18n_locale_locales_code_fk", "tableFrom": "research_fields_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.room_associated_categories": {"name": "room_associated_categories", "schema": "", "columns": {"room_id": {"name": "room_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"room_associated_categories_room_id_rooms_id_fk": {"name": "room_associated_categories_room_id_rooms_id_fk", "tableFrom": "room_associated_categories", "tableTo": "rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "room_associated_categories_category_id_room_categories_id_fk": {"name": "room_associated_categories_category_id_room_categories_id_fk", "tableFrom": "room_associated_categories", "tableTo": "room_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.room_categories": {"name": "room_categories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.room_categories_i18n": {"name": "room_categories_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"room_categories_i18n_data_id_room_categories_id_fk": {"name": "room_categories_i18n_data_id_room_categories_id_fk", "tableFrom": "room_categories_i18n", "tableTo": "room_categories", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "room_categories_i18n_locale_locales_code_fk": {"name": "room_categories_i18n_locale_locales_code_fk", "tableFrom": "room_categories_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rooms": {"name": "rooms", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "area": {"name": "area", "type": "double precision", "primaryKey": false, "notNull": false}, "floor_load": {"name": "floor_load", "type": "double precision", "primaryKey": false, "notNull": false}, "building_id": {"name": "building_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"rooms_building_id_buildings_id_fk": {"name": "rooms_building_id_buildings_id_fk", "tableFrom": "rooms", "tableTo": "buildings", "columnsFrom": ["building_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "rooms_modified_by_users_id_fk": {"name": "rooms_modified_by_users_id_fk", "tableFrom": "rooms", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_contracts": {"name": "service_contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "monetary_cost": {"name": "monetary_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "in_kind_cost": {"name": "in_kind_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "visibility_id": {"name": "visibility_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": false}, "is_renewable": {"name": "is_renewable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "has_parts": {"name": "has_parts", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parts_count": {"name": "parts_count", "type": "integer", "primaryKey": false, "notNull": false}, "has_workforce": {"name": "has_workforce", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hour_bank": {"name": "hour_bank", "type": "integer", "primaryKey": false, "notNull": false}, "has_maintenance": {"name": "has_maintenance", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "maintenance_count": {"name": "maintenance_count", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"service_contracts_equipment_id_equipments_id_fk": {"name": "service_contracts_equipment_id_equipments_id_fk", "tableFrom": "service_contracts", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_contracts_vendor_id_vendors_id_fk": {"name": "service_contracts_vendor_id_vendors_id_fk", "tableFrom": "service_contracts", "tableTo": "vendors", "columnsFrom": ["vendor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_contracts_visibility_id_visibilities_id_fk": {"name": "service_contracts_visibility_id_visibilities_id_fk", "tableFrom": "service_contracts", "tableTo": "visibilities", "columnsFrom": ["visibility_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "service_contracts_modified_by_users_id_fk": {"name": "service_contracts_modified_by_users_id_fk", "tableFrom": "service_contracts", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_contracts_document": {"name": "service_contracts_document", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "service_contract_id": {"name": "service_contract_id", "type": "text", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"service_contracts_document_service_contract_id_service_contracts_id_fk": {"name": "service_contracts_document_service_contract_id_service_contracts_id_fk", "tableFrom": "service_contracts_document", "tableTo": "service_contracts", "columnsFrom": ["service_contract_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_contracts_document_document_id_media_id_fk": {"name": "service_contracts_document_document_id_media_id_fk", "tableFrom": "service_contracts_document", "tableTo": "media", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_contracts_i18n": {"name": "service_contracts_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"service_contracts_i18n_data_id_service_contracts_id_fk": {"name": "service_contracts_i18n_data_id_service_contracts_id_fk", "tableFrom": "service_contracts_i18n", "tableTo": "service_contracts", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_contracts_i18n_locale_locales_code_fk": {"name": "service_contracts_i18n_locale_locales_code_fk", "tableFrom": "service_contracts_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_offers": {"name": "service_offers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "is_for_clinical_research": {"name": "is_for_clinical_research", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "highlight_service": {"name": "highlight_service", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"service_offers_address_addresses_id_fk": {"name": "service_offers_address_addresses_id_fk", "tableFrom": "service_offers", "tableTo": "addresses", "columnsFrom": ["address"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_offers_modified_by_users_id_fk": {"name": "service_offers_modified_by_users_id_fk", "tableFrom": "service_offers", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_offers_equipments": {"name": "service_offers_equipments", "schema": "", "columns": {"service_offer_id": {"name": "service_offer_id", "type": "text", "primaryKey": false, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"service_offers_equipments_service_offer_id_service_offers_id_fk": {"name": "service_offers_equipments_service_offer_id_service_offers_id_fk", "tableFrom": "service_offers_equipments", "tableTo": "service_offers", "columnsFrom": ["service_offer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_offers_equipments_equipment_id_equipments_id_fk": {"name": "service_offers_equipments_equipment_id_equipments_id_fk", "tableFrom": "service_offers_equipments", "tableTo": "equipments", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_offers_i18n": {"name": "service_offers_i18n", "schema": "", "columns": {"data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "service_conditions": {"name": "service_conditions", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"service_offers_i18n_data_id_service_offers_id_fk": {"name": "service_offers_i18n_data_id_service_offers_id_fk", "tableFrom": "service_offers_i18n", "tableTo": "service_offers", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "service_offers_i18n_locale_locales_code_fk": {"name": "service_offers_i18n_locale_locales_code_fk", "tableFrom": "service_offers_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.techniques": {"name": "techniques", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"techniques_modified_by_users_id_fk": {"name": "techniques_modified_by_users_id_fk", "tableFrom": "techniques", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.techniques_i18n": {"name": "techniques_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"techniques_i18n_data_id_techniques_id_fk": {"name": "techniques_i18n_data_id_techniques_id_fk", "tableFrom": "techniques_i18n", "tableTo": "techniques", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "techniques_i18n_locale_locales_code_fk": {"name": "techniques_i18n_locale_locales_code_fk", "tableFrom": "techniques_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_associated_buildings": {"name": "unit_associated_buildings", "schema": "", "columns": {"unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": true}, "building_id": {"name": "building_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"unit_associated_buildings_unit_id_units_id_fk": {"name": "unit_associated_buildings_unit_id_units_id_fk", "tableFrom": "unit_associated_buildings", "tableTo": "units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_associated_buildings_building_id_buildings_id_fk": {"name": "unit_associated_buildings_building_id_buildings_id_fk", "tableFrom": "unit_associated_buildings", "tableTo": "buildings", "columnsFrom": ["building_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_associated_jurisdictions": {"name": "unit_associated_jurisdictions", "schema": "", "columns": {"unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": true}, "unit_parent_id": {"name": "unit_parent_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"unit_associated_jurisdictions_unit_id_units_id_fk": {"name": "unit_associated_jurisdictions_unit_id_units_id_fk", "tableFrom": "unit_associated_jurisdictions", "tableTo": "units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_associated_jurisdictions_unit_parent_id_unit_parents_id_fk": {"name": "unit_associated_jurisdictions_unit_parent_id_unit_parents_id_fk", "tableFrom": "unit_associated_jurisdictions", "tableTo": "unit_parents", "columnsFrom": ["unit_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_associated_people": {"name": "unit_associated_people", "schema": "", "columns": {"unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": true}, "person_id": {"name": "person_id", "type": "text", "primaryKey": false, "notNull": true}, "role_type_id": {"name": "role_type_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"unit_associated_people_unit_id_units_id_fk": {"name": "unit_associated_people_unit_id_units_id_fk", "tableFrom": "unit_associated_people", "tableTo": "units", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_associated_people_person_id_people_id_fk": {"name": "unit_associated_people_person_id_people_id_fk", "tableFrom": "unit_associated_people", "tableTo": "people", "columnsFrom": ["person_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_associated_people_role_type_id_people_role_types_id_fk": {"name": "unit_associated_people_role_type_id_people_role_types_id_fk", "tableFrom": "unit_associated_people", "tableTo": "people_role_types", "columnsFrom": ["role_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_parents": {"name": "unit_parents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "jurisdiction_types", "typeSchema": "public", "primaryKey": false, "notNull": true}, "institution_id": {"name": "institution_id", "type": "text", "primaryKey": false, "notNull": false}, "unit_id": {"name": "unit_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"unit_parents_institution_id_institutions_id_fk": {"name": "unit_parents_institution_id_institutions_id_fk", "tableFrom": "unit_parents", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_parents_modified_by_users_id_fk": {"name": "unit_parents_modified_by_users_id_fk", "tableFrom": "unit_parents", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_types": {"name": "unit_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.unit_types_i18n": {"name": "unit_types_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"unit_types_i18n_data_id_unit_types_id_fk": {"name": "unit_types_i18n_data_id_unit_types_id_fk", "tableFrom": "unit_types_i18n", "tableTo": "unit_types", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "unit_types_i18n_locale_locales_code_fk": {"name": "unit_types_i18n_locale_locales_code_fk", "tableFrom": "unit_types_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.units": {"name": "units", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "guid_id": {"name": "guid_id", "type": "text", "primaryKey": false, "notNull": false}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"units_guid_id_guids_id_fk": {"name": "units_guid_id_guids_id_fk", "tableFrom": "units", "tableTo": "guids", "columnsFrom": ["guid_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "units_type_id_unit_types_id_fk": {"name": "units_type_id_unit_types_id_fk", "tableFrom": "units", "tableTo": "unit_types", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "units_parent_id_unit_parents_id_fk": {"name": "units_parent_id_unit_parents_id_fk", "tableFrom": "units", "tableTo": "unit_parents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "units_modified_by_users_id_fk": {"name": "units_modified_by_users_id_fk", "tableFrom": "units", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.units_i18n": {"name": "units_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "other_names": {"name": "other_names", "type": "text", "primaryKey": false, "notNull": false}, "acronyms": {"name": "acronyms", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"units_i18n_data_id_units_id_fk": {"name": "units_i18n_data_id_units_id_fk", "tableFrom": "units_i18n", "tableTo": "units", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "units_i18n_locale_locales_code_fk": {"name": "units_i18n_locale_locales_code_fk", "tableFrom": "units_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vendor_associated_contacts": {"name": "vendor_associated_contacts", "schema": "", "columns": {"vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": true}, "vendor_contact_id": {"name": "vendor_contact_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"vendor_associated_contacts_vendor_id_vendors_id_fk": {"name": "vendor_associated_contacts_vendor_id_vendors_id_fk", "tableFrom": "vendor_associated_contacts", "tableTo": "vendors", "columnsFrom": ["vendor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "vendor_associated_contacts_vendor_contact_id_vendor_contacts_id_fk": {"name": "vendor_associated_contacts_vendor_contact_id_vendor_contacts_id_fk", "tableFrom": "vendor_associated_contacts", "tableTo": "vendor_contacts", "columnsFrom": ["vendor_contact_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vendor_contacts": {"name": "vendor_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vendor_contacts_modified_by_users_id_fk": {"name": "vendor_contacts_modified_by_users_id_fk", "tableFrom": "vendor_contacts", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vendor_contacts_i18n": {"name": "vendor_contacts_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vendor_contacts_i18n_data_id_vendor_contacts_id_fk": {"name": "vendor_contacts_i18n_data_id_vendor_contacts_id_fk", "tableFrom": "vendor_contacts_i18n", "tableTo": "vendor_contacts", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "vendor_contacts_i18n_locale_locales_code_fk": {"name": "vendor_contacts_i18n_locale_locales_code_fk", "tableFrom": "vendor_contacts_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vendors": {"name": "vendors", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vendors_modified_by_users_id_fk": {"name": "vendors_modified_by_users_id_fk", "tableFrom": "vendors", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vendors_i18n": {"name": "vendors_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "other_names": {"name": "other_names", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vendors_i18n_data_id_vendors_id_fk": {"name": "vendors_i18n_data_id_vendors_id_fk", "tableFrom": "vendors_i18n", "tableTo": "vendors", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "vendors_i18n_locale_locales_code_fk": {"name": "vendors_i18n_locale_locales_code_fk", "tableFrom": "vendors_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.visibilities": {"name": "visibilities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "modified_by": {"name": "modified_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"visibilities_modified_by_users_id_fk": {"name": "visibilities_modified_by_users_id_fk", "tableFrom": "visibilities", "tableTo": "users", "columnsFrom": ["modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.visibilities_i18n": {"name": "visibilities_i18n", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "data_id": {"name": "data_id", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"visibilities_i18n_data_id_visibilities_id_fk": {"name": "visibilities_i18n_data_id_visibilities_id_fk", "tableFrom": "visibilities_i18n", "tableTo": "visibilities", "columnsFrom": ["data_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "visibilities_i18n_locale_locales_code_fk": {"name": "visibilities_i18n_locale_locales_code_fk", "tableFrom": "visibilities_i18n", "tableTo": "locales", "columnsFrom": ["locale"], "columnsTo": ["code"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.permission_domain": {"name": "permission_domain", "schema": "public", "values": ["address", "applicationSector", "building", "campus", "equipment", "excellenceHub", "fundingProject", "infrastructure", "innovationLab", "institution", "media", "people", "researchField", "room", "serviceContract", "serviceOffer", "technique", "unit", "user", "userRole", "vendor", "visibility", "documentationCategory", "equipmentCategory", "equipmentStatus", "equipmentType", "fundingProjectType", "fundingProjectIdentifierType", "infrastructureStatus", "infrastructureType", "institutionType", "mediaType", "peopleRoleType", "roomCategory", "unitType"]}, "public.permission_action": {"name": "permission_action", "schema": "public", "values": ["create", "read", "update", "delete"]}, "public.address_types": {"name": "address_types", "schema": "public", "values": ["campus", "civic"]}, "public.jurisdiction_types": {"name": "jurisdiction_types", "schema": "public", "values": ["institution", "unit"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}