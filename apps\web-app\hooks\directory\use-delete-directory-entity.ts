import { useToast } from '@/components/hooks/use-toast';
import { deleteEntity } from '@/services/directory/entities';
import type { DirectoryEntity } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

export const useDeleteDirectoryEntity = (directoryEntity: DirectoryEntity) => {
  const tCommon = useTranslations('common');
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteEntity(directoryEntity, id),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ['directoryEntities', { directoryEntity }],
      });
      toast({
        description: tCommon('notifications.success', {
          action:
            directoryEntity === 'people' || directoryEntity === 'unit'
              ? tCommon('actions.deletedFeminine')
              : tCommon('actions.deleted'),
          resource: tCommon(`resources.${directoryEntity}`),
        }),
        title: tCommon('notifications.successTitle'),
        variant: 'success',
      });
    },
    onError: () => {
      toast({
        description: tCommon('notifications.errors.onDeleteResource', {
          resource: tCommon(`resources.${directoryEntity}`),
        }),
        title: tCommon('notifications.successTitle'),
        variant: 'destructive',
      });
    },
  });
};
