import * as Schema from 'effect/Schema';

const AuthServiceGroupAccessLevelSchema = Schema.Struct({
  id: Schema.Number,
  name: Schema.String,
  isGlobal: Schema.Boolean,
  uid: Schema.NullOr(Schema.String),
  titles: Schema.Array(
    Schema.Struct({
      fr: Schema.NullishOr(Schema.String),
      en: Schema.NullishOr(Schema.String),
    }),
  ),
  title: Schema.String,
  text: Schema.String,
});

const UserGroupAccessLevelSchema = AuthServiceGroupAccessLevelSchema.omit(
  'uid',
  'titles',
  'title',
  'text',
);

const AuthServiceGroupSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  uid: Schema.NullOr(Schema.String),
  accessLevel: AuthServiceGroupAccessLevelSchema,
  title: Schema.String,
  titles: Schema.Array(
    Schema.Struct({
      fr: Schema.NullishOr(Schema.String),
      en: Schema.NullishOr(Schema.String),
    }),
  ),
  text: Schema.String,
  isGlobal: Schema.Boolean,
  functionalitiesPids: Schema.Array(Schema.Int),
});

const UserGroupSchema = Schema.extend(
  AuthServiceGroupSchema.omit('accessLevel', 'uid', 'title', 'titles', 'text'),
  Schema.Struct({
    accessLevel: UserGroupAccessLevelSchema,
  }),
).pipe(Schema.rename({ functionalitiesPids: 'permissions' }));

export const AuthServiceResponseSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String,
  username: Schema.String,
  email: Schema.String,
  fullname: Schema.String,
  enabled: Schema.Boolean,
  person: Schema.NullOr(Schema.String),
  groups: Schema.Array(AuthServiceGroupSchema),
  role_names: Schema.Array(Schema.String),
  equipement_list: Schema.Array(Schema.Int),
  infrastructure_list: Schema.Array(Schema.Int),
  created_at: Schema.String,
  last_updated_at: Schema.String,
});

export const UserSchema = AuthServiceResponseSchema.omit(
  'fullname',
  'groups',
).pipe(
  Schema.extend(
    Schema.Struct({
      groups: Schema.Array(UserGroupSchema),
    }),
  ),
  Schema.rename({
    text: 'fullName',
    role_names: 'roles',
    equipement_list: 'equipmentList',
    infrastructure_list: 'infrastructureList',
    created_at: 'createdAt',
    last_updated_at: 'lastUpdatedAt',
  }),
);

export type User = Schema.Schema.Type<typeof UserSchema>;
