/**
 * Comprehensive unit tests for BaseConverter critical parsing methods
 *
 * These tests cover the core parsing functionality that is essential to the
 * MySQL-to-PostgreSQL migration system. Failures in these methods cause
 * the entire migration to fail.
 */

import { beforeEach, describe, expect, test, vi } from 'vitest';
import {
  CATEGORIE_EQUIPEMENT_CREATE_TABLE,
  CATE<PERSON><PERSON>IE_EQUIPEMENT_INSERT_STATEMENT,
  CSV_TEST_CASES,
  EXPECTED_PARSED_VALUES,
  FULL_SQL_SAMPLE,
  MALFORMED_INSERT_NO_VALUES,
  SINGLE_ROW_INSERT,
  SPLIT_ROWS_TEST_CASES,
} from './test-data';
import { TestableBaseConverter } from './testable-base-converter';

describe('BaseConverter Critical Parsing Methods', () => {
  let converter: TestableBaseConverter;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  let consoleSpy: any;

  beforeEach(() => {
    converter = new TestableBaseConverter();
    // Spy on console methods to capture warnings
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('1. parseCSVValues - Core CSV parsing with quote/escape handling', () => {
    test('should parse simple CSV values', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.simple);
      expect(result).toEqual(['1', "'test'", 'NULL', '123']);
    });

    test('should handle values with commas inside quotes', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.withCommas);
      expect(result).toEqual(['1', "'text, with comma'", 'NULL', '123']);
    });

    test('should handle escaped single quotes', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.withQuotes);
      expect(result).toEqual(['1', "'text with ''quotes'''", 'NULL', '123']);
    });

    test('should handle double quotes', () => {
      const result = converter.testParseCSVValues(
        CSV_TEST_CASES.withDoubleQuotes,
      );
      expect(result).toEqual(['1', '"text with ""quotes"""', 'NULL', '123']);
    });

    test('should handle mixed quote types', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.mixedQuotes);
      expect(result).toEqual([
        '1',
        "'single quotes'",
        '"double quotes"',
        'NULL',
      ]);
    });

    test('should handle backslash escaped quotes', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.escaped);
      expect(result).toEqual([
        '1',
        "'text with ''escaped'' quotes'",
        'NULL',
        '123',
      ]);
    });

    test('should handle French characters with quotes', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.french);
      expect(result).toEqual([
        '392',
        '196',
        "'fr'",
        `'dégazeur sous vide'`,
        "'Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.'",
        'NULL',
        'NULL',
      ]);
    });

    test('should handle complex mixed quotes and commas', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.complex);
      expect(result).toEqual([
        '1',
        "'Complex, \"mixed\" ''quotes'' text'",
        'NULL',
        '123',
        "'Final field'",
      ]);
    });

    test('should handle empty string', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.empty);
      expect(result).toEqual([]);
    });

    test('should handle NULL only', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.nullOnly);
      expect(result).toEqual(['NULL']);
    });

    test('should handle multiple NULLs', () => {
      const result = converter.testParseCSVValues(CSV_TEST_CASES.multipleNulls);
      expect(result).toEqual(['NULL', 'NULL', 'NULL']);
    });

    test('should handle real batiment data', () => {
      const realData = "1,'504A',NULL,1,1,NULL,0,0";
      const result = converter.testParseCSVValues(realData);
      expect(result).toEqual([
        '1',
        "'504A'",
        'NULL',
        '1',
        '1',
        'NULL',
        '0',
        '0',
      ]);
    });

    test('should handle real batiment data with complex values', () => {
      const realData = "67,'689A; 689B; 690A',NULL,1292,5,NULL,0,0";
      const result = converter.testParseCSVValues(realData);
      expect(result).toEqual([
        '67',
        "'689A; 689B; 690A'",
        'NULL',
        '1292',
        '5',
        'NULL',
        '0',
        '0',
      ]);
    });

    test('should handle extremely long French text with multiple escaped quotes', () => {
      const longFrenchText = `392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL`;

      const result = converter.testParseCSVValues(longFrenchText);

      expect(result).toHaveLength(7); // Should return exactly 7 values
      expect(result[0]).toBe('392');
      expect(result[1]).toBe('196');
      expect(result[2]).toBe("'fr'");
      expect(result[3]).toBe("'dégazeur sous vide'");
      expect(result[4]).toContain("'Système de dégazement"); // Long description starts
      expect(result[4]).toContain("contamination.'"); // Long description ends
      expect(result[5]).toBe('NULL');
      expect(result[6]).toBe('NULL');
    });
  });

  describe('2. splitValueRows - Multi-row INSERT statement splitting', () => {
    test('should split single row', () => {
      const result = converter.testSplitValueRows(
        SPLIT_ROWS_TEST_CASES.singleRow,
      );
      expect(result).toEqual(["1,'test',NULL"]);
    });

    test('should split multiple rows', () => {
      const result = converter.testSplitValueRows(
        SPLIT_ROWS_TEST_CASES.multipleRows,
      );
      expect(result).toEqual([
        "378,189,'fr','collecteur de fractions','Le collecteur de fractions est un appareil conçu pour collecter des échantillons courants ou particuliers de l''effluent de colonne, et les stocker pour qu''ils soient récupérables. Les récipients de stockage sont généralement des éprouvettes ou flacons d''échantillon orientés dans un disque rotatif ou sur une courroie mobile, et dont le mouvement est contrôlé par microprocesseur. Sur signal du microprocesseur, la prochaine éprouvette est déposée sous la vanne, et l''effluent est échantillonné jusqu''à ce que l''ordinateur émette un nouveau signal. Une fois les propriétés du chromatogramme de la séparation définies, un programme de collection peut être rédigé. On peut collecter les fractions à l''aide d''une minuterie, soit à des intervalles ou à des heures précises afin d''échantillonner aux moments d''activité de pointe voulus. On peut aussi collecter les fractions en surveillant les lectures du détecteur: aux moments de dégagement maximal d''effluent, le collecteur de fractions sera déclenché, et cette concentration sera collectée dans un flacon précis. Quand l''effluent retourne au taux de référence, on dispose de la colonne jusqu''à ce que l''effluent atteigne à nouveau un taux de pointe. Les collecteurs de fraction sont couramment utilisés avec les systèmes de chromatographie en phase liquide.',NULL,NULL",
        "379,190,'en','UV detector','A part of a liquid chromatography instrument that detects absorption of UV light by samples. The output is recorded as a series of peaks - each one representing a compound in the mixture passing through the detector and absorbing UV light.',NULL,NULL",
        "380,190,'fr','détecteur UV','Partie du système de chromatographie à phase liquide qui détecte l''absorption de lumière UV par les échantillons. Les sorties sont enregistrées comme une série de pics - chacun représente un composé du mélange qui traverse le détecteur et absorbe de la lumière UV.',NULL,NULL",
        "381,191,'en','single wavelength UV detector',NULL,NULL,NULL",
        "382,191,'fr','transilluminateur pour systèmes de documentation sur gel/à UV/de longueur d''onde unique',NULL,NULL,NULL",
        "383,192,'en','photodiode array detector',NULL,NULL,NULL",
        "384,192,'fr','détecteur à matrice de photodiodes',NULL,NULL,NULL",
        "385,193,'en','pump system',NULL,NULL,NULL",
        "386,193,'fr','pompe',NULL,NULL,NULL",
        "387,194,'en','quaternary pump system','A pump system that pump and mix up to four different solvents in parallel.',NULL,NULL",
        "388,194,'fr','système de pompage quaternaire','Système de pompage qui mélange simultanément jusqu''à 4 solvants.',NULL,NULL",
        "389,195,'en','nano pump system','A pump system optimized for nano flow chromatography.',NULL,NULL",
        "390,195,'fr','système UHPLC à nanopompe','Système de pompage UHPLC avec nanopompes.',NULL,NULL",
        "391,196,'en','vacuum degasser','A degassing system used for degassing solvents in liquid chromatography. Dissolved gasses, usually nitrogen and oxygen from the air, tend to be evolved in the mobile phase as the pressure is reduced when the mobile phase leaves the liquid chromatography column and enters the detector. Gasses in the mobile phase in the detector can produce completely unacceptable noise and, thus, must be removed. The dissolved gasses were originally removed under vacuum but, unfortunately, are soon replaced if the solvent is left in contact with air at atmospheric pressure. For this reason degassing is now usually carried out by bubbling helium through the mobile phase reservoirs. Secondly, vacuum is used in the thermionic detector. This consists of a device, very similar in design to the thermionic valve which is attached to a vacuum and a small quantity of the eluent from a gas chromatography column allowed to bleed through it. Helium is used as the carrier gas. The presence of solute vapor causes the thermionic current to fall. This type of detector tends to become contaminated rather readily.',NULL,NULL",
        "392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL",
        "393,197,'en','flash pump system','Any pump system used in flash column chromatography to push the solvent through the column. Better flow rates can be achieved by using a pump or by using compressed gas (e.g. air, nitrogen, or argon) to push the solvent through the column (flash column chromatography).',NULL,NULL",
        "394,197,'fr','pompe pour chromatographie rapide sur colonne','Dispositif de pompage utilisé dans les systèmes pour chromatographie rapide sur colonne pour rapidement propulser le solvant dans celle-ci. L''utilisation d''une pompe ou d''un gaz comprimé (air, nitrogène, argon) pour l''injection du solvant dans la colonne accroît le débit d''écoulement (chromatographie flash).',NULL,NULL",
        "395,198,'en','isocratic pump system','A pump system optimized for isocratic chromatography.',NULL,NULL",
        "396,198,'fr','pompe isocratique pour chromatographie UHPLC','Système de pompage UHPLC avec pompes isocratiques.',NULL,NULL",
        "397,199,'en','gradient pump system','A pump system optimized for gradient chromatography.',NULL,NULL",
        "398,199,'fr','pompe pour chromatographie UHPLC à gradients','Système de pompage optimisé pour la chromatographie UHPLC à gradients.',NULL,NULL",
        "399,200,'en','capillary pump system','A pump system optimized for capillary chromatography.',NULL,NULL",
        "400,200,'fr','pompe pour chromatographie sur colonne capillaire','Pompe conçue pour la chromatographie sur colonne capillaire',NULL,NULL",
        "401,201,'en','liquid chromatography coupled mass spectrometer',NULL,NULL,NULL",
        "402,201,'fr','spectromètre de masse à chromatographie en phase liquide',NULL,NULL,NULL",
        "403,202,'en','flash chromatograph',NULL,NULL,NULL",
        "404,202,'fr','chromatographe flash',NULL,NULL,NULL",
        "405,203,'en','thermal analysis',NULL,NULL,NULL",
        "406,203,'fr','analyse thermique',NULL,NULL,NULL",
        "407,204,'en','calorimeter',NULL,NULL,NULL",
        "408,204,'fr','calorimètre',NULL,NULL,NULL",
        "409,205,'en','isothermal titration calorimeter','An instrument used to determine the thermodynamic parameters of interactions in solution. It is most often used to study the binding of small molecules (such as medicinal compounds) to larger macromolecules (proteins, DNA etc.).',NULL,NULL",
      ]);
    });

    test('should handle rows with commas in values', () => {
      const result = converter.testSplitValueRows(
        SPLIT_ROWS_TEST_CASES.withCommas,
      );
      expect(result).toEqual([
        "1,'text, with comma',NULL",
        "2,'another, text',NULL",
      ]);
    });

    test('should handle rows with quotes in values', () => {
      const result = converter.testSplitValueRows(
        SPLIT_ROWS_TEST_CASES.withQuotes,
      );
      expect(result).toEqual([
        "1,'text with ''quotes''',NULL",
        "2,'more ''quotes''',NULL",
      ]);
    });

    test('should handle nested parentheses', () => {
      const result = converter.testSplitValueRows(SPLIT_ROWS_TEST_CASES.nested);
      expect(result).toEqual([
        "1,'(nested parens)',NULL",
        "2,'more (nested)',NULL",
      ]);
    });

    test('should handle complex mixed content', () => {
      const result = converter.testSplitValueRows(
        SPLIT_ROWS_TEST_CASES.complex,
      );
      expect(result).toEqual([
        "1,'Complex, \"mixed\" ''quotes'' text',NULL",
        "2,'Another complex (text)',123",
      ]);
    });

    test('should handle real batiment multi-row data', () => {
      const realData =
        "(1,'504A',NULL,1,1,NULL,0,0),(2,'511A',NULL,1,1,NULL,0,0),(3,'511C',NULL,1,1,NULL,0,0)";
      const result = converter.testSplitValueRows(realData);
      expect(result).toEqual([
        "1,'504A',NULL,1,1,NULL,0,0",
        "2,'511A',NULL,1,1,NULL,0,0",
        "3,'511C',NULL,1,1,NULL,0,0",
      ]);
    });

    test('should handle empty input', () => {
      const result = converter.testSplitValueRows('');
      expect(result).toEqual([]);
    });

    test('should filter out empty rows', () => {
      const result = converter.testSplitValueRows('(1,2,3),,(4,5,6)');
      expect(result).toEqual(['1,2,3', '4,5,6']);
    });
  });

  describe('3. extractValuesFromInsertStatement - Single INSERT statement value extraction', () => {
    test('should extract values from single row INSERT', () => {
      const result = converter.testExtractValuesFromInsertStatement(
        SINGLE_ROW_INSERT,
        FULL_SQL_SAMPLE,
        'batiment',
      );

      expect(result).toEqual(EXPECTED_PARSED_VALUES.batiment_first_row);
    });

    test('should handle NULL values correctly', () => {
      const insertStatement =
        "INSERT INTO `batiment` VALUES (1,'504A',NULL,1,1,NULL,0,0);";
      const result = converter.testExtractValuesFromInsertStatement(
        insertStatement,
        FULL_SQL_SAMPLE,
        'batiment',
      );

      expect(result.pseudonyme).toBeNull();
      expect(result.slug).toBeNull();
      expect(result.uid).toBe('504A');
    });

    test('should warn when not enough values provided', () => {
      const insertStatement = "INSERT INTO `batiment` VALUES (1,'504A');"; // Only 2 values for 8 columns

      converter.testExtractValuesFromInsertStatement(
        insertStatement,
        FULL_SQL_SAMPLE,
        'batiment',
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Warning: Not enough values (2) for columns (8)',
      );
    });

    test('should handle quoted values with special characters', () => {
      const insertStatement =
        "INSERT INTO `batiment_trad` VALUES (7,7,'fr','Centre d''éducation physique et des sports (CEPSUM)');";
      const result = converter.testExtractValuesFromInsertStatement(
        insertStatement,
        FULL_SQL_SAMPLE,
        'batiment_trad',
      );

      expect(result.nom).toBe(
        "Centre d'éducation physique et des sports (CEPSUM)",
      );
    });

    test('should throw error for malformed INSERT statement', () => {
      const sqlContent = `
        CREATE TABLE \`test_table\` (
          \`id\` int(11) NOT NULL,
          \`name\` varchar(255) DEFAULT NULL
        );
      `;

      expect(() => {
        converter.testExtractValuesFromInsertStatement(
          MALFORMED_INSERT_NO_VALUES,
          sqlContent,
          'test_table',
        );
      }).toThrow('Could not extract values from INSERT statement');
    });

    test('should handle real equipment category data', () => {
      const insertStatement =
        "INSERT INTO `categorie_equipement` VALUES (1,'1438','analysis-equipment',0,1);";
      const sqlContent = `${CATEGORIE_EQUIPEMENT_CREATE_TABLE}\n${CATEGORIE_EQUIPEMENT_INSERT_STATEMENT}`;

      const result = converter.testExtractValuesFromInsertStatement(
        insertStatement,
        sqlContent,
        'categorie_equipement',
      );

      expect(result).toEqual({
        id: '1',
        uid: '1438',
        slug: 'analysis-equipment',
        removed: '0',
        validated: '1',
      });
    });
  });

  describe('4. extractInsertStatements - INSERT statement discovery', () => {
    test('should extract and split multi-row INSERT statement with semicolons in quoted strings', () => {
      const sqlContent = `
          INSERT INTO \`categorie_equipement_trad\` VALUES (1,1,'en','analysis equipment','Equipment used to measure, characterize or analyze substances, materials, devices, systems or entities.',NULL,NULL),(2,1,'fr','équipement d''analyse','Équipement servant à mesurer, caractériser ou analyser des substances, matériaux, dispositifs, systèmes ou entités.',NULL,NULL),(3,2,'en','biomedical analysis equipment','Equipment for the measurement, analysis or characterization of the structural, physiological or activity-response properties of living entities.',NULL,NULL),(392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL);
      `;

      const result = converter.testExtractInsertStatements(
        sqlContent,
        'categorie_equipement_trad',
      );
      // Should split the multi-row INSERT into 4 individual INSERT statements
      expect(result).toHaveLength(4);

      // Verify the long French text with semicolons is preserved intact in the 4th statement
      expect(result[3]).toContain(
        "À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés",
      );
      expect(result[3]).toContain(
        'Ce type de détecteur est facilement sujet à contamination.',
      );

      // Old expectation (wrong):
      expect(result).not.toEqual([
        `INSERT INTO \`categorie_equipement_trad\` VALUES (1,1,'en','analysis equipment','Equipment used to measure, characterize or analyze substances, materials, devices, systems or entities.',NULL,NULL),(2,1,'fr','équipement d''analyse','Équipement servant à mesurer, caractériser ou analyser des substances, matériaux, dispositifs, systèmes ou entités.',NULL,NULL),(3,2,'en','biomedical analysis equipment','Equipment for the measurement, analysis or characterization of the structural, physiological or activity-response properties of living entities.',NULL,NULL),(392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL);`,
      ]);
    });

    test('should handle real batiment_trad data with French characters', () => {
      const result = converter.testExtractInsertStatements(
        `INSERT INTO \`categorie_equipement\` VALUES (1,'1438','analysis-equipment',0,1),(2,'1503','biomedical-analysis-equipment',0,1);
         INSERT INTO \`categorie_equipement\` VALUES (3,'72','electrophysiology-equipment',0,1),(4,'174','electrophysiology-data-acquisition-system',0,1),(5,'206','electromyography-stimulator',0,1),(6,'242','electromyography-device',0,1),(7,'451','skin-electrode',0,1),(8,'1440','voltage-clamp-device',0,1),(9,'396','patch-clamp-device',0,1),(10,'251','oocyte-clamp',0,1),(11,'557','multichannel-acquisition-system',0,1),(12,'440','digital-sleep-recorder',0,1);
         INSERT INTO \`categorie_equipement\` VALUES (13,'395','gel-imaging-system',0,1),(14,'544','comet-assay-analysis-system',0,1),(15,'405','immunoblot-scanner',0,1),(16,'957','immunostainer',0,1),(17,'536','western-processing-system',0,1),(18,'105','variable-mode-imager',0,1),(19,'101','scanning-camera',0,1),(20,'622','gel-dryer',0,1),(21,'1453','thermal-cycler',0,1),(22,'934','sequencing-equipment',0,1);`,
        'categorie_equipement',
      );
      expect(result.length).toBe(3);
      expect(result).toEqual([
        "INSERT INTO `categorie_equipement` VALUES (1,'1438','analysis-equipment',0,1),(2,'1503','biomedical-analysis-equipment',0,1);",
        "INSERT INTO `categorie_equipement` VALUES (3,'72','electrophysiology-equipment',0,1),(4,'174','electrophysiology-data-acquisition-system',0,1),(5,'206','electromyography-stimulator',0,1),(6,'242','electromyography-device',0,1),(7,'451','skin-electrode',0,1),(8,'1440','voltage-clamp-device',0,1),(9,'396','patch-clamp-device',0,1),(10,'251','oocyte-clamp',0,1),(11,'557','multichannel-acquisition-system',0,1),(12,'440','digital-sleep-recorder',0,1);",
        "INSERT INTO `categorie_equipement` VALUES (13,'395','gel-imaging-system',0,1),(14,'544','comet-assay-analysis-system',0,1),(15,'405','immunoblot-scanner',0,1),(16,'957','immunostainer',0,1),(17,'536','western-processing-system',0,1),(18,'105','variable-mode-imager',0,1),(19,'101','scanning-camera',0,1),(20,'622','gel-dryer',0,1),(21,'1453','thermal-cycler',0,1),(22,'934','sequencing-equipment',0,1);",
      ]); // Should split multi-row into individual statements
    });

    test('should extract multiple INSERT statements for same table', () => {
      const sqlContent = `
        CREATE TABLE \`test\` (id int);
        INSERT INTO \`test\` VALUES (1);
        INSERT INTO \`test\` VALUES (2);
        INSERT INTO \`other\` VALUES (3);
      `;

      const result = converter.testExtractInsertStatements(sqlContent, 'test');
      expect(result).toEqual([
        'INSERT INTO `test` VALUES (1);',
        'INSERT INTO `test` VALUES (2);',
      ]);
    });

    test('should handle multi-row INSERT statements', () => {
      const result = converter.testExtractInsertStatements(
        FULL_SQL_SAMPLE,
        'batiment',
      );
      expect(result.length).toBeGreaterThan(1); // Should split multi-row into individual statements
      expect(result[0]).toContain('INSERT INTO `batiment` VALUES');
      expect(result[0]).toContain("(1,'504A',NULL,1,1,NULL,0,0)");
    });

    test('should return empty array for non-existent table', () => {
      const result = converter.testExtractInsertStatements(
        FULL_SQL_SAMPLE,
        'nonexistent',
      );
      expect(result).toEqual([]);
    });

    test('should handle table names with different quote styles', () => {
      const sqlContent = `
        INSERT INTO test VALUES (1);
        INSERT INTO \`test\` VALUES (2);
        INSERT INTO "test" VALUES (3);
      `;

      const result = converter.testExtractInsertStatements(sqlContent, 'test');
      expect(result).toHaveLength(3);
    });

    test('should handle case-insensitive table names', () => {
      const sqlContent = `
        INSERT INTO \`Test\` VALUES (1);
        INSERT INTO \`TEST\` VALUES (2);
        INSERT INTO \`test\` VALUES (3);
      `;

      const result = converter.testExtractInsertStatements(sqlContent, 'test');
      expect(result).toHaveLength(3);
    });
  });
});
