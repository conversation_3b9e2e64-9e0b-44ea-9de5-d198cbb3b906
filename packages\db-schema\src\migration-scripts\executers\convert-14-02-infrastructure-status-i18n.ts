import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { InfrastructureStatusI18nMigrationConverter } from '../converters/14-02-convert-infrastructure-status-i18n-inserts';

async function convertInfrastructureStatusI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new InfrastructureStatusI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertInfrastructureStatusI18nData().catch(console.error);
