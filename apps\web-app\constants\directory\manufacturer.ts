import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const initialColumnVisibility = {
  acronym: true,
  createdAt: false,
  id: true,
  lastUpdatedAt: true,
  name: true,
  organizationId: false,
  parent: true,
} as const;

export const manufacturerFormSections = {
  contactDetails: {
    key: 'description.contactDetails',
    order: 2,
  },
  description: {
    key: 'description',
    order: 1,
  },
} as const;

export const manufacturerFormDefaultValues = (
  locale: SupportedLocale,
): VendorFormSchema => ({
  alias: [{ locale, value: '' }],
  contacts: [],
  dateEnd: null,
  name: [{ locale, value: '' }],
  phones: [],
});
