/**
 * Test data samples extracted from the real MySQL dump file
 *
 * This file contains real data patterns that have caused parsing issues
 * in the migration system, ensuring our tests cover actual failure scenarios.
 */

// Real CREATE TABLE statement from batiment table
export const BATIMENT_CREATE_TABLE = `CREATE TABLE \`batiment\` (
  \`id\` smallint(6) NOT NULL AUTO_INCREMENT,
  \`uid\` varchar(36) DEFAULT NULL COMMENT 'Identifiant SADVR ou du DI',
  \`pseudonyme\` varchar(150) DEFAULT NULL COMMENT 'Alias',
  \`juridiction_id\` int(11) NOT NULL COMMENT 'Juridiction: Établissement ou unité',
  \`campus_id\` smallint(6) NOT NULL COMMENT 'Campus',
  \`slug\` varchar(255) DEFAULT NULL,
  \`removed\` tinyint(1) NOT NULL DEFAULT '0',
  \`validated\` tinyint(1) DEFAULT '0',
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`uid\` (\`uid\`),
  UNIQUE KEY \`slug\` (\`slug\`),
  UNIQUE KEY \`pseudonyme\` (\`pseudonyme\`),
  KEY \`juridiction__batiment__fk\` (\`juridiction_id\`),
  KEY \`campus__batiment__fk\` (\`campus_id\`),
  CONSTRAINT \`campus__batiment__fk\` FOREIGN KEY (\`campus_id\`) REFERENCES \`campus\` (\`id\`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT \`juridiction__batiment__fk\` FOREIGN KEY (\`juridiction_id\`) REFERENCES \`organization\` (\`organization_id\`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8;`;

// Real INSERT statement from batiment table (multi-row)
export const BATIMENT_INSERT_STATEMENT = `INSERT INTO \`batiment\` VALUES (1,'504A',NULL,1,1,NULL,0,0),(2,'511A',NULL,1,1,NULL,0,0),(3,'511C',NULL,1,1,NULL,0,0),(64,'Possiblement 583A',NULL,1374,2,NULL,0,0),(65,'Possiblement 596A',NULL,1294,3,NULL,0,0),(66,'Possiblement 698A',NULL,1305,4,NULL,0,0),(67,'689A; 689B; 690A',NULL,1292,5,NULL,0,0);`;

// Real INSERT statement from batiment_trad table with French characters and quotes
export const BATIMENT_TRAD_INSERT_STATEMENT = `INSERT INTO \`batiment_trad\` VALUES (1,1,'fr','Samuel-Bronfman'),(7,7,'fr','Centre d''éducation physique et des sports (CEPSUM)'),(13,13,'fr','Centre d''éducation physique et des sports (CEPSUM)'),(15,15,'fr','2910, boul. Édouard-Montpetit'),(34,34,'fr','520, ch. de la Côte-Sainte-Catherine'),(67,67,'fr','Complexe des sciences');`;

// CREATE TABLE for equipment category (problematic table)
export const CATEGORIE_EQUIPEMENT_CREATE_TABLE = `CREATE TABLE \`categorie_equipement\` (
  \`id\` smallint(6) NOT NULL AUTO_INCREMENT,
  \`uid\` varchar(36) DEFAULT NULL COMMENT 'Identifiant SADVR ou du DI',
  \`slug\` varchar(255) DEFAULT NULL,
  \`removed\` tinyint(1) NOT NULL DEFAULT '0',
  \`validated\` tinyint(1) DEFAULT '0',
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`uid\` (\`uid\`),
  UNIQUE KEY \`slug\` (\`slug\`)
) ENGINE=InnoDB AUTO_INCREMENT=829 DEFAULT CHARSET=utf8;`;

// Sample of equipment category INSERT (first few rows)
export const CATEGORIE_EQUIPEMENT_INSERT_STATEMENT = `INSERT INTO \`categorie_equipement\` VALUES (1,'1438','analysis-equipment',0,1),(2,'1503','biomedical-analysis-equipment',0,1),(3,'72','electrophysiology-equipment',0,1);`;

// Complex INSERT with special characters and quotes
export const COMPLEX_INSERT_WITH_QUOTES = `INSERT INTO \`test_table\` VALUES (1,'Centre hospitalier de l''Université de Montréal','Description with "double quotes"',NULL),(2,'Text with, comma inside','Another ''quoted'' text',123);`;

// Single row INSERT statement (what BaseConverter creates after splitting)
export const SINGLE_ROW_INSERT = `INSERT INTO \`batiment\` VALUES (1,'504A',NULL,1,1,NULL,0,0);`;

// Malformed INSERT statements for error testing
// biome-ignore lint/style/noUnusedTemplateLiteral: <explanation>
export const MALFORMED_INSERT_NO_VALUES = `INSERT INTO \`test_table\` (id, name);`;
export const MALFORMED_INSERT_UNMATCHED_QUOTES = `INSERT INTO \`test_table\` VALUES (1,'unmatched quote);`;
export const MALFORMED_INSERT_UNMATCHED_PARENS = `INSERT INTO \`test_table\` VALUES (1,'test',2;`;

// CREATE TABLE with complex column definitions
export const COMPLEX_CREATE_TABLE = `CREATE TABLE \`complex_table\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`enum_field\` enum('option1','option2','option3') DEFAULT 'option1',
  \`text_field\` text COLLATE utf8_unicode_ci,
  \`decimal_field\` decimal(10,2) DEFAULT '0.00',
  PRIMARY KEY (\`id\`),
  KEY \`idx_enum\` (\`enum_field\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;`;

// Test data for CSV parsing edge cases
export const CSV_TEST_CASES = {
  simple: "1,'test',NULL,123",
  withCommas: "1,'text, with comma',NULL,123",
  withQuotes: "1,'text with ''quotes''',NULL,123",
  withDoubleQuotes: '1,"text with ""quotes""",NULL,123',
  mixedQuotes: `1,'single quotes',"double quotes",NULL`,
  escaped: "1,'text with ''escaped'' quotes',NULL,123",
  french:
    "392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL",
  complex: `1,'Complex, "mixed" ''quotes'' text',NULL,123,'Final field'`,
  empty: '',
  nullOnly: 'NULL',
  multipleNulls: 'NULL,NULL,NULL',
};

// Test data for splitValueRows edge cases
export const SPLIT_ROWS_TEST_CASES = {
  singleRow: "(1,'test',NULL)",
  multipleRows:
    "(378,189,'fr','collecteur de fractions','Le collecteur de fractions est un appareil conçu pour collecter des échantillons courants ou particuliers de l''effluent de colonne, et les stocker pour qu''ils soient récupérables. Les récipients de stockage sont généralement des éprouvettes ou flacons d''échantillon orientés dans un disque rotatif ou sur une courroie mobile, et dont le mouvement est contrôlé par microprocesseur. Sur signal du microprocesseur, la prochaine éprouvette est déposée sous la vanne, et l''effluent est échantillonné jusqu''à ce que l''ordinateur émette un nouveau signal. Une fois les propriétés du chromatogramme de la séparation définies, un programme de collection peut être rédigé. On peut collecter les fractions à l''aide d''une minuterie, soit à des intervalles ou à des heures précises afin d''échantillonner aux moments d''activité de pointe voulus. On peut aussi collecter les fractions en surveillant les lectures du détecteur: aux moments de dégagement maximal d''effluent, le collecteur de fractions sera déclenché, et cette concentration sera collectée dans un flacon précis. Quand l''effluent retourne au taux de référence, on dispose de la colonne jusqu''à ce que l''effluent atteigne à nouveau un taux de pointe. Les collecteurs de fraction sont couramment utilisés avec les systèmes de chromatographie en phase liquide.',NULL,NULL),(379,190,'en','UV detector','A part of a liquid chromatography instrument that detects absorption of UV light by samples. The output is recorded as a series of peaks - each one representing a compound in the mixture passing through the detector and absorbing UV light.',NULL,NULL),(380,190,'fr','détecteur UV','Partie du système de chromatographie à phase liquide qui détecte l''absorption de lumière UV par les échantillons. Les sorties sont enregistrées comme une série de pics - chacun représente un composé du mélange qui traverse le détecteur et absorbe de la lumière UV.',NULL,NULL),(381,191,'en','single wavelength UV detector',NULL,NULL,NULL),(382,191,'fr','transilluminateur pour systèmes de documentation sur gel/à UV/de longueur d''onde unique',NULL,NULL,NULL),(383,192,'en','photodiode array detector',NULL,NULL,NULL),(384,192,'fr','détecteur à matrice de photodiodes',NULL,NULL,NULL),(385,193,'en','pump system',NULL,NULL,NULL),(386,193,'fr','pompe',NULL,NULL,NULL),(387,194,'en','quaternary pump system','A pump system that pump and mix up to four different solvents in parallel.',NULL,NULL),(388,194,'fr','système de pompage quaternaire','Système de pompage qui mélange simultanément jusqu''à 4 solvants.',NULL,NULL),(389,195,'en','nano pump system','A pump system optimized for nano flow chromatography.',NULL,NULL),(390,195,'fr','système UHPLC à nanopompe','Système de pompage UHPLC avec nanopompes.',NULL,NULL),(391,196,'en','vacuum degasser','A degassing system used for degassing solvents in liquid chromatography. Dissolved gasses, usually nitrogen and oxygen from the air, tend to be evolved in the mobile phase as the pressure is reduced when the mobile phase leaves the liquid chromatography column and enters the detector. Gasses in the mobile phase in the detector can produce completely unacceptable noise and, thus, must be removed. The dissolved gasses were originally removed under vacuum but, unfortunately, are soon replaced if the solvent is left in contact with air at atmospheric pressure. For this reason degassing is now usually carried out by bubbling helium through the mobile phase reservoirs. Secondly, vacuum is used in the thermionic detector. This consists of a device, very similar in design to the thermionic valve which is attached to a vacuum and a small quantity of the eluent from a gas chromatography column allowed to bleed through it. Helium is used as the carrier gas. The presence of solute vapor causes the thermionic current to fall. This type of detector tends to become contaminated rather readily.',NULL,NULL),(392,196,'fr','dégazeur sous vide','Système de dégazement utilisé pour dégazer les solvants de chromatographie en phase liquide. On tend à évoluer les gaz dissous (généralement l''azote et l''oxygène de l''air) à la phase mobile, à plus basse pression, quand à cette étape les composants quittent la colonne à chromatographie et pénètrent le lecteur. La présence de gaz dans le lecteur peut causer un bruitage de données inacceptable, et ils doivent donc être retirés. À l''origine, les gaz dissous étaient éliminés sous vide; cependant, ils étaient vite remplacés si le solvant entrait en contact avec de l''air à pression atmosphérique. Pour cette raison, le dégazage est maintenant effectué en bouillonnant de l''hélium dans les réservoirs de phase liquide. Dans un deuxième temps, la pompe est utilisée dans le détecteur thermoionique. Ce dispositif ressemble à la valve thermoionique fixée à une pompe; on laisse une petite quantité d''éluat de la colonne de chromatographie gazeuse le traverser. L''hélium est utilisé comme gaz vecteur. La présence de valeur de soluté cause une baisse de courant thermoionique. Ce type de détecteur est facilement sujet à contamination.',NULL,NULL),(393,197,'en','flash pump system','Any pump system used in flash column chromatography to push the solvent through the column. Better flow rates can be achieved by using a pump or by using compressed gas (e.g. air, nitrogen, or argon) to push the solvent through the column (flash column chromatography).',NULL,NULL),(394,197,'fr','pompe pour chromatographie rapide sur colonne','Dispositif de pompage utilisé dans les systèmes pour chromatographie rapide sur colonne pour rapidement propulser le solvant dans celle-ci. L''utilisation d''une pompe ou d''un gaz comprimé (air, nitrogène, argon) pour l''injection du solvant dans la colonne accroît le débit d''écoulement (chromatographie flash).',NULL,NULL),(395,198,'en','isocratic pump system','A pump system optimized for isocratic chromatography.',NULL,NULL),(396,198,'fr','pompe isocratique pour chromatographie UHPLC','Système de pompage UHPLC avec pompes isocratiques.',NULL,NULL),(397,199,'en','gradient pump system','A pump system optimized for gradient chromatography.',NULL,NULL),(398,199,'fr','pompe pour chromatographie UHPLC à gradients','Système de pompage optimisé pour la chromatographie UHPLC à gradients.',NULL,NULL),(399,200,'en','capillary pump system','A pump system optimized for capillary chromatography.',NULL,NULL),(400,200,'fr','pompe pour chromatographie sur colonne capillaire','Pompe conçue pour la chromatographie sur colonne capillaire',NULL,NULL),(401,201,'en','liquid chromatography coupled mass spectrometer',NULL,NULL,NULL),(402,201,'fr','spectromètre de masse à chromatographie en phase liquide',NULL,NULL,NULL),(403,202,'en','flash chromatograph',NULL,NULL,NULL),(404,202,'fr','chromatographe flash',NULL,NULL,NULL),(405,203,'en','thermal analysis',NULL,NULL,NULL),(406,203,'fr','analyse thermique',NULL,NULL,NULL),(407,204,'en','calorimeter',NULL,NULL,NULL),(408,204,'fr','calorimètre',NULL,NULL,NULL),(409,205,'en','isothermal titration calorimeter','An instrument used to determine the thermodynamic parameters of interactions in solution. It is most often used to study the binding of small molecules (such as medicinal compounds) to larger macromolecules (proteins, DNA etc.).',NULL,NULL)",
  withCommas: "(1,'text, with comma',NULL),(2,'another, text',NULL)",
  withQuotes: "(1,'text with ''quotes''',NULL),(2,'more ''quotes''',NULL)",
  nested: "(1,'(nested parens)',NULL),(2,'more (nested)',NULL)",
  complex:
    "(1,'Complex, \"mixed\" ''quotes'' text',NULL),(2,'Another complex (text)',123)",
};

// Full SQL content samples for integration tests
export const FULL_SQL_SAMPLE = `
-- Table structure for table \`batiment\`
${BATIMENT_CREATE_TABLE}

-- Dumping data for table \`batiment\`
LOCK TABLES \`batiment\` WRITE;
/*!40000 ALTER TABLE \`batiment\` DISABLE KEYS */;
${BATIMENT_INSERT_STATEMENT}
/*!40000 ALTER TABLE \`batiment\` ENABLE KEYS */;
UNLOCK TABLES;

-- Table structure for table \`batiment_trad\`
CREATE TABLE \`batiment_trad\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`data_id\` smallint(6) NOT NULL,
  \`language_id\` varchar(8) NOT NULL,
  \`nom\` varchar(150) DEFAULT NULL,
  PRIMARY KEY (\`id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dumping data for table \`batiment_trad\`
LOCK TABLES \`batiment_trad\` WRITE;
/*!40000 ALTER TABLE \`batiment_trad\` DISABLE KEYS */;
${BATIMENT_TRAD_INSERT_STATEMENT}
/*!40000 ALTER TABLE \`batiment_trad\` DISABLE KEYS */;
UNLOCK TABLES;
`;

// Expected column names for test tables
export const EXPECTED_COLUMNS = {
  batiment: [
    'id',
    'uid',
    'pseudonyme',
    'juridiction_id',
    'campus_id',
    'slug',
    'removed',
    'validated',
  ],
  batiment_trad: ['id', 'data_id', 'language_id', 'nom'],
  categorie_equipement: ['id', 'uid', 'slug', 'removed', 'validated'],
};

// Expected parsed values for test cases
export const EXPECTED_PARSED_VALUES = {
  batiment_first_row: {
    id: '1',
    uid: '504A',
    pseudonyme: null,
    juridiction_id: '1',
    campus_id: '1',
    slug: null,
    removed: '0',
    validated: '0',
  },
  batiment_complex_row: {
    id: '67',
    uid: '689A; 689B; 690A',
    pseudonyme: null,
    juridiction_id: '1292',
    campus_id: '5',
    slug: null,
    removed: '0',
    validated: '0',
  },
};

// Error scenarios that should trigger warnings
export const ERROR_SCENARIOS = {
  notEnoughValues: {
    columns: [
      'id',
      'uid',
      'pseudonyme',
      'juridiction_id',
      'campus_id',
      'slug',
      'removed',
      'validated',
    ],
    values: ['1', '504A'], // Only 2 values for 8 columns
    expectedWarning: 'Warning: Not enough values (2) for columns (8)',
  },
  tooManyValues: {
    columns: ['id', 'name'],
    values: ['1', 'test', 'extra', 'values'],
    expectedWarning: 'Too many values provided',
  },
};
