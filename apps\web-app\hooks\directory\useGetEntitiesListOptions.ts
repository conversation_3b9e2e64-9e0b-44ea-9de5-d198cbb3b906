import type { RieServiceParams } from '@/constants/rie-client';
import { getEntitiesList } from '@/services/directory/entities';
import { mapEntitiesToTableColumns } from '@/services/mappers/bottin/entities';
import type { DirectoryFull } from '@/types/directory/directory';
import type { ApiReturnType } from '@/types/common';
import type { DirectoryEntity } from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export function entitiesListOptions(
  pageParam: number,
  params: RieServiceParams,
  queryParams: string,
  directoryEntity: DirectoryEntity,
) {
  return queryOptions({
    queryFn: () =>
      getEntitiesList({
        directoryEntity,
        pageParam,
        params,
        queryParams,
      }),
    queryKey: [
      'directoryEntities',
      { directoryEntity, params, queryParams, pageParam },
    ],
    select: (data: ApiReturnType<DirectoryFull[]>) =>
      mapEntitiesToTableColumns(data, directoryEntity),
  });
}
