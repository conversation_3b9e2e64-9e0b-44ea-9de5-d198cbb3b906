'use client';

import {
  type PermissionsGroupFormSchema,
  permissionsGroupFormSchema,
} from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form.schema';
import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useGetAllPermissions } from '@/hooks/admin/permissions/permissions.hook';
import type { RequestStatus } from '@/types/common';
import { MultiSelectNew } from '@/ui/multi-select-new';
import { effectTsResolver } from '@hookform/resolvers/effect-ts';
import { useForm } from 'react-hook-form';

interface PermissionFormProps {
  initialData?: PermissionsGroupFormSchema;
  onSubmitAction: (data: PermissionsGroupFormSchema) => void;
  status: RequestStatus;
}

export function PermissionsGroupForm({
  initialData,
  onSubmitAction,
  status,
}: PermissionFormProps) {
  const { data: permissions, status: permissionsStatus } =
    useGetAllPermissions<'select'>({ view: 'select' });

  const defaultValues: Partial<PermissionsGroupFormSchema> = {
    name: initialData?.name ?? '',
    description: initialData?.description ?? '',
    permissions: initialData?.permissions ?? [],
  };

  const form = useForm<PermissionsGroupFormSchema>({
    resolver: effectTsResolver(permissionsGroupFormSchema),
    defaultValues,
  });

  const { formState } = form;
  const { isDirty, isValid } = formState;

  if (permissionsStatus === 'pending') {
    return <div>Loading...</div>;
  }

  if (permissionsStatus === 'error') {
    return <div>Error</div>;
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {initialData ? 'Edit Permission Group' : 'Create Permission Group'}
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmitAction)}>
          <CardContent className="space-y-4">
            {formState.errors.root && (
              <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                {formState.errors.root.message}
              </div>
            )}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip required label="Name" />
                  <FormControl>
                    <Input
                      placeholder="e.g., Admin Group, Editor Group"
                      {...field}
                    />
                  </FormControl>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip label="Description" />
                  <FormControl>
                    <Textarea
                      placeholder="Optional description of this permission group"
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide additional context about this permission group
                    (optional)
                  </FormDescription>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="permissions"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip label="Permissions" />
                  <FormControl>
                    <MultiSelectNew
                      onValueChange={field.onChange}
                      options={permissions ?? []}
                      defaultValue={field.value}
                    />
                  </FormControl>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button
              type="submit"
              disabled={
                status === 'pending' || (!isDirty && !initialData) || !isValid
              }
            >
              {status === 'pending'
                ? 'Saving...'
                : initialData
                  ? 'Update Permission group'
                  : 'Create Permission group'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
