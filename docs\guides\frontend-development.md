# Frontend Development Guide

This guide covers everything you need to know about developing the RIE frontend application built with Next.js 15, React, and TypeScript.

## 🚀 Getting Started

### Prerequisites

- Completed [installation guide](../getting-started/installation.md)
- Understanding of React, Next.js, and TypeScript
- Familiarity with Tailwind CSS

### Development Setup

```bash
# Navigate to web app directory
cd apps/web-app

# Start development server
pnpm dev

# Application will be available at http://localhost:3000
```

## 🏗️ Project Structure

```
apps/web-app/src/
├── app/                           # Next.js App Router
│   ├── [locale]/                  # Internationalized routes
│   │   ├── (auth)/               # Auth route group
│   │   ├── (dashboard)/          # Dashboard route group
│   │   ├── layout.tsx            # Locale layout
│   │   └── page.tsx              # Home page
│   ├── globals.css               # Global styles
│   └── layout.tsx                # Root layout
├── components/                    # React components
│   ├── ui/                       # Shadcn/UI components
│   ├── forms/                    # Form components
│   ├── layout/                   # Layout components
│   └── features/                 # Feature-specific components
├── lib/                          # Utility libraries
│   ├── api/                      # API client functions
│   ├── auth/                     # Authentication utilities
│   ├── hooks/                    # Custom React hooks
│   ├── utils/                    # Utility functions
│   └── validations/              # Form validation schemas
├── stores/                       # Zustand stores
├── types/                        # TypeScript type definitions
└── styles/                       # Additional styles
```

## 🛠️ Common Development Tasks

### Creating a New Page

#### 1. Create the Page Component

```typescript
// app/[locale]/(dashboard)/equipment/page.tsx
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { EquipmentList } from '@/components/features/equipment/equipment-list'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('equipment')
  
  return {
    title: t('title'),
    description: t('description')
  }
}

export default async function EquipmentPage() {
  const t = await getTranslations('equipment')
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">{t('title')}</h1>
        <Button asChild>
          <Link href="/equipment/new">{t('addNew')}</Link>
        </Button>
      </div>
      
      <EquipmentList />
    </div>
  )
}
```

#### 2. Add Internationalization

Add translations to message files:

```json
// messages/en.json
{
  "equipment": {
    "title": "Equipment",
    "description": "Manage research equipment",
    "addNew": "Add Equipment",
    "list": {
      "name": "Name",
      "type": "Type",
      "status": "Status",
      "location": "Location"
    }
  }
}

// messages/fr.json
{
  "equipment": {
    "title": "Équipement",
    "description": "Gérer l'équipement de recherche",
    "addNew": "Ajouter un équipement",
    "list": {
      "name": "Nom",
      "type": "Type",
      "status": "Statut",
      "location": "Emplacement"
    }
  }
}
```

### Creating Components

#### 1. Feature Component

```typescript
// components/features/equipment/equipment-list.tsx
'use client'

import { useQuery } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { DataTable } from '@/components/ui/data-table'
import { equipmentApi } from '@/lib/api/equipment'
import { equipmentColumns } from './equipment-columns'

export function EquipmentList() {
  const t = useTranslations('equipment.list')
  
  const { data: equipment, isLoading, error } = useQuery({
    queryKey: ['equipment'],
    queryFn: equipmentApi.getAll
  })
  
  if (isLoading) {
    return <div className="flex justify-center p-8">Loading...</div>
  }
  
  if (error) {
    return <div className="text-red-500 p-4">Error loading equipment</div>
  }
  
  return (
    <DataTable
      columns={equipmentColumns}
      data={equipment || []}
      searchKey="name"
      searchPlaceholder={t('searchPlaceholder')}
    />
  )
}
```

#### 2. Table Columns Definition

```typescript
// components/features/equipment/equipment-columns.tsx
'use client'

import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, Edit, Trash } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Equipment } from '@/types/equipment'

export const equipmentColumns: ColumnDef<Equipment>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      return <Badge variant="outline">{type}</Badge>
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      const variant = status === 'AVAILABLE' ? 'success' : 
                    status === 'IN_USE' ? 'warning' : 'destructive'
      
      return <Badge variant={variant}>{status}</Badge>
    },
  },
  {
    accessorKey: 'location',
    header: 'Location',
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const equipment = row.original
      
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem className="text-red-600">
              <Trash className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
```

### Working with Forms

#### 1. Create Form Schema

```typescript
// lib/validations/equipment.ts
import { z } from 'zod'

export const createEquipmentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  type: z.enum(['MICROSCOPE', 'CENTRIFUGE', 'SPECTROMETER']),
  location: z.string().min(1, 'Location is required'),
  description: z.string().optional(),
})

export type CreateEquipmentData = z.infer<typeof createEquipmentSchema>
```

#### 2. Create Form Component

```typescript
// components/features/equipment/equipment-form.tsx
'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { createEquipmentSchema, CreateEquipmentData } from '@/lib/validations/equipment'
import { equipmentApi } from '@/lib/api/equipment'
import { toast } from 'sonner'

interface EquipmentFormProps {
  onSuccess?: () => void
}

export function EquipmentForm({ onSuccess }: EquipmentFormProps) {
  const t = useTranslations('equipment.form')
  const queryClient = useQueryClient()
  
  const form = useForm<CreateEquipmentData>({
    resolver: zodResolver(createEquipmentSchema),
    defaultValues: {
      name: '',
      type: 'MICROSCOPE',
      location: '',
      description: '',
    },
  })
  
  const createMutation = useMutation({
    mutationFn: equipmentApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipment'] })
      toast.success(t('createSuccess'))
      form.reset()
      onSuccess?.()
    },
    onError: (error) => {
      toast.error(t('createError'))
      console.error('Failed to create equipment:', error)
    },
  })
  
  const onSubmit = (data: CreateEquipmentData) => {
    createMutation.mutate(data)
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('name')}</FormLabel>
              <FormControl>
                <Input placeholder={t('namePlaceholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('type')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="MICROSCOPE">{t('types.microscope')}</SelectItem>
                  <SelectItem value="CENTRIFUGE">{t('types.centrifuge')}</SelectItem>
                  <SelectItem value="SPECTROMETER">{t('types.spectrometer')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('location')}</FormLabel>
              <FormControl>
                <Input placeholder={t('locationPlaceholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('description')}</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder={t('descriptionPlaceholder')} 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button 
          type="submit" 
          disabled={createMutation.isPending}
          className="w-full"
        >
          {createMutation.isPending ? t('creating') : t('create')}
        </Button>
      </form>
    </Form>
  )
}
```

### API Integration

#### 1. Create API Client

```typescript
// lib/api/equipment.ts
import { apiClient } from './client'
import { Equipment, CreateEquipmentData, UpdateEquipmentData } from '@/types/equipment'

export const equipmentApi = {
  getAll: async (): Promise<Equipment[]> => {
    const response = await apiClient.get('/equipment')
    return response.data
  },
  
  getById: async (id: string): Promise<Equipment> => {
    const response = await apiClient.get(`/equipment/${id}`)
    return response.data
  },
  
  create: async (data: CreateEquipmentData): Promise<Equipment> => {
    const response = await apiClient.post('/equipment', data)
    return response.data
  },
  
  update: async (id: string, data: UpdateEquipmentData): Promise<Equipment> => {
    const response = await apiClient.put(`/equipment/${id}`, data)
    return response.data
  },
  
  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/equipment/${id}`)
  },
}
```

#### 2. API Client Configuration

```typescript
// lib/api/client.ts
import axios from 'axios'
import { getSession } from '@/lib/auth/session'

export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL + '/api/v2',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(async (config) => {
  const session = await getSession()
  
  if (session?.token) {
    config.headers.Authorization = `Bearer ${session.token}`
  }
  
  return config
})

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login or refresh token
      window.location.href = '/auth/login'
    }
    
    return Promise.reject(error)
  }
)
```

### State Management

#### 1. Zustand Store

```typescript
// stores/equipment-store.ts
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Equipment } from '@/types/equipment'

interface EquipmentState {
  selectedEquipment: Equipment | null
  filters: {
    type?: string
    status?: string
    location?: string
  }
  
  // Actions
  setSelectedEquipment: (equipment: Equipment | null) => void
  setFilters: (filters: Partial<EquipmentState['filters']>) => void
  clearFilters: () => void
}

export const useEquipmentStore = create<EquipmentState>()(
  devtools(
    (set) => ({
      selectedEquipment: null,
      filters: {},
      
      setSelectedEquipment: (equipment) =>
        set({ selectedEquipment: equipment }),
      
      setFilters: (newFilters) =>
        set((state) => ({
          filters: { ...state.filters, ...newFilters }
        })),
      
      clearFilters: () =>
        set({ filters: {} }),
    }),
    { name: 'equipment-store' }
  )
)
```

#### 2. Using the Store

```typescript
// components/features/equipment/equipment-filters.tsx
'use client'

import { useEquipmentStore } from '@/stores/equipment-store'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'

export function EquipmentFilters() {
  const { filters, setFilters, clearFilters } = useEquipmentStore()
  
  return (
    <div className="flex gap-4 items-center">
      <Select
        value={filters.type || ''}
        onValueChange={(value) => setFilters({ type: value || undefined })}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Filter by type" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="MICROSCOPE">Microscope</SelectItem>
          <SelectItem value="CENTRIFUGE">Centrifuge</SelectItem>
          <SelectItem value="SPECTROMETER">Spectrometer</SelectItem>
        </SelectContent>
      </Select>
      
      <Select
        value={filters.status || ''}
        onValueChange={(value) => setFilters({ status: value || undefined })}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Filter by status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="AVAILABLE">Available</SelectItem>
          <SelectItem value="IN_USE">In Use</SelectItem>
          <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
        </SelectContent>
      </Select>
      
      <Button variant="outline" onClick={clearFilters}>
        Clear Filters
      </Button>
    </div>
  )
}
```

### Custom Hooks

#### 1. Data Fetching Hook

```typescript
// lib/hooks/use-equipment.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { equipmentApi } from '@/lib/api/equipment'
import { CreateEquipmentData, UpdateEquipmentData } from '@/types/equipment'
import { toast } from 'sonner'

export function useEquipment() {
  return useQuery({
    queryKey: ['equipment'],
    queryFn: equipmentApi.getAll,
  })
}

export function useEquipmentById(id: string) {
  return useQuery({
    queryKey: ['equipment', id],
    queryFn: () => equipmentApi.getById(id),
    enabled: !!id,
  })
}

export function useCreateEquipment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: equipmentApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipment'] })
      toast.success('Equipment created successfully')
    },
    onError: () => {
      toast.error('Failed to create equipment')
    },
  })
}

export function useUpdateEquipment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEquipmentData }) =>
      equipmentApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipment'] })
      toast.success('Equipment updated successfully')
    },
    onError: () => {
      toast.error('Failed to update equipment')
    },
  })
}
```

#### 2. Form Hook

```typescript
// lib/hooks/use-equipment-form.ts
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createEquipmentSchema, CreateEquipmentData } from '@/lib/validations/equipment'
import { useCreateEquipment } from './use-equipment'

export function useEquipmentForm(onSuccess?: () => void) {
  const createMutation = useCreateEquipment()
  
  const form = useForm<CreateEquipmentData>({
    resolver: zodResolver(createEquipmentSchema),
    defaultValues: {
      name: '',
      type: 'MICROSCOPE',
      location: '',
      description: '',
    },
  })
  
  const onSubmit = (data: CreateEquipmentData) => {
    createMutation.mutate(data, {
      onSuccess: () => {
        form.reset()
        onSuccess?.()
      },
    })
  }
  
  return {
    form,
    onSubmit,
    isLoading: createMutation.isPending,
  }
}
```

## 🎨 Styling and UI

### Using Shadcn/UI Components

```typescript
// Install new components
npx shadcn-ui@latest add button
npx shadcn-ui@latest add input
npx shadcn-ui@latest add form
```

### Custom Styling

```typescript
// components/ui/custom-button.tsx
import { Button, ButtonProps } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface CustomButtonProps extends ButtonProps {
  gradient?: boolean
}

export function CustomButton({ 
  className, 
  gradient = false, 
  ...props 
}: CustomButtonProps) {
  return (
    <Button
      className={cn(
        gradient && 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700',
        className
      )}
      {...props}
    />
  )
}
```

## 🧪 Testing

### Component Testing

```typescript
// components/features/equipment/__tests__/equipment-list.test.tsx
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { EquipmentList } from '../equipment-list'

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('EquipmentList', () => {
  it('renders loading state', () => {
    renderWithProviders(<EquipmentList />)
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })
})
```

### Hook Testing

```typescript
// lib/hooks/__tests__/use-equipment.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useEquipment } from '../use-equipment'
import { equipmentApi } from '@/lib/api/equipment'

vi.mock('@/lib/api/equipment')

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useEquipment', () => {
  it('fetches equipment data', async () => {
    const mockEquipment = [{ id: '1', name: 'Test Equipment' }]
    vi.mocked(equipmentApi.getAll).mockResolvedValue(mockEquipment)
    
    const { result } = renderHook(() => useEquipment(), {
      wrapper: createWrapper(),
    })
    
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })
    
    expect(result.current.data).toEqual(mockEquipment)
  })
})
```

## 🚀 Performance Optimization

### Code Splitting

```typescript
// Lazy load components
import { lazy, Suspense } from 'react'

const EquipmentForm = lazy(() => import('./equipment-form'))

export function EquipmentPage() {
  return (
    <div>
      <Suspense fallback={<div>Loading form...</div>}>
        <EquipmentForm />
      </Suspense>
    </div>
  )
}
```

### Image Optimization

```typescript
import Image from 'next/image'

export function EquipmentCard({ equipment }) {
  return (
    <div className="card">
      <Image
        src={equipment.imageUrl}
        alt={equipment.name}
        width={300}
        height={200}
        className="rounded-lg"
        priority={false} // Set to true for above-the-fold images
      />
    </div>
  )
}
```

### Memoization

```typescript
import { memo, useMemo } from 'react'

export const EquipmentCard = memo(({ equipment }) => {
  const formattedDate = useMemo(() => 
    new Date(equipment.createdAt).toLocaleDateString(),
    [equipment.createdAt]
  )
  
  return (
    <div className="card">
      <h3>{equipment.name}</h3>
      <p>Created: {formattedDate}</p>
    </div>
  )
})
```

## 🔧 Development Tools

### Storybook

```bash
# Start Storybook
pnpm storybook

# Build Storybook
pnpm build-storybook
```

Create stories for components:

```typescript
// components/ui/button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { Button } from './button'

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  args: {
    children: 'Button',
    variant: 'default',
  },
}

export const Secondary: Story = {
  args: {
    children: 'Button',
    variant: 'secondary',
  },
}
```

### Next.js DevTools

- **Next.js Bundle Analyzer**: Analyze bundle size
- **React DevTools**: Debug React components
- **TanStack Query DevTools**: Debug queries and mutations

## 🆘 Troubleshooting

### Common Issues

#### Hydration Errors

- Ensure server and client render the same content
- Use `useEffect` for client-only code
- Check for date/time formatting differences

#### API Connection Issues

- Verify `NEXT_PUBLIC_API_BASE_URL` environment variable
- Check CORS configuration on the API
- Ensure authentication tokens are properly set

#### Build Errors

- Check TypeScript errors: `pnpm type-check`
- Verify all imports are correct
- Ensure environment variables are set for build

---

**Next**: [Testing Guide](./testing.md) | [Deployment Guide](./deployment.md)
