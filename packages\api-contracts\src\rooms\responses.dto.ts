import * as Schema from 'effect/Schema';

// Base schema for rooms
export const RoomBaseDtoSchema = Schema.Struct({
    id: Schema.String,
    isActive: Schema.Boolean,
    number: Schema.String,
    area: Schema.NullOr(Schema.Number),
    floorLoad: Schema.NullOr(Schema.Number),
    buildingId: Schema.NullOr(Schema.String),
});

// DTO for lists of rooms (e.g., view='list')
export const RoomListItemDtoSchema = Schema.Struct({
    id: Schema.String,
    text: Schema.String,
    building: Schema.NullOr(Schema.String),
    number: Schema.String,
    area: Schema.NullOr(Schema.Number),
    jurisdiction: Schema.NullOr(Schema.String),
    lastUpdatedAt: Schema.String,
    createdAt: Schema.String,
});

export type RoomListItemDTO = Schema.Schema.Type<
    typeof RoomListItemDtoSchema
>;

// DTO for the detailed view of a single room (e.g., view='detail')
export const RoomDetailResponseDtoSchema = Schema.extend(
    RoomBaseDtoSchema,
    Schema.Struct({
        building: Schema.NullOr(Schema.Struct({
            id: Schema.String,
            name: Schema.String,
        })),
        createdAt: Schema.String,
        updatedAt: Schema.String,
        modifiedBy: Schema.NullOr(Schema.String),
    }),
);

export type RoomDetailResponseDTO = Schema.Schema.Type<
    typeof RoomDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const RoomEditResponseDtoSchema = Schema.extend(
    RoomBaseDtoSchema,
    Schema.Struct({
        building: Schema.NullOr(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        createdAt: Schema.String,
        updatedAt: Schema.String,
        modifiedBy: Schema.NullOr(Schema.String),
    }),
);

export type RoomEditResponseDTO = Schema.Schema.Type<
    typeof RoomEditResponseDtoSchema
>;