'use client';

import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form-schema';
import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import {
  createRole,
  deleteRole,
  updateRole,
} from '@/services/permissions/roles.service';
import type { RoleResultType } from '@/types/permission.type';
import type {
  CollectionViewParamType,
  RoleList,
  RoleSelect,
} from '@rie/domain/types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAllRolesOptions, getRoleByIdOptions } from './roles.options';

export type RoleResultTypeHook<View extends CollectionViewParamType['view']> =
  View extends 'select' ? RoleSelect[] : RoleList[];

export const useGetAllRoles = <View extends CollectionViewParamType['view']>({
  view,
}: CollectionViewParamType) => {
  return useQuery<RoleResultType<View>>(getAllRolesOptions({ view }));
};

export const useGetRoleById = <View extends 'detail' | 'edit' = 'detail'>(
  id: string,
  { view = 'detail' as View }: { view?: View } = {},
) => {
  return useQuery(getRoleByIdOptions(id, { view }));
};

export const useCreateRole = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (payload: RoleFormSchema) => await createRole(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role created successfully',
        variant: 'success',
      });
      router.push('/admin/roles');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create role',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateRole = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      payload,
      id,
    }: { payload: RoleFormSchema; id: string }) => {
      return await updateRole({ payload, id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role updated successfully',
        variant: 'success',
      });
      router.push('/admin/roles');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update role',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role deleted successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to delete role',
        variant: 'destructive',
      });
    },
  });
};
