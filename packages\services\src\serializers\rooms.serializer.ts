import {
    type RoomDetailResponseDTO,
    RoomDetailResponseDtoSchema,
    type RoomEditResponseDTO,
    RoomEditResponseDtoSchema,
    type RoomListItemDTO,
    RoomListItemDtoSchema,
    type SelectOptionDTO,
    SelectOptionDtoSchema,
} from '@rie/api-contracts';
import type { Room } from '@rie/domain/aggregates';
import { DbRoomDataSchema } from '@rie/domain/schemas';
import type { RoomData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes a Room aggregate into the shape required for a list view.
 */
export function serializeRoomToListItem(
    room: Room,
): Effect.Effect<RoomListItemDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbRoomDataSchema,
        RoomListItemDtoSchema,
        {
            strict: true,
            decode: (roomData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // For list view, we use the room number as text
                        const safeText = roomData.number.trim() !== ''
                            ? roomData.number
                            : roomData.id;

                        return {
                            id: roomData.id,
                            text: safeText,
                            building: null, // Will be populated by service layer with building info
                            number: roomData.number,
                            area: roomData.area,
                            jurisdiction: null, // Will be populated by service layer with jurisdiction info
                            lastUpdatedAt: roomData.updatedAt,
                            createdAt: roomData.createdAt,
                        };
                    },
                    catch: (error) => new ParseResult.Type(
                        ast,
                        roomData,
                        `Failed to serialize room to list item: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    ),
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        room.toRaw(),
        Effect.flatMap((rawData) => Schema.decode(transformer)(rawData)),
    );
}

/**
 * Serializes a Room aggregate into the shape required for a detailed view.
 */
export function serializeRoomToDetail(
    room: Room,
): Effect.Effect<RoomDetailResponseDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbRoomDataSchema,
        RoomDetailResponseDtoSchema,
        {
            strict: true,
            decode: (roomData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        return {
                            id: roomData.id,
                            isActive: roomData.isActive,
                            number: roomData.number,
                            area: roomData.area,
                            floorLoad: roomData.floorLoad,
                            buildingId: roomData.buildingId,
                            building: null, // Will be populated by service layer
                            createdAt: roomData.createdAt,
                            updatedAt: roomData.updatedAt,
                            modifiedBy: roomData.modifiedBy,
                        };
                    },
                    catch: (error) => new ParseResult.Type(
                        ast,
                        roomData,
                        `Failed to serialize room to detail: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    ),
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        room.toRaw(),
        Effect.flatMap((rawData) => Schema.decode(transformer)(rawData)),
    );
}

/**
 * Serializes a Room aggregate into a simple value/label pair for select inputs.
 */
export function serializeRoomToSelectOption(
    room: Room,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbRoomDataSchema,
        SelectOptionDtoSchema,
        {
            strict: true,
            decode: (roomData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        const label = roomData.number.trim() !== ''
                            ? roomData.number
                            : `Room ${roomData.id}`;

                        return {
                            value: roomData.id,
                            label,
                        };
                    },
                    catch: (error) => new ParseResult.Type(
                        ast,
                        roomData,
                        `Failed to serialize room to select option: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    ),
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        room.toRaw(),
        Effect.flatMap((rawData) => Schema.decode(transformer)(rawData)),
    );
}

/**
 * Serializes database room data into the shape required for an edit form.
 */
export function serializeRoomToFormEdit(
    roomData: RoomData,
): Effect.Effect<RoomEditResponseDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbRoomDataSchema,
        RoomEditResponseDtoSchema,
        {
            strict: true,
            decode: (source, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        return {
                            id: source.id,
                            isActive: source.isActive,
                            number: source.number,
                            area: source.area,
                            floorLoad: source.floorLoad,
                            buildingId: source.buildingId,
                            building: null, // Will be populated by service layer
                            createdAt: source.createdAt,
                            updatedAt: source.updatedAt,
                            modifiedBy: source.modifiedBy,
                        };
                    },
                    catch: (error) => new ParseResult.Type(
                        ast,
                        source,
                        `Failed to serialize room to form edit: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    ),
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return Schema.decode(transformer)(roomData);
}