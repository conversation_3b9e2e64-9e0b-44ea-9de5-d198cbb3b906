import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { PermissionGroupsMigrationConverter } from '../converters/one-time/00-03-convert-permission-groups';

async function convertPermissionGroupsData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new PermissionGroupsMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Permission groups conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during permission groups conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertPermissionGroupsData().catch(console.error);
