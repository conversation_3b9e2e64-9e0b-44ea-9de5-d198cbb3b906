/**
 * Vitest setup file for BaseConverter tests
 *
 * This file sets up the test environment for the migration system tests.
 */

import { beforeEach } from 'vitest';

// Mock console methods to reduce noise during tests
beforeEach(() => {
  // Optionally suppress console.log during tests
  // vi.spyOn(console, 'log').mockImplementation(() => {});
  // vi.spyOn(console, 'warn').mockImplementation(() => {});
});
