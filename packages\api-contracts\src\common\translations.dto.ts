import * as Schema from 'effect/Schema';
import { NormalizedNullableStringSchema } from './base.schemas';

// Describes the structure of a single translation field coming from an API request
export const OptionalLocalizedFieldDtoSchema = Schema.Struct({
  locale: Schema.String,
  value: NormalizedNullableStringSchema,
});

export type OptionalLocalizedFieldDto = Schema.Schema.Type<
  typeof OptionalLocalizedFieldDtoSchema
>;

export const RequiredLocalizedFieldDtoSchema = Schema.Struct({
  locale: Schema.String,
  value: Schema.String,
});

export type RequiredLocalizedFieldDto = Schema.Schema.Type<
  typeof RequiredLocalizedFieldDtoSchema
>;

export const TranslationDtoSchema = Schema.Struct({
  name: RequiredLocalizedFieldDtoSchema,
  description: OptionalLocalizedFieldDtoSchema,
  otherNames: OptionalLocalizedFieldDtoSchema,
  acronyms: OptionalLocalizedFieldDtoSchema,
});

export type TranslationDto = Schema.Schema.Type<typeof TranslationDtoSchema>;

export const TranslationInputDtoSchema = Schema.extend(
  TranslationDtoSchema.omit('name'),
  Schema.Struct({
    name: OptionalLocalizedFieldDtoSchema,
  }),
);

export type TranslationInputDto = Schema.Schema.Type<
  typeof TranslationInputDtoSchema
>;
