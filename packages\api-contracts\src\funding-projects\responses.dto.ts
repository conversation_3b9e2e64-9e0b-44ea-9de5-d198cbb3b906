import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    RequiredLocalizedFieldDtoSchema,
} from '../common';

// Base schema for funding projects
export const FundingProjectBaseDtoSchema = Schema.Struct({
    id: Schema.String,
    isActive: Schema.Boolean,
    holderId: Schema.NullOr(Schema.String),
    typeId: Schema.NullOr(Schema.String),
    fciId: Schema.NullOr(Schema.String),
    synchroId: Schema.NullOr(Schema.String),
    obtainingYear: Schema.NullOr(Schema.Number),
    endDate: Schema.NullOr(Schema.String),
});

// DTO for lists of funding projects (e.g., view='list')
export const FundingProjectListItemDtoSchema = Schema.Struct({
    id: Schema.String,
    text: Schema.String,
    titulaire: Schema.NullOr(Schema.String), // holder
    infrastructure: Schema.NullOr(Schema.String), // infrastructure
    lastUpdatedAt: Schema.String,
    createdAt: Schema.String,
    holderId: Schema.NullOr(Schema.String),
    typeId: Schema.NullOr(Schema.String),
    fciId: Schema.NullOr(Schema.String),
    synchroId: Schema.NullOr(Schema.String),
    obtainingYear: Schema.NullOr(Schema.Number),
    endDate: Schema.NullOr(Schema.String),
});

export type FundingProjectListItemDTO = Schema.Schema.Type<
    typeof FundingProjectListItemDtoSchema
>;

// Funding project-specific translation schemas
export const FundingProjectTranslationDtoSchema = Schema.Struct({
    name: RequiredLocalizedFieldDtoSchema,
    description: OptionalLocalizedFieldDtoSchema,
});

export const FundingProjectTranslationInputDtoSchema = Schema.Struct({
    name: OptionalLocalizedFieldDtoSchema,
    description: OptionalLocalizedFieldDtoSchema,
});

// DTO for the detailed view of a single funding project (e.g., view='detail')
export const FundingProjectDetailResponseDtoSchema = Schema.extend(
    FundingProjectBaseDtoSchema,
    Schema.Struct({
        translations: FundingProjectTranslationDtoSchema,
        holder: Schema.NullOr(Schema.Struct({
            id: Schema.String,
            name: Schema.String,
        })),
        fundingType: Schema.NullOr(Schema.Struct({
            id: Schema.String,
            name: Schema.String,
        })),
        createdAt: Schema.String,
        updatedAt: Schema.String,
        modifiedBy: Schema.NullOr(Schema.String),
    }),
);

export type FundingProjectDetailResponseDTO = Schema.Schema.Type<
    typeof FundingProjectDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const FundingProjectEditResponseDtoSchema = Schema.extend(
    FundingProjectBaseDtoSchema,
    Schema.Struct({
        translations: Schema.Array(FundingProjectTranslationInputDtoSchema),
        holder: Schema.NullOr(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        fundingType: Schema.NullOr(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        purchasedEquipment: Schema.Array(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        associateResearchers: Schema.Array(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        financedInfrastructures: Schema.Array(Schema.Struct({
            value: Schema.String,
            label: Schema.String,
        })),
        createdAt: Schema.String,
        updatedAt: Schema.String,
        modifiedBy: Schema.NullOr(Schema.String),
    }),
);

export type FundingProjectEditResponseDTO = Schema.Schema.Type<
    typeof FundingProjectEditResponseDtoSchema
>;

// DTO for select options (e.g., view='select')
export const FundingProjectSelectDtoSchema = Schema.Struct({
    value: Schema.String,
    label: Schema.String,
});

export type FundingProjectSelectDTO = Schema.Schema.Type<
    typeof FundingProjectSelectDtoSchema
>;