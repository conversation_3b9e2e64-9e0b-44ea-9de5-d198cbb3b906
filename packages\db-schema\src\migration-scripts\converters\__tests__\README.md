# BaseConverter Test Suite

## Overview

This directory contains comprehensive unit tests for the critical parsing methods in the `BaseConverter` class. These tests are essential to the MySQL-to-PostgreSQL migration system, as failures in these methods cause the entire migration to fail.

## Test Results Summary

✅ **All 63 tests passing (100% success rate)**

### Test Coverage

- **Line Coverage**: 51.52% of BaseConverter.ts
- **Branch Coverage**: 93.33%
- **Function Coverage**: 29.16%

The coverage focuses on the critical parsing methods that are essential for the migration system.

## Test Files

### 1. `base-converter.test.ts` (34 tests)
Main test file covering the core parsing functionality:
- **parseCSVValues** (12 tests): CSV parsing with quote/escape handling
- **splitValueRows** (8 tests): Multi-row INSERT statement splitting
- **extractValuesFromInsertStatement** (8 tests): Value extraction from INSERT statements
- **extractInsertStatements** (6 tests): INSERT statement discovery

### 2. `base-converter-part2.test.ts` (18 tests)
Secondary test file covering schema parsing:
- **extractColumnNames** (9 tests): Column name extraction from CREATE TABLE
- **extractCreateStatement** (9 tests): CREATE TABLE statement extraction

### 3. `base-converter-integration.test.ts` (11 tests)
Integration tests covering complete parsing flow:
- **Complete parsing pipeline** (6 tests): CREATE TABLE → INSERT → parsed values
- **Error scenarios** (5 tests): Missing tables, malformed SQL, validation

## Test Data

### `test-data.ts`
Contains real data samples extracted from the MySQL dump file, including:
- Real CREATE TABLE statements with complex constraints
- Multi-row INSERT statements with French characters
- Complex quoted strings and escape sequences
- Edge cases that have caused parsing failures

## Key Testing Utilities

### `testable-base-converter.ts`
- Concrete implementation of abstract BaseConverter class
- Exposes protected methods as public for testing
- Includes copied implementation of private `parseCSVValues` method

### `vitest.setup.ts`
- Test environment configuration
- Setup for migration system tests

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run tests with UI
pnpm test:ui
```

### 2. `splitValueRows(valuesContent: string): string[]`
**Priority: CRITICAL** - Multi-row INSERT statement splitting

**Test Coverage:**
- Single row parsing
- Multiple row parsing
- Rows with commas in quoted values
- Rows with quotes in values
- Nested parentheses handling
- Complex mixed content
- Empty input handling

**Real-world scenarios:**
- Multi-row batiment INSERT: `(1,'504A',NULL,1,1,NULL,0,0),(2,'511A',NULL,1,1,NULL,0,0)`

### 3. `extractValuesFromInsertStatement(insertStatement, sqlContent, tableName): Record<string, any>`
**Priority: CRITICAL** - Single INSERT statement value extraction

**Test Coverage:**
- Single row INSERT extraction
- NULL value handling
- Warning generation for column count mismatches
- Quoted values with special characters
- Error handling for malformed statements
- Real equipment category data

**Error scenarios:**
- "Warning: Not enough values (2) for columns (8)" - The exact error mentioned in requirements
- Too many values handling
- Missing CREATE TABLE statements

### 4. `extractInsertStatements(sqlContent: string, tableName: string): string[]`
**Priority: HIGH** - INSERT statement discovery

**Test Coverage:**
- Single INSERT statement extraction
- Multiple INSERT statements for same table
- Multi-row INSERT statements
- Non-existent table handling
- Different quote styles in table names
- Case-insensitive table names

### 5. `extractColumnNames(createTableStatement: string): string[]`
**Priority: HIGH** - Column name extraction from CREATE TABLE

**Test Coverage:**
- Batiment table columns
- Equipment category columns
- Complex column definitions
- Columns with/without backticks
- Mixed quote styles
- Constraints and keys handling
- Foreign key constraints
- Malformed statements

### 6. `extractCreateStatement(sqlContent: string, tableName: string): string`
**Priority: HIGH** - CREATE TABLE statement extraction

**Test Coverage:**
- Batiment and batiment_trad tables
- Non-existent table handling
- Different quote styles
- Case-insensitive table names
- Multi-line CREATE TABLE statements
- CREATE TABLE IF NOT EXISTS
- Empty SQL content

## Integration Tests

### Complete Flow Testing
Tests the entire parsing pipeline:
1. Extract CREATE TABLE statement
2. Extract column names
3. Extract INSERT statements
4. Split multi-row INSERT into individual rows
5. Parse individual row values

### Error Scenarios
- Column count mismatches (the "Not enough values" error)
- Too many values
- Missing CREATE TABLE statements
- Malformed INSERT statements
- Unmatched quotes
- Empty VALUES clauses

### Real-world Failure Scenarios
- Reproduces the exact "Not enough values (2) for columns (8)" error
- Tests BuildingMigrationConverter data patterns
- Tests problematic converters mentioned in requirements:
  - EquipmentI18nMigrationConverter
  - BuildingMigrationConverter
  - InfrastructureI18nMigrationConverter
  - InfrastructureMigrationConverter
  - UnitI18nMigrationConverter
  - VendorI18nMigrationConverter
  - OrganizationI18nMigrationConverter
  - EquipmentCategoryI18nMigrationConverter

## Test Data

### Real MySQL Dump Samples
All test data is extracted from the actual MySQL dump file:
`packages/db-schema/src/migration-scripts/data/mysqldump-cleaned.sql`

**Key data samples:**
- **BATIMENT_CREATE_TABLE** - Real CREATE TABLE for batiment with 8 columns
- **BATIMENT_INSERT_STATEMENT** - Multi-row INSERT with real building data
- **BATIMENT_TRAD_INSERT_STATEMENT** - French translations with quotes
- **CATEGORIE_EQUIPEMENT_*** - Equipment category data
- **COMPLEX_INSERT_WITH_QUOTES** - Mixed quotes and commas test case

### Edge Cases
- Empty values, NULL values
- Malformed SQL statements
- Unmatched quotes and parentheses
- Column count mismatches
- French characters and special symbols

## Running Tests

### Prerequisites
```bash
# Install dependencies
pnpm install
```

### Test Commands
```bash
# Run all tests
pnpm test

# Run tests once (CI mode)
pnpm test:run

# Run with coverage report
pnpm test:coverage

# Run in watch mode
pnpm test:watch

# Run with UI
pnpm test:ui
```

### Coverage Goals
- **Line Coverage**: >95% for all critical parsing methods
- **Branch Coverage**: >90% for error handling paths
- **Function Coverage**: 100% for all public/protected methods

## Expected Test Results

### Success Criteria
- All 50+ test cases should pass
- No console warnings during normal parsing operations
- Proper error handling for malformed input
- Accurate reproduction of real-world parsing scenarios

### Performance Expectations
- All tests should complete in <5 seconds
- Memory usage should remain stable during test runs
- No memory leaks in parsing operations

## Debugging Failed Tests

### Common Issues
1. **Quote handling errors** - Check CSV_TEST_CASES for proper escaping
2. **Column count mismatches** - Verify EXPECTED_COLUMNS data
3. **Integration failures** - Check FULL_SQL_SAMPLE structure
4. **Real data parsing** - Verify test data matches actual MySQL dump

### Debug Tools
- Use `console.log` in testable-base-converter.ts
- Enable Vitest UI for interactive debugging
- Check coverage reports for missed edge cases
- Use `--reporter=verbose` for detailed test output

## Maintenance

### Adding New Tests
1. Add test data to `test-data.ts`
2. Create test cases in appropriate test file
3. Update this README with new coverage
4. Verify integration with existing tests

### Updating Test Data
When the MySQL dump changes:
1. Update constants in `test-data.ts`
2. Verify EXPECTED_COLUMNS match new schema
3. Update EXPECTED_PARSED_VALUES for new data
4. Run full test suite to catch regressions

This test suite ensures the migration system's parsing reliability and prevents the critical bugs that were recently fixed from reoccurring.
