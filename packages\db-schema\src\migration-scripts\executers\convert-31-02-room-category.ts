import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { RoomCategoryMigrationConverter } from '../converters/31-02-convert-room-category-inserts';

async function convertRoomCategoryData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);
  try {
    // Initialize converter
    const converter = new RoomCategoryMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertRoomCategoryData().catch(console.error);
