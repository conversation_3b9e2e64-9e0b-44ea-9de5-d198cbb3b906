import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type { ParseError } from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DbVendorI18NDataSchema } from '../schemas';
import type { VendorTranslationFields } from '../types';

export const VendorTranslationData = Data.case<VendorTranslationFields>();

/**
 * VendorTranslation Value Object
 * 
 * Represents a single translation for a vendor in a specific locale.
 * This is an immutable value object that encapsulates vendor translation data
 * and provides type-safe access to translation fields.
 */
export class VendorTranslation {
  private constructor(private readonly data: VendorTranslationFields) {}

  /**
   * Creates a new VendorTranslation value object from raw data.
   * It validates the input against the VendorTranslationSchema.
   * This is the single, authoritative entry point for creating a valid VendorTranslation.
   */
  static create(input: unknown): Effect.Effect<VendorTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbVendorI18NDataSchema)(input),
      Effect.map(
        (validatedData) =>
          new VendorTranslation(VendorTranslationData(validatedData)),
      ),
    );
  }

  // --- Accessors ---

  getLocale() {
    return this.data.locale;
  }

  getName() {
    return this.data.name;
  }

  getWebsite() {
    return this.data.website;
  }

  getDescription() {
    return this.data.description;
  }

  getOtherNames() {
    return this.data.otherNames;
  }

  /**
   * Converts the value object to its raw data representation.
   * Useful for persistence or serialization.
   */
  toRaw(): VendorTranslationFields {
    return this.data;
  }

  /**
   * Gets the dataId (foreign key to the vendor)
   */
  getDataId(): string {
    return this.data.dataId;
  }

  /**
   * Gets the translation ID
   */
  getId(): string {
    return this.data.id;
  }
}
