import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type { ParseError } from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DbInstitutionI18NDataSchema } from '../schemas';
import type { InstitutionTranslationFields } from '../types';

export const InstitutionTranslationData = Data.case<InstitutionTranslationFields>();

/**
 * Represents a single, immutable, and validated translation for an institution.
 * Its responsibility is to hold the value and enforce its invariants.
 * It does not handle data transformation between layers.
 */
export class InstitutionTranslation {
    // The constructor is kept private to enforce creation via the validated factory method.
    private constructor(private readonly data: InstitutionTranslationFields) { }

    /**
     * Creates a new InstitutionTranslation value object from raw data.
     * It validates the input against the InstitutionTranslationSchema.
     * This is the single, authoritative entry point for creating a valid InstitutionTranslation.
     */
    static create(input: unknown): Effect.Effect<InstitutionTranslation, ParseError> {
        return pipe(
            Schema.decodeUnknown(DbInstitutionI18NDataSchema)(input),
            Effect.map(
                (validatedData) =>
                    new InstitutionTranslation(InstitutionTranslationData(validatedData)),
            ),
        );
    }

    // --- Accessors ---

    getLocale() {
        return this.data.locale;
    }

    getName() {
        return this.data.name;
    }

    getDescription() {
        return this.data.description;
    }

    getOtherNames() {
        return this.data.otherNames;
    }

    getAcronyms() {
        return this.data.acronyms;
    }

    /**
     * Converts the value object to its raw data representation.
     * Useful for persistence or serialization.
     */
    toRaw(): InstitutionTranslationFields {
        return this.data;
    }

    /**
     * Checks for equality with another InstitutionTranslation object based on its values.
     */
    equals(other: InstitutionTranslation): boolean {
        return (
            this.data.locale === other.data.locale &&
            this.data.name === other.data.name &&
            this.data.description === other.data.description &&
            this.data.otherNames === other.data.otherNames &&
            this.data.acronyms === other.data.acronyms
        );
    }
}