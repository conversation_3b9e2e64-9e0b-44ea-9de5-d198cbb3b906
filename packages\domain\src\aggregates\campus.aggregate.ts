import type { DbCampus } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import * as R from 'remeda';
import {
  CampusInvariantViolationError,
  type DuplicateLocaleError,
  TranslationValidationError,
} from '../errors';
import { DbCampusDataSchema } from '../schemas';
import type { CampusData } from '../types';
import {
  CampusTranslation,
  CampusTranslationCollection,
} from '../value-objects';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface CampusAggregateState
  extends Omit<DbCampus, 'createdAt' | 'updatedAt'> {
  createdAt?: string;
  updatedAt?: string;
  translations: CampusTranslationCollection;
}

const CampusAggregateState = Data.case<CampusAggregateState>();

// Campus Aggregate Root
export class Campus {
  private constructor(private readonly data: CampusAggregateState) { }

  /**
   * Factory for creating a NEW Campus aggregate.
   * Use this when creating a campus from user input for the first time.
   */
  static create(props: {
    sadId?: string | null;
    institutionId: string;
    modifiedBy: string;
    translations: CampusTranslationCollection;
  }): Effect.Effect<Campus, CampusInvariantViolationError> {
    const now = new Date().toISOString();
    const newCampus = new Campus(
      CampusAggregateState({
        id: DbUtils.cuid2(),
        isActive: true,
        // Set temporary timestamps to satisfy toRaw() validation
        createdAt: now as string,
        updatedAt: now as string,
        sadId: props.sadId ?? null,
        institutionId: props.institutionId,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
      }),
    );

    // Ensure the new campus is valid before returning it
    return pipe(
      newCampus.validateInvariants(),
      Effect.map(() => newCampus),
    );
  }

  /**
   * Factory for rehydrating a Campus aggregate from raw database data.
   */
  static fromDatabaseData(
    rawData: CampusData,
  ): Effect.Effect<
    Campus,
    | TranslationValidationError
    | ParseResult.ParseError
    | CampusInvariantViolationError
    | DuplicateLocaleError
  > {
    return pipe(
      Schema.decodeUnknown(DbCampusDataSchema)(rawData),
      Effect.mapError((parseError) =>
        TranslationValidationError.forField(
          'Campus',
          'database_validation',
          `Campus database data validation failed: ${parseError.message}`,
        ),
      ),
      Effect.flatMap((validated) =>
        pipe(
          validated.translations.map((t) => CampusTranslation.create(t)),
          Effect.all,
          Effect.flatMap(CampusTranslationCollection.create),
          Effect.map((translations) => ({ ...validated, translations })),
        ),
      ),
      Effect.map(
        (hydratedData) =>
          new Campus(
            CampusAggregateState({
              ...hydratedData,
              id: rawData.id, // ensure original id is preserved
            }),
          ),
      ),
      Effect.flatMap((campus) =>
        pipe(
          campus.validateInvariants(),
          Effect.map(() => campus),
        ),
      ),
    );
  }

  // --- Business Methods ---

  activate(
    modifiedBy: string,
  ): Effect.Effect<Campus, CampusInvariantViolationError> {
    if (this.data.isActive) {
      return Effect.fail(
        new CampusInvariantViolationError({
          campusId: this.data.id,
          reason: 'Cannot activate a campus that is already active',
        }),
      );
    }
    return Effect.succeed(
      new Campus(
        CampusAggregateState({
          ...this.data,
          isActive: true,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  deactivate(
    modifiedBy: string,
  ): Effect.Effect<Campus, CampusInvariantViolationError> {
    if (!this.data.isActive) {
      return Effect.fail(
        new CampusInvariantViolationError({
          campusId: this.data.id,
          reason: 'Cannot deactivate a campus that is already inactive',
        }),
      );
    }
    return Effect.succeed(
      new Campus(
        CampusAggregateState({
          ...this.data,
          isActive: false,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  /**
   * Updates the campus with new data following the "replace all" pattern.
   * All translations are replaced atomically.
   */
  update(props: {
    sadId?: string | null;
    institutionId: string;
    isActive: boolean;
    modifiedBy: string;
    translations: CampusTranslationCollection;
  }): Effect.Effect<Campus, CampusInvariantViolationError> {
    const newCampus = new Campus(
      CampusAggregateState({
        ...this.data,
        sadId: props.sadId ?? null,
        institutionId: props.institutionId,
        isActive: props.isActive,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
        // `updatedAt` is no longer set here; the database trigger will handle it.
      }),
    );

    return pipe(
      newCampus.validateInvariants(),
      Effect.map(() => newCampus),
    );
  }

  // --- Query Methods ---

  get id(): string {
    return this.data.id;
  }

  /**
   * Gets a composite view of all translatable fields, with locale fallbacks.
   * Delegates the complex logic to the CampusTranslationCollection.
   */
  getCompositeTranslations(locale: string, fallbackLocale = 'en') {
    return this.data.translations.getCompositeTranslations(
      locale,
      fallbackLocale,
    );
  }

  /**
   * Gets the entire collection of CampusTranslation value objects.
   * Useful for views that need to display all available translations, like an edit form.
   */
  getAllTranslations(): readonly CampusTranslation[] {
    return this.data.translations.getAll();
  }

  isActive(): boolean {
    return this.data.isActive ?? true;
  }

  toRaw(): Effect.Effect<CampusData, ParseResult.ParseError> {
    const SourceCampusSchema = Schema.extend(
      DbCampusDataSchema.omit('createdAt', 'updatedAt'),
      Schema.Struct({
        createdAt: Schema.optional(Schema.String),
        updatedAt: Schema.optional(Schema.String),
      }),
    );

    // 3. Create the failable transformer
    const transformer = Schema.transformOrFail(
      SourceCampusSchema,
      DbCampusDataSchema,
      {
        decode: (source, _, ast) => {
          if (source.createdAt && source.updatedAt) {
            // The cast is safe because we have checked the properties exist.
            return ParseResult.succeed(source as CampusData);
          }
          return ParseResult.fail(
            new ParseResult.Type(
              ast,
              source,
              'Cannot serialize for persistence because createdAt or updatedAt is missing',
            ),
          );
        },
        encode: (val, _options, ast) =>
          ParseResult.fail(
            new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
          ),
      },
    );

    const campusData = R.omit(this.data, ['translations']);

    return pipe(
      this.data.translations.toPersistenceArray(),
      Effect.flatMap((persistableTranslations) =>
        Schema.decode(transformer)({
          ...campusData,
          translations: persistableTranslations,
        }),
      ),
    );
  }

  // --- Invariant Validation ---

  private validateInvariants(): Effect.Effect<
    void,
    CampusInvariantViolationError
  > {
    // Check that campus has at least one translation
    if (this.data.translations.isEmpty()) {
      return Effect.fail(
        new CampusInvariantViolationError({
          campusId: this.data.id,
          reason: 'Campus must have at least one translation',
        }),
      );
    }

    // Check that at least one translation has a name
    const hasValidName = this.data.translations.getAll().some((t) => {
      const name = t.getName();
      return name !== null && name !== undefined && name.trim() !== '';
    });

    if (!hasValidName) {
      return Effect.fail(
        new CampusInvariantViolationError({
          campusId: this.data.id,
          reason: 'Campus must have at least one translation with a valid name',
        }),
      );
    }

    return Effect.succeed(void 0);
  }
}
