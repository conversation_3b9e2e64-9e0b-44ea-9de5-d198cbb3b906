'use client';
import type { RieServiceParams } from '@/constants/rie-client';
import { entitiesListOptions } from '@/hooks/directory/useGetEntitiesListOptions';
import { DirectoryEntity } from '@rie/domain/types';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useMemo } from 'react';

export const useGetEntitiesList = ({
  directoryEntity,
  pageParam = 0,
  params,
  queryParams = '',
}: {
  directoryEntity: DirectoryEntity;
  pageParam: number;
  params: RieServiceParams;
  queryParams?: string;
}) => {
  const [searchQuery] = useQueryState('q');
  const [sortValue] = useQueryState('sort');
  const searchParams = useSearchParams();

  const combinedQueryParams = useMemo(() => {
    const allParams = new URLSearchParams();

    // Add search query if it exists
    if (searchQuery) {
      allParams.append('q', searchQuery);
    }

    // Add sort value if it exists
    if (sortValue) {
      allParams.append('sort', sortValue);
    }

    // Add any explicit queryParams passed to the hook
    if (queryParams) {
      const customParams = new URLSearchParams(queryParams);
      for (const [key, value] of Array.from(customParams.entries())) {
        allParams.append(key, value);
      }
    }

    // Add all filter parameters from the URL
    for (const [key, value] of Array.from(searchParams.entries())) {
      if (key.startsWith('ff[')) {
        allParams.append(key, value);
      }
    }

    return allParams.toString();
  }, [searchQuery, sortValue, queryParams, searchParams]);

  return useQuery(
    entitiesListOptions(
      pageParam,
      params,
      combinedQueryParams,
      directoryEntity,
    ),
  );
};
