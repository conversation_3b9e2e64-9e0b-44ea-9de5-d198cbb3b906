import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InstitutionsRuntime } from '@/infrastructure/runtimes/institutions.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  InstitutionDetailResponseDtoSchema,
  InstitutionEditResponseDtoSchema,
  InstitutionFormRequestDtoSchema,
  InstitutionListItemDtoSchema,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { InstitutionsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  permissionForResource,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

export const getAllInstitutionsRoute = describeRoute({
  description: 'Lister toutes les institutions',
  operationId: 'getAllInstitutions',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(InstitutionListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Institutions retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const getInstitutionByIdRoute = describeRoute({
  description: 'Obtenir une institution par ID',
  operationId: 'getInstitutionById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue pour la ressource (detail ou edit)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              InstitutionDetailResponseDtoSchema,
              InstitutionEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Institution trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const createInstitutionRoute = describeRoute({
  description: 'Créer une institution',
  operationId: 'createInstitution',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InstitutionFormRequestDtoSchema),
        example: {
          institutionType: { value: 'bqmr7x60br0e6br1rw81uz4j', label: 'Université' },
          guidId: 'NEW_INSTITUTION_001',
          pseudonym: 'NUT',
          name: [
            { locale: 'en', value: 'New University of Technology' },
            { locale: 'fr', value: 'Nouvelle Université de Technologie' },
          ],
          description: [
            { locale: 'en', value: 'A leading institution in technology and innovation' },
            { locale: 'fr', value: 'Une institution leader en technologie et innovation' },
          ],
          otherNames: [],
          acronyms: [
            { locale: 'en', value: 'NUT' },
            { locale: 'fr', value: 'NUT' },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(InstitutionEditResponseDtoSchema),
        },
      },
      description: 'Institution créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - Le type n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const updateInstitutionRoute = describeRoute({
  description: 'Mettre à jour une institution',
  operationId: 'updateInstitution',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InstitutionFormRequestDtoSchema),
        example: {
          institutionType: { value: 'bqmr7x60br0e6br1rw81uz4j', label: 'Université' },
          guidId: 'UPDATED_INSTITUTION_001',
          pseudonym: 'UUT',
          name: [
            { locale: 'en', value: 'Updated University of Technology' },
            { locale: 'fr', value: 'Université de Technologie Mise à Jour' },
          ],
          description: [
            { locale: 'en', value: 'An updated leading institution in technology and innovation' },
            { locale: 'fr', value: 'Une institution leader mise à jour en technologie et innovation' },
          ],
          otherNames: [],
          acronyms: [
            { locale: 'en', value: 'UUT' },
            { locale: 'fr', value: 'UUT' },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InstitutionEditResponseDtoSchema),
        },
      },
      description: 'Institution mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const deleteInstitutionRoute = describeRoute({
  description: 'Supprimer une institution',
  operationId: 'deleteInstitution',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Institution supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

const institutionsRoute = new Hono<{
  Variables: HonoVariables;
}>();

institutionsRoute.get(
  '/',
  getAllInstitutionsRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    // For select view, skip permission check (like controlled-lists)
    // For list view, require institution:read permission
    if (view === 'select') {
      const program = Effect.gen(function* () {
        const institutionService = yield* InstitutionsServiceLive;
        return yield* institutionService.getAllInstitutions({
          locale,
          fallbackLocale,
          view,
        });
      });

      const result = await InstitutionsRuntime.runPromiseExit(program);
      const errorResponse = handleEffectError(ctx, result);
      if (errorResponse) {
        console.error('[Route][Institutions][GET /] errorResponse returned');
        return errorResponse;
      }
      if (Exit.isSuccess(result)) {
        console.log('[Route][Institutions][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
        return ctx.json(result.value);
      }
      return ctx.json({ error: 'An error occurred' }, 500);
    }

    // For list view, require permission and authentication
    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.getAllInstitutions({
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('institution:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    console.log('[Route][Institutions][GET /] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Institutions][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Institutions][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.get(
  '/:id',
  getInstitutionByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: ResourceViewSchema,
    }),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');
    const user = ctx.get('user');
    const session = ctx.get('session');

    // For edit view, require authentication and permissions
    if (view === 'edit') {
      if (!user || !session) {
        return ctx.json({ error: 'Unauthorized' }, 401);
      }

      const program = Effect.gen(function* () {
        const institutionService = yield* InstitutionsServiceLive;
        return yield* institutionService.getInstitutionById({
          id,
          locale,
          fallbackLocale,
          view,
        });
      }).pipe(
        withPolicy(permissionForResource('institution', 'update', id)),
        Effect.provideService(CurrentUser, {
          sessionId: session.id,
          userId: UserIdSchema.make(user.id),
        }),
      );

      const result = await InstitutionsRuntime.runPromiseExit(program);
      const errorResponse = handleEffectError(ctx, result);
      if (errorResponse) {
        return errorResponse;
      }
      if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
      }
      return ctx.json({ error: 'An error occurred' }, 500);
    }

    // For detail view, allow public access (or implement different permission logic)
    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.getInstitutionById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    });

    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.post(
  '/',
  createInstitutionRoute,
  validator('json', InstitutionFormRequestDtoSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.createInstitution({
        institutionDto: body,
        userId: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('institution:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.put(
  '/:id',
  updateInstitutionRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', InstitutionFormRequestDtoSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.updateInstitution({
        id,
        institutionDto: body,
        userId: user.id,
      });
    }).pipe(
      withPolicy(permissionForResource('institution', 'update', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.delete(
  '/:id',
  deleteInstitutionRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.deleteInstitution(id);
    }).pipe(
      withPolicy(permissionForResource('institution', 'delete', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Institution deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { institutionsRoute };

