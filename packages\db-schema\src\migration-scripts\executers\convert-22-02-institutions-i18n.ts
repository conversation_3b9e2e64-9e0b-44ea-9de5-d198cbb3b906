import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { OrganizationI18nMigrationConverter } from '../converters/22-02-convert-institution-i18n-inserts';

async function convertOrganizationI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new OrganizationI18nMigrationConverter();
    // Convert the file
    await converter.convertFile();
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertOrganizationI18nData().catch(console.error);
