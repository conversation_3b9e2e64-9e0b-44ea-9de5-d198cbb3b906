import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { DirectoryListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { useQuery } from '@tanstack/react-query';

export const useGetDirectoryList = <
  Key extends DirectoryListKey,
  locale extends SupportedLocale,
  View extends CollectionViewParamType['view'],
>({
  directoryListKey,
  locale,
  view,
}: {
  directoryListKey: Key;
  locale: locale;
  view: View;
}) => {
  return useQuery(getDirectoryListOptions({ directoryListKey, locale, view }));
};

interface GetDirectoryByIdParams<Key extends DirectoryListKey> {
  directoryListKey: Key;
  id: string;
  view: ResourceViewType;
}

export const useGetDirectoryById = <
  Key extends DirectoryListKey,
  View extends ResourceViewType,
>({
  directoryListKey,
  id,
  view,
}: GetDirectoryByIdParams<Key> & { view: View }) => {
  return useQuery(getDirectoryByIdOptions({ directoryListKey, id, view }));
};
