import * as Schema from 'effect/Schema';

// --- UserId ---
export const UserIdSchema = Schema.String.pipe(
  Schema.brand('UserId', {
    message: () => 'Invalid User ID format',
  }),
);
export type UserId = Schema.Schema.Type<typeof UserIdSchema>;

// Define all other IDs for directory entities
// --- BuildingId ---
export const BuildingIdSchema = Schema.String.pipe(
  Schema.brand('BuildingId', {
    message: () => 'Invalid Building ID format',
  }),
);
export type BuildingId = Schema.Schema.Type<typeof BuildingIdSchema>;

// --- CampusId ---
export const CampusIdSchema = Schema.String.pipe(
  Schema.brand('CampusId', {
    message: () => 'Invalid Campus ID format',
  }),
);
export type CampusId = Schema.Schema.Type<typeof CampusIdSchema>;

// --- InstitutionId ---
export const InstitutionIdSchema = Schema.String.pipe(
  Schema.brand('InstitutionId', {
    message: () => 'Invalid Institution ID format',
  }),
);
export type InstitutionId = Schema.Schema.Type<typeof InstitutionIdSchema>;

// --- FundingProjectId ---
export const FundingProjectIdSchema = Schema.String.pipe(
  Schema.brand('FundingProjectId', {
    message: () => 'Invalid Funding Project ID format',
  }),
);
export type FundingProjectId = Schema.Schema.Type<
  typeof FundingProjectIdSchema
>;

// --- VendorId ---
export const VendorIdSchema = Schema.String.pipe(
  Schema.brand('VendorId', {
    message: () => 'Invalid Vendor ID format',
  }),
);
export type VendorId = Schema.Schema.Type<typeof VendorIdSchema>;

// --- PersonId ---
export const PersonIdSchema = Schema.String.pipe(
  Schema.brand('PersonId', {
    message: () => 'Invalid Person ID format',
  }),
);
export type PersonId = Schema.Schema.Type<typeof PersonIdSchema>;

// --- RoomId ---
export const RoomIdSchema = Schema.String.pipe(
  Schema.brand('RoomId', {
    message: () => 'Invalid Room ID format',
  }),
);
export type RoomId = Schema.Schema.Type<typeof RoomIdSchema>;

// --- UnitId ---
export const UnitIdSchema = Schema.String.pipe(
  Schema.brand('UnitId', {
    message: () => 'Invalid Unit ID format',
  }),
);
export type UnitId = Schema.Schema.Type<typeof UnitIdSchema>;

// --- InfrastructureId ---
export const InfrastructureIdSchema = Schema.String.pipe(
  Schema.brand('InfrastructureId', {
    message: () => 'Invalid Infrastructure ID format',
  }),
);
export type InfrastructureId = Schema.Schema.Type<
  typeof InfrastructureIdSchema
>;

// --- EquipmentId ---
export const EquipmentIdSchema = Schema.String.pipe(
  Schema.brand('EquipmentId', {
    message: () => 'Invalid Equipment ID format',
  }),
);
export type EquipmentId = Schema.Schema.Type<typeof EquipmentIdSchema>;

// --- ServiceOfferId ---
export const ServiceOfferIdSchema = Schema.String.pipe(
  Schema.brand('ServiceOfferId', {
    message: () => 'Invalid Service Offer ID format',
  }),
);
export type ServiceOfferId = Schema.Schema.Type<typeof ServiceOfferIdSchema>;
