'use client';

import { PermissionsGroupForm } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form';
import type { PermissionsGroupFormSchema } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form.schema';
import { useUpdatePermissionsGroups } from '@/hooks/admin/permissions/permissions-groups.hook';

interface EditPermissionProps {
  id: string;
  initialData: PermissionsGroupFormSchema;
}

export const EditPermissionGroup = ({
  id,
  initialData,
}: EditPermissionProps) => {
  const { mutate, status } = useUpdatePermissionsGroups();
  const handleOnSubmit = (data: PermissionsGroupFormSchema) => {
    mutate({ payload: data, id });
  };

  return (
    <PermissionsGroupForm
      onSubmitAction={handleOnSubmit}
      status={status}
      initialData={initialData}
    />
  );
};
