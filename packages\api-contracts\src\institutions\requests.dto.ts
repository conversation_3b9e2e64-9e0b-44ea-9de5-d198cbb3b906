import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    SelectOptionDtoSchema,
} from '../common';

/**
 * Describes the entire request body for creating or updating an institution.
 * This is the shape of the data the client will send to the API.
 **/

export const InstitutionFormRequestDtoSchema = Schema.Struct({
    id: Schema.optional(Schema.String),
    isActive: Schema.optional(Schema.Boolean),
    guidId: Schema.optional(Schema.NullOr(Schema.String)),
    institutionType: SelectOptionDtoSchema,
    pseudonym: Schema.String,
    name: Schema.Array(OptionalLocalizedFieldDtoSchema),
    description: Schema.Array(OptionalLocalizedFieldDtoSchema),
    otherNames: Schema.Array(OptionalLocalizedFieldDtoSchema),
    acronyms: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type InstitutionFormRequestDTO = Schema.Schema.Type<
    typeof InstitutionFormRequestDtoSchema
>;