import type { personFormSections } from '@/constants/directory/person';
import type { Affiliation, PersonFull } from '@/types/controlled-list';
import type { IdType } from '@/types/directory/project';
import type { Territory } from '@/types/infrastructure';

export type Person = PersonFull;

export type PersonFormSectionKey = keyof typeof personFormSections;

export type PersonPostPayload = {
  affiliations?: Affiliation[];
  emails: {
    address: string;
  }[];
  emplacements?: {
    estLocal?: boolean;
    local: IdType | null;
    territory: null | Territory;
  } | null;
  familyName: string;
  givenName: string;
  telephones?: {
    number: string;
  }[];
};
