import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import {
    createDirectory,
    deleteDirectory,
    updateDirectory,
} from '@/services/directory/directory-list.service';
import type { BuildingEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateBuilding = () => {
    const router = useRouter();
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation<BuildingEdit, Error, BuildingFormSchema>({
        mutationFn: async (payload) =>
            (await createDirectory({
                directoryListKey: 'building',
                payload,
            })) as unknown as BuildingEdit,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['directory', 'building'],
            });
            toast({
                title: 'Success',
                description: 'Building created successfully',
                variant: 'success',
            });
            router.push('/bottin/batiments');
        },
    });
};

export const useUpdateBuilding = () => {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation<
        BuildingEdit,
        Error,
        { id: string; payload: BuildingFormSchema }
    >({
        mutationFn: async ({ id, payload }) =>
            (await updateDirectory({
                directoryListKey: 'building',
                id,
                payload,
            })) as unknown as BuildingEdit,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['directory', 'building'],
            });
            toast({
                title: 'Success',
                description: 'Building updated successfully',
                variant: 'success',
            });
        },
    });
};

export const useDeleteBuilding = () => {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    return useMutation<void, Error, string>({
        mutationFn: async (id: string) => {
            await deleteDirectory({ directoryListKey: 'building', id });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['directory', 'building'],
            });
            toast({
                title: 'Success',
                description: 'Building deleted successfully',
                variant: 'success',
            });
        },
    });
};