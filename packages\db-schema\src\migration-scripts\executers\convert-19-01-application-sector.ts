import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { ApplicationSectorMigrationConverter } from '../converters/19-01-convert-application-sector-inserts';

async function convertApplicationSectorData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new ApplicationSectorMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertApplicationSectorData().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
