import type {
  DbVendorDataSchema,
  DbVendorI18NDataSchema,
  DbVendorI18NInputSchema,
  VendorDetailSchema,
  VendorEditSchema,
  VendorFormInputSchema,
  VendorInputSchema,
  VendorListSchema,
  VendorSchema,
  VendorSelectSchema,
} from '../schemas';

import type * as Schema from 'effect/Schema';

export type Vendor = Schema.Schema.Type<typeof VendorSchema>;
export type VendorInput = Schema.Schema.Type<typeof VendorInputSchema>;
export type VendorFormInput = Schema.Schema.Type<typeof VendorFormInputSchema>;
export type VendorList = Schema.Schema.Type<typeof VendorListSchema>;
export type VendorSelect = Schema.Schema.Type<typeof VendorSelectSchema>;
export type VendorEdit = Schema.Schema.Type<typeof VendorEditSchema>;
export type VendorDetail = Schema.Schema.Type<typeof VendorDetailSchema>;
export type VendorData = Schema.Schema.Type<typeof DbVendorDataSchema>;
export type VendorTranslationFields = Schema.Schema.Type<
  typeof DbVendorI18NDataSchema
>;
export type VendorI18NInput = Schema.Schema.Type<
  typeof DbVendorI18NInputSchema
>;
