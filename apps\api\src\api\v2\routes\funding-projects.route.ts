import { handleEffectError } from '@/api/v2/utils/error-handler';
import { FundingProjectsRuntime } from '@/infrastructure/runtimes/funding-projects.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  FundingProjectInputSchema,
  FundingProjectSchema,
  ResourceIdSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { FundingProjectsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllFundingProjectsRoute = describeRoute({
  description: 'Lister tous les projets de financement',
  operationId: 'getAllFundingProjects',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(FundingProjectSchema)),
        },
      },
      description: 'Projets de financement retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Funding Projects'],
});

export const getFundingProjectByIdRoute = describeRoute({
  description: 'Obtenir un projet de financement par ID',
  operationId: 'getFundingProjectById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du projet de financement (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(FundingProjectSchema),
        },
      },
      description: 'Projet de financement trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Projet de financement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Funding Projects'],
});

export const createFundingProjectRoute = describeRoute({
  description: 'Créer un projet de financement',
  operationId: 'createFundingProject',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(FundingProjectInputSchema),
        example: {
          holderId: 'w4ku5n46mdvkv5bjuuml2j3w',
          typeId: 'bqmr7x60br0e6br1rw81uz4j',
          fciId: 'FCI_NEW_001',
          synchroId: 'SYNC_NEW_001',
          obtainingYear: 2024,
          endDate: '2026-12-31',
          translations: [
            {
              locale: 'en',
              name: 'New AI Research Project',
              description: 'New advanced AI research initiative',
              otherNames: 'New AI Project',
              acronyms: 'NARP',
            },
            {
              locale: 'fr',
              name: 'Nouveau Projet de Recherche IA',
              description: 'Nouvelle initiative de recherche avancée en IA',
              otherNames: 'Nouveau Projet IA',
              acronyms: 'NPRI',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(FundingProjectSchema),
        },
      },
      description: 'Projet de financement créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Clé étrangère non trouvée - La personne ou le type n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Funding Projects'],
});

export const updateFundingProjectRoute = describeRoute({
  description: 'Mettre à jour un projet de financement',
  operationId: 'updateFundingProject',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du projet de financement (format CUID)',
      example: 'fp123abc456def789ghi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(FundingProjectInputSchema),
        example: {
          holderId: 'w4ku5n46mdvkv5bjuuml2j3w',
          typeId: 'bqmr7x60br0e6br1rw81uz4j',
          fciId: 'FCI_UPDATED_001',
          synchroId: 'SYNC_UPDATED_001',
          obtainingYear: 2025,
          endDate: '2027-12-31',
          translations: [
            {
              locale: 'en',
              name: 'Updated AI Research Project',
              description: 'Updated advanced AI research initiative',
              otherNames: 'Updated AI Project',
              acronyms: 'UARP',
            },
            {
              locale: 'fr',
              name: 'Projet de Recherche IA Mis à Jour',
              description: 'Initiative de recherche avancée en IA mise à jour',
              otherNames: 'Projet IA Mis à Jour',
              acronyms: 'PRIMAJ',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(FundingProjectSchema),
        },
      },
      description: 'Projet de financement mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        'Projet de financement non trouvé ou clé étrangère non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Funding Projects'],
});

export const deleteFundingProjectRoute = describeRoute({
  description: 'Supprimer un projet de financement',
  operationId: 'deleteFundingProject',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du projet de financement (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Projet de financement supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Projet de financement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Funding Projects'],
});

const fundingProjectsRoute = new Hono<{
  Variables: HonoVariables;
}>();

fundingProjectsRoute.get(
  '/',
  getAllFundingProjectsRoute,
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    // Get view parameter from query (default to 'list')
    const view = ctx.req.query('view') as 'list' | 'select' | 'grid' || 'list';
    const locale = ctx.req.query('locale') || 'en';
    const fallbackLocale = ctx.req.query('fallbackLocale') || 'en';

    // Allow both authenticated and unauthenticated access like infrastructures/equipments
    const program = Effect.gen(function* () {
      const fundingProjectService = yield* FundingProjectsServiceLive;
      return yield* fundingProjectService.getAllFundingProjects({
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('fundingProject:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  });

fundingProjectsRoute.get(
  '/:id',
  getFundingProjectByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    // Get view parameter from query (default to 'detail')
    const view = ctx.req.query('view') as 'detail' | 'edit' || 'detail';
    const locale = ctx.req.query('locale') || 'en';
    const fallbackLocale = ctx.req.query('fallbackLocale') || 'en';

    // Allow both authenticated and unauthenticated access like infrastructures/equipments
    const program = Effect.gen(function* () {
      const fundingProjectService = yield* FundingProjectsServiceLive;
      return yield* fundingProjectService.getFundingProjectById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('fundingProject:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);
fundingProjectsRoute.post(
  '/',
  createFundingProjectRoute,
  validator('json', FundingProjectInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const fundingProjectService = yield* FundingProjectsServiceLive;
      return yield* fundingProjectService.createFundingProject({
        ...body,
        modifiedBy: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('fundingProject:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await FundingProjectsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

fundingProjectsRoute.put(
  '/:id',
  updateFundingProjectRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', FundingProjectInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const fundingProjectService = yield* FundingProjectsServiceLive;
      return yield* fundingProjectService.updateFundingProject({
        id,
        project: {
          ...body,
          modifiedBy: user.id,
        },
      });
    }).pipe(
      withPolicy(checkPermission('fundingProject:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

fundingProjectsRoute.delete(
  '/:id',
  deleteFundingProjectRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const fundingProjectService = yield* FundingProjectsServiceLive;
      return yield* fundingProjectService.deleteFundingProject(id);
    }).pipe(
      withPolicy(checkPermission('fundingProject:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await FundingProjectsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Funding project deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { fundingProjectsRoute };

