import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { ControlledListKey } from '@/types/common';
import type { ControlledListSelectResult, ControlledListsResponse } from '@/types/controlled-list/controlled-list.type';
import { CONTROLLED_LIST_ENTITIES } from '@/types/controlled-list/controlled-list.type';

// Main entities that have their own endpoints (not controlled lists)
const MAIN_ENTITIES = {
    building: 'v2/buildings',
    campus: 'v2/campuses',
    organisation: 'v2/institutions',
    person: 'v2/people',
    unit: 'v2/units',
    establishment: 'v2/institutions',
    local: 'v2/rooms',
    supplier: 'v2/vendors',
} as const;

export async function getControlledListSelect<Key extends ControlledListKey>({
    controlledListKey,
}: {
    controlledListKey: Key;
}): Promise<ControlledListSelectResult> {
    const entityName = CONTROLLED_LIST_ENTITIES[controlledListKey];
    if (!entityName) {
        throw new Error(`No entity mapping found for controlled list: ${controlledListKey}`);
    }

    try {
        const client = await getApiClient();

        // Check if this is a main entity with its own endpoint
        const mainEntityEndpoint = MAIN_ENTITIES[controlledListKey as keyof typeof MAIN_ENTITIES];
        if (mainEntityEndpoint) {
            // Call the entity's own endpoint with view=select
            const result = await client
                .get(`${mainEntityEndpoint}?view=select&locale=fr&fallbackLocale=fr`)
                .json<ControlledListSelectResult>();
            return result;
        }

        // Use controlled-lists endpoint for actual controlled lists
        // Note: API expects entities as comma-separated values for array parsing
        const result = await client
            .get(`v2/controlled-lists?entities=${entityName}&view=select&limit=-1`)
            .json<ControlledListsResponse>();

        // La réponse contient un objet avec les entités comme clés
        return result[entityName] || [];
    } catch (error) {
        if (error instanceof APIV2Error) throw error;
        throw new Error(`Failed to fetch controlled list: ${controlledListKey}`);
    }
}

// Fonction pour récupérer plusieurs listes contrôlées en une seule requête
export async function getMultipleControlledListsSelect(
    controlledListKeys: ControlledListKey[]
): Promise<Record<string, ControlledListSelectResult>> {
    try {
        const client = await getApiClient();
        const results: Record<string, ControlledListSelectResult> = {};

        // Separate main entities from controlled lists
        const mainEntityKeys = controlledListKeys.filter(key => key in MAIN_ENTITIES);
        const controlledListKeys_ = controlledListKeys.filter(key => !(key in MAIN_ENTITIES));

        // Fetch main entities individually
        const mainEntityPromises = mainEntityKeys.map(async (key) => {
            const endpoint = MAIN_ENTITIES[key as keyof typeof MAIN_ENTITIES];
            const entityName = CONTROLLED_LIST_ENTITIES[key];
            if (endpoint && entityName) {
                const result = await client
                    .get(`${endpoint}?view=select&locale=fr&fallbackLocale=fr`)
                    .json<ControlledListSelectResult>();
                return { [entityName]: result };
            }
            return {};
        });

        // Fetch controlled lists in bulk
        let controlledListResult: ControlledListsResponse = {};
        if (controlledListKeys_.length > 0) {
            const validEntities = controlledListKeys_
                .map(key => CONTROLLED_LIST_ENTITIES[key])
                .filter((entity): entity is string => !!entity);

            if (validEntities.length > 0) {
                const entitiesParam = validEntities.join(',');
                controlledListResult = await client
                    .get(`v2/controlled-lists?entities=${entitiesParam}&view=select&limit=-1`)
                    .json<ControlledListsResponse>();
            }
        }

        // Combine results
        const mainEntityResults = await Promise.all(mainEntityPromises);
        for (const mainResult of mainEntityResults) {
            Object.assign(results, mainResult);
        }
        Object.assign(results, controlledListResult);

        return results;
    } catch (error) {
        if (error instanceof APIV2Error) throw error;
        throw new Error('Failed to fetch multiple controlled lists');
    }
}
