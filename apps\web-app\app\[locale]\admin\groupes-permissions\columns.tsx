'use client';

import { <PERSON>er<PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { TableActionButtons } from '@/components/table-action-buttons/table-action-buttons';
import dayjs from '@/lib/dayjs';
import type { SupportedLocale } from '@/types/locale';
import type { PermissionsGroupList } from '@rie/domain/types';
import type { ColumnDef } from '@tanstack/react-table';

const namespace = 'permissionsGroups';
export const permissionsGroupsColumns = (
  locale: SupportedLocale,
): ColumnDef<PermissionsGroupList>[] => {
  return [
    {
      accessorKey: 'name',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'name',
      size: 280,
    },
    {
      accessorKey: 'description',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'description',
      size: 280,
    },
    {
      accessorKey: 'createdAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'createdAt',
      maxSize: 100,
    },
    {
      accessorKey: 'updatedAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'updatedAt',
      maxSize: 100,
    },
    {
      accessorKey: 'groupPermissionActions',
      cell: ({ row }) => (
        <TableActionButtons
          id={row.original.id}
          name={row.original.name}
          pathname="/admin/groupes-permissions/[id]/editer"
        />
      ),
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={namespace}
          translationKey="table.columns.actions"
        />
      ),
      id: 'groupPermissionActions',
      size: 80,
      maxSize: 80,
    },
  ];
};
