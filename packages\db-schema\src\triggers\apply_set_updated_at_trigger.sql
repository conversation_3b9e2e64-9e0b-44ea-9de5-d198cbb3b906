-- This is a script to be run manually or after migrations.
DO $$
DECLARE
    t_name TEXT;
BEGIN
-- Loop through all tables in the 'public' schema that have an 'updated_at' column
    FOR t_name IN
        SELECT table_name
        FROM information_schema.columns
        WHERE table_schema = 'public' AND column_name = 'updated_at'
    LOOP
        -- Dynamically create the DROP and CREATE TRIGGER statements
        EXECUTE format('DROP TRIGGER IF EXISTS set_%s_updated_at ON %I;', t_name, t_name);
        EXECUTE format('CREATE TRIGGER set_%s_updated_at
                        BEFORE UPDATE ON %I
                        FOR EACH ROW
                        EXECUTE FUNCTION trigger_set_timestamp();', t_name, t_name);
    END LOOP;
END;
$$LANGUAGE plpgsql;