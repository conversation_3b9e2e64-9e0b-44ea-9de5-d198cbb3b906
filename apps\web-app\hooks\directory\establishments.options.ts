import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllEstablishmentsOptions = <
  View extends CollectionViewParamType['view'],
>({
  locale,
  view,
}: CollectionOptionsParams<View>) =>
  getDirectoryListOptions({
    directoryListKey: 'establishment' as const,
    locale,
    view,
  });

export const getEstablishmentByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getDirectoryByIdOptions({
    directoryListKey: 'establishment',
    id,
    view,
  } as const);
