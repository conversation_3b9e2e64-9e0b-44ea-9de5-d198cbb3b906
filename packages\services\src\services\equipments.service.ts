import { EquipmentForbiddenError } from '@rie/domain/errors';
import { EquipmentNotFoundError } from '@rie/domain/errors';
import { isEquipmentPubliclyAccessible } from '@rie/domain/services';
import type { EquipmentInput } from '@rie/domain/types';
import { EquipmentsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import { UserPermissionsServiceLive } from './user-permissions.service';

export class EquipmentsServiceLive extends Effect.Service<EquipmentsServiceLive>()(
  'EquipmentsServiceLive',
  {
    dependencies: [
      EquipmentsRepositoryLive.Default,
      UserPermissionsServiceLive.Default,
    ],
    effect: Effect.gen(function* () {
      const getAllEquipments = (userId?: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const equipmentsRepository = yield* EquipmentsRepositoryLive;

          // If no user is provided (unauthenticated), use optimized database-level filtering
          // for public equipments only
          const equipments = userId
            ? yield* equipmentsRepository.findAllActiveEquipments()
            : yield* equipmentsRepository.findAllActivePublicEquipments();

          const t1 = performance.now();
          console.log(
            `Call to getAllEquipments (authenticated) took ${t1 - t0} ms.`,
          );
          return equipments;
        });
      };

      const getEquipmentById = (id: string, userId?: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const equipment = yield* repo.findEquipmentById(id);

          if (!equipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }

          // If no user is provided (unauthenticated), check infrastructure visibility
          if (!userId) {
            if (!isEquipmentPubliclyAccessible(equipment)) {
              return yield* Effect.fail(
                new EquipmentForbiddenError({
                  id,
                  reason: 'Equipment is not publicly accessible',
                }),
              );
            }
          }

          const t1 = performance.now();
          console.log(
            `Call to getEquipmentById (authenticated) took ${t1 - t0} ms.`,
          );
          return equipment;
        });
      };

      const createEquipment = (equipment: EquipmentInput) =>
        Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          return yield* repo.createEquipment({ equipment });
        });

      const updateEquipment = ({
        id,
        equipment,
      }: { equipment: EquipmentInput; id: string }) => {
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const existingEquipment = yield* repo.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }
          return yield* repo.updateEquipment({
            equipmentId: id,
            equipment,
          });
        });
      };

      const deleteEquipment = (id: string) => {
        return Effect.gen(function* () {
          const repo = yield* EquipmentsRepositoryLive;
          const existingEquipment = yield* repo.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(new EquipmentNotFoundError({ id }));
          }
          const result = yield* repo.deleteEquipment(id);
          return result.length > 0;
        });
      };

      return {
        getAllEquipments,
        getEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } as const;
    }),
  },
) {}
