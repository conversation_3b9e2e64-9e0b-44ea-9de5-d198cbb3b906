import { TranslationValidationError } from '../errors';
import type {
  FieldValidator,
  UnitTranslationFields,
  ValidationErrors,
} from '../types';

export type RequiredFieldName<T> = {
  [K in keyof T]: T[K] extends string
    ? null extends T[K]
      ? never
      : undefined extends T[K]
        ? never
        : K
    : never;
}[keyof T] extends infer U
  ? U extends string
    ? U
    : never
  : never;

// Extract field names that are optional (string | null | undefined)
export type OptionalFieldName<T> = {
  [K in keyof T]: T[K] extends string | null | undefined
    ? null extends T[K]
      ? K
      : undefined extends T[K]
        ? K
        : never
    : never;
}[keyof T] extends infer U
  ? U extends string
    ? U
    : never
  : never;

export const createRequiredStringValidator = <
  T,
  K extends RequiredFieldName<T>,
>(
  customMessage?: string,
): FieldValidator<T, K> => {
  return (value: T[K], fieldName: K): ValidationErrors<T> => {
    const stringValue = value as string;

    if (!stringValue || stringValue.trim().length === 0) {
      return {
        [fieldName]: customMessage || `${fieldName as string} is required`,
      } as ValidationErrors<T>;
    }

    return {};
  };
};

export const createMaxLengthFieldValidator = <T, K extends keyof T>(
  maxLength: number,
  customMessage?: string,
): FieldValidator<T, K> => {
  return (value: T[K], fieldName: K): ValidationErrors => {
    const stringValue = value as string;

    if (stringValue && stringValue.trim().length > maxLength) {
      return {
        [fieldName]:
          customMessage ||
          `${fieldName as string} cannot be longer than ${maxLength} characters`,
      } as ValidationErrors;
    }

    return {};
  };
};

export const createOptionalStringValidator = <
  T,
  K extends OptionalFieldName<T>,
>(): FieldValidator<T, K> => {
  return (value: T[K], _fieldName: K): ValidationErrors => {
    // Optional fields pass validation for any "empty-ish" value
    if (
      value === null ||
      value === undefined ||
      (typeof value === 'string' && value.trim().length === 0)
    ) {
      return {};
    }

    return {};
  };
};

// Helper function to convert ValidationErrors to TranslationValidationError
export const createTranslationValidationError = <T>(
  entityType: string,
  validationErrors: ValidationErrors<T>,
  primaryMessage?: string,
): TranslationValidationError => {
  const errorRecord = validationErrors as Record<string, string>;
  return TranslationValidationError.fromValidationErrors(
    entityType,
    errorRecord,
    primaryMessage,
  );
};

// Validator combinators
/**
 * Combines multiple validators using AND logic - all validators must pass for validation to succeed.
 *
 * **Execution Flow:**
 * - Runs validators sequentially in array order
 * - Short-circuits on first failure (remaining validators are not executed)
 * - Continues only if current validator returns empty object `{}`
 *
 * **Success Condition:**
 * - ALL validators must return `{}` (empty object)
 *
 * **Failure Condition:**
 * - ANY validator returns an object with error properties
 * - Returns the FIRST error encountered
 *
 * **Use Cases:**
 * - Field validation where multiple constraints must be satisfied
 * - Required field + max length validation
 * - Optional field + format validation (when value is present)
 *
 * @template T - The entity type being validated
 * @template K - The field name being validated (must be a key of T)
 *
 * @param validators - Array of validators to combine with AND logic
 * @returns A combined validator that passes only when all individual validators pass
 *
 * @example
 * ```typescript
 * // Validate required field with max length
 * const validator = all([
 *   createRequiredStringValidator<User, 'name'>(),
 *   createMaxLengthFieldValidator<User, 'name'>(50)
 * ]);
 *
 * validator('John', 'name');        // {} (pass - both validators succeed)
 * validator('', 'name');           // {name: "name is required"} (fail - first validator fails)
 * validator('Very long name...', 'name'); // {name: "name cannot be longer than 50 characters"} (fail - second validator fails)
 * ```
 */
export const all = <T, K extends keyof T>(
  validators: FieldValidator<T, K>[],
): FieldValidator<T, K> => {
  return (value: T[K], fieldName: K): ValidationErrors => {
    for (const validator of validators) {
      const errors = validator(value, fieldName);
      if (Object.keys(errors).length > 0) {
        return errors; // Return first error encountered
      }
    }
    return {};
  };
};

/**
 * Combines multiple validators using OR logic - any validator can pass for validation to succeed.
 *
 * **Execution Flow:**
 * - Runs validators sequentially in array order
 * - Short-circuits on first success (remaining validators are not executed)
 * - Continues only if current validator returns an object with error properties
 *
 * **Success Condition:**
 * - ANY validator returns `{}` (empty object)
 *
 * **Failure Condition:**
 * - ALL validators return objects with error properties
 * - Returns the LAST error encountered (from the final validator in the array)
 *
 * **Use Cases:**
 * - Alternative validation paths (either email OR phone required)
 * - Multiple acceptable formats for the same field
 * - Fallback validation strategies
 *
 * @template T - The entity type being validated
 * @template K - The field name being validated (must be a key of T)
 *
 * @param validators - Array of validators to combine with OR logic
 * @returns A combined validator that passes when any individual validator passes
 *
 * @example
 * ```typescript
 * // Accept either email or phone number
 * const validator = or([
 *   createEmailValidator<User, 'contact'>(),
 *   createPhoneValidator<User, 'contact'>()
 * ]);
 *
 * validator('<EMAIL>', 'contact'); // {} (pass - email validator succeeds)
 * validator('555-1234', 'contact');        // {} (pass - phone validator succeeds)
 * validator('invalid', 'contact');         // {contact: "Invalid phone format"} (fail - returns last error)
 * ```
 *
 * @warning Be careful when using `or` with validators that always pass (like `createOptionalStringValidator`).
 * The first validator will always succeed, making subsequent validators unreachable.
 */
export const or = <T, K extends keyof T>(
  validators: FieldValidator<T, K>[],
): FieldValidator<T, K> => {
  return (value: T[K], fieldName: K): ValidationErrors => {
    for (const validator of validators) {
      const errors = validator(value, fieldName);
      if (Object.keys(errors).length === 0) {
        return {}; // Short-circuit: first validator that passes
      }
    }

    // If all validators failed, return the last error
    return validators[validators.length - 1]?.(value, fieldName) || {};
  };
};

// Usage examples and factory functions
export const createRequiredFieldValidator = <T, K extends RequiredFieldName<T>>(
  maxLength?: number,
  customMessages?: {
    required?: string;
    maxLength?: string;
  },
): FieldValidator<T, K> => {
  const validators: FieldValidator<T, K>[] = [
    createRequiredStringValidator<T, K>(customMessages?.required),
  ];

  if (maxLength) {
    validators.push(
      createMaxLengthFieldValidator<T, K>(maxLength, customMessages?.maxLength),
    );
  }

  return all(validators);
};

export const createOptionalFieldValidator = <T, K extends OptionalFieldName<T>>(
  maxLength?: number,
  customMessages?: {
    maxLength?: string;
  },
): FieldValidator<T, K> => {
  const validators: FieldValidator<T, K>[] = [
    createOptionalStringValidator<T, K>(),
  ];

  if (maxLength) {
    validators.push(
      createMaxLengthFieldValidator<T, K>(maxLength, customMessages?.maxLength),
    );
  }

  return or(validators);
};

// Convenience functions for common patterns
export const validateRequiredLocale = createRequiredFieldValidator<
  UnitTranslationFields,
  'locale'
>(150, {
  required: 'Name is required',
  maxLength: 'Name must be 150 characters or less',
});

export const validateOptionalDescription = createOptionalFieldValidator<
  UnitTranslationFields,
  'description'
>(1500, {
  maxLength: 'Description must be 1500 characters or less',
});

// ISO 3166-1 alpha-2 country codes
const ISO_3166_1_ALPHA_2_CODES = new Set([
  'AD',
  'AE',
  'AF',
  'AG',
  'AI',
  'AL',
  'AM',
  'AO',
  'AQ',
  'AR',
  'AS',
  'AT',
  'AU',
  'AW',
  'AX',
  'AZ',
  'BA',
  'BB',
  'BD',
  'BE',
  'BF',
  'BG',
  'BH',
  'BI',
  'BJ',
  'BL',
  'BM',
  'BN',
  'BO',
  'BQ',
  'BR',
  'BS',
  'BT',
  'BV',
  'BW',
  'BY',
  'BZ',
  'CA',
  'CC',
  'CD',
  'CF',
  'CG',
  'CH',
  'CI',
  'CK',
  'CL',
  'CM',
  'CN',
  'CO',
  'CR',
  'CU',
  'CV',
  'CW',
  'CX',
  'CY',
  'CZ',
  'DE',
  'DJ',
  'DK',
  'DM',
  'DO',
  'DZ',
  'EC',
  'EE',
  'EG',
  'EH',
  'ER',
  'ES',
  'ET',
  'FI',
  'FJ',
  'FK',
  'FM',
  'FO',
  'FR',
  'GA',
  'GB',
  'GD',
  'GE',
  'GF',
  'GG',
  'GH',
  'GI',
  'GL',
  'GM',
  'GN',
  'GP',
  'GQ',
  'GR',
  'GS',
  'GT',
  'GU',
  'GW',
  'GY',
  'HK',
  'HM',
  'HN',
  'HR',
  'HT',
  'HU',
  'ID',
  'IE',
  'IL',
  'IM',
  'IN',
  'IO',
  'IQ',
  'IR',
  'IS',
  'IT',
  'JE',
  'JM',
  'JO',
  'JP',
  'KE',
  'KG',
  'KH',
  'KI',
  'KM',
  'KN',
  'KP',
  'KR',
  'KW',
  'KY',
  'KZ',
  'LA',
  'LB',
  'LC',
  'LI',
  'LK',
  'LR',
  'LS',
  'LT',
  'LU',
  'LV',
  'LY',
  'MA',
  'MC',
  'MD',
  'ME',
  'MF',
  'MG',
  'MH',
  'MK',
  'ML',
  'MM',
  'MN',
  'MO',
  'MP',
  'MQ',
  'MR',
  'MS',
  'MT',
  'MU',
  'MV',
  'MW',
  'MX',
  'MY',
  'MZ',
  'NA',
  'NC',
  'NE',
  'NF',
  'NG',
  'NI',
  'NL',
  'NO',
  'NP',
  'NR',
  'NU',
  'NZ',
  'OM',
  'PA',
  'PE',
  'PF',
  'PG',
  'PH',
  'PK',
  'PL',
  'PM',
  'PN',
  'PR',
  'PS',
  'PT',
  'PW',
  'PY',
  'QA',
  'RE',
  'RO',
  'RS',
  'RU',
  'RW',
  'SA',
  'SB',
  'SC',
  'SD',
  'SE',
  'SG',
  'SH',
  'SI',
  'SJ',
  'SK',
  'SL',
  'SM',
  'SN',
  'SO',
  'SR',
  'SS',
  'ST',
  'SV',
  'SX',
  'SY',
  'SZ',
  'TC',
  'TD',
  'TF',
  'TG',
  'TH',
  'TJ',
  'TK',
  'TL',
  'TM',
  'TN',
  'TO',
  'TR',
  'TT',
  'TV',
  'TW',
  'TZ',
  'UA',
  'UG',
  'UM',
  'US',
  'UY',
  'UZ',
  'VA',
  'VC',
  'VE',
  'VG',
  'VI',
  'VN',
  'VU',
  'WF',
  'WS',
  'YE',
  'YT',
  'ZA',
  'ZM',
  'ZW',
]);

export const createValidLocaleValidator = (customMessage?: string) => {
  return (value: string): ValidationErrors => {
    const upperCaseValue = value.trim().toUpperCase();

    if (!ISO_3166_1_ALPHA_2_CODES.has(upperCaseValue)) {
      return {
        locale:
          customMessage ||
          'locale must be a valid ISO 3166-1 alpha-2 country code',
      };
    }

    return {};
  };
};

// Convenience validator for locale fields
export const validateLocaleCode = createValidLocaleValidator(
  'Locale must be a valid ISO 3166-1 alpha-2 country code',
);
