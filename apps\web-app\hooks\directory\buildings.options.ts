import {
    getDirectoryByIdOptions,
    getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
    CollectionViewParamType,
    ResourceViewType,
} from '@rie/domain/types';

export const getAllBuildingsOptions = <
    View extends CollectionViewParamType['view'],
>({
    locale,
    view,
}: CollectionOptionsParams<View>) =>
    getDirectoryListOptions({
        directoryListKey: 'building' as const,
        locale,
        view,
    });

export const getBuildingByIdOptions = <View extends ResourceViewType>({
    id,
    view,
}: {
    id: string;
    view: View;
}) =>
    getDirectoryByIdOptions({ directoryListKey: 'building', id, view } as const);