import type { RieServiceParams } from '@/constants/rie-client';
import { getApiClient } from '@/services/client/api-client';
import type { ApiInfiniteReturnType, ApiReturnType } from '@/types/common';
import type { InfrastructureFull } from '@/types/infrastructure';
import type { SupportedLocale } from '@/types/locale';
import type {
  InfrastructureData,
  InfrastructureInput,
  InfrastructureWithRelatedEquipments,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllInfrastructures = async (
  params: RieServiceParams,
  queryParams: string,
): Promise<ApiReturnType<InfrastructureData[]>> => {
  try {
    // Parse queryParams string into URLSearchParams and convert to object
    const urlSearchParams = new URLSearchParams(queryParams);
    const queryParamsObject = Object.fromEntries(urlSearchParams.entries());

    // Combine all parameters
    const allParams = {
      ...params,
      ...queryParamsObject,
    };

    const apiClient = await getApiClient();

    return await apiClient
      .get<ApiReturnType<InfrastructureData[]>>('v2/infrastructures', {
        searchParams: allParams,
      })
      .json();
  } catch (error) {
    console.error('Error getting all infrastructures:', error);
    throw error;
  }
};

export const getInfrastructureByIdWithRelatedEquipments = async (
  id: string,
  locale: SupportedLocale,
): Promise<InfrastructureWithRelatedEquipments> => {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .get<InfrastructureWithRelatedEquipments>(`v2/infrastructures/${id}`, {
        searchParams: {
          locale,
          view: 'detail',
        },
      })
      .json();
  } catch (error) {
    console.error('Error getting infrastructure:', error);
    throw error;
  }
};

export const createInfrastructure = async (
  payload: InfrastructureInput,
): Promise<InfrastructureData> => {
  try {
    const apiClient = await getApiClient();

    return await apiClient
      .post<InfrastructureData>('v2/infrastructures', {
        json: payload,
      })
      .json();
  } catch (error) {
    console.error('Error creating infrastructure:', error);
    throw error;
  }
};

export const updateInfrastructure = async (
  id: string,
  payload: InfrastructureInput,
): Promise<InfrastructureData> => {
  try {
    const apiClient = await getApiClient();
    return await apiClient
      .put<InfrastructureData>(`v2/infrastructures/${id}`, {
        json: payload,
      })
      .json();
  } catch (error) {
    console.error('Error updating infrastructure:', error);
    throw error;
  }
};

export const getInfrastructuresList = async ({
  pageParam,
  params,
  queryParams,
  viewOwnInfrastructures,
}: {
  pageParam: number;
  params: RieServiceParams;
  queryParams: string;
  viewOwnInfrastructures: boolean;
}): Promise<ApiInfiniteReturnType<InfrastructureFull[]>> => {
  try {
    const apiClient = await getApiClient();

    // Parse queryParams string into URLSearchParams and convert to object
    const urlSearchParams = new URLSearchParams(queryParams);
    const queryParamsObject = Object.fromEntries(urlSearchParams.entries());

    // Combine all search parameters
    const allSearchParams = {
      ...params,
      start: pageParam.toString(),
      ...(viewOwnInfrastructures && { userdata: '1' }),
      ...queryParamsObject,
    };

    return await apiClient
      .get<ApiInfiniteReturnType<InfrastructureData[]>>('v2/infrastructures', {
        searchParams: allSearchParams,
      })
      .json();
  } catch (error) {
    console.error('Error getting infrastructures list:', error);
    throw error;
  }
};

export const getInfrastructureById = async (
  id: string,
  locale: SupportedLocale,
  view: ResourceViewType,
  queryParams?: string,
): Promise<InfrastructureData> => {
  try {
    const apiClient = await getApiClient();

    // Parse queryParams string into URLSearchParams and convert to object
    const urlSearchParams = new URLSearchParams(queryParams || '');
    const queryParamsObject = Object.fromEntries(urlSearchParams.entries());

    // Combine all search parameters
    const allSearchParams = {
      locale,
      view,
      ...queryParamsObject,
    };

    return await apiClient
      .get<InfrastructureData>(`v2/infrastructures/${id}`, {
        searchParams: allSearchParams,
      })
      .json();
  } catch (error) {
    console.error(
      'Error getting infrastructure by id:',
      JSON.stringify(error, null, 2),
    );
    throw error;
  }
};

export const deleteInfrastructure = async (id: string): Promise<boolean> => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete<boolean>(`v2/infrastructures/${id}`).json();
  } catch (error) {
    console.error('Error deleting infrastructure:', error);
    throw error;
  }
};
