import {
  PermissionGroupAlreadyExistsError,
  PermissionGroupNotFoundError,
} from '@rie/domain/errors';
import {
  DbPermissionGroupToPermissionsGroupEdit,
  PermissionsGroupInputToDBInput,
  dbPermissionGroupsToPermissionGroups,
  dbPermissionsGroupToPermissionGroup,
} from '@rie/domain/serializers';
import type {
  CollectionViewParamType,
  PermissionsGroupInput,
  ResourceViewType,
} from '@rie/domain/types';
import { PermissionsGroupsRepositoryLive } from '@rie/repositories';
import { Effect } from 'effect';
import * as Schema from 'effect/Schema';

interface UpdatePermissionsGroupParams extends PermissionsGroupInput {
  id: string;
}

export class PermissionsGroupsServiceLive extends Effect.Service<PermissionsGroupsServiceLive>()(
  'PermissionGroupsServiceLive',
  {
    dependencies: [PermissionsGroupsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get all permission groups
       */
      const getAllPermissionsGroups = ({ view }: CollectionViewParamType) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;
          const permissionGroups =
            yield* permissionsGroupsRepository.findAllPermissionsGroups();
          const t1 = performance.now();

          console.log(
            `Call to getAllPermissionGroups took ${t1 - t0} milliseconds.`,
          );

          return dbPermissionGroupsToPermissionGroups(permissionGroups, view);
        });
      };

      /**
       * Get a permission group by ID with all its permissions
       */
      const getPermissionsGroupById = (params: {
        id: string;
        view: ResourceViewType;
      }) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;
          const permissionGroup =
            yield* permissionsGroupsRepository.findPermissionsGroupById(
              params.id,
            );

          if (!permissionGroup) {
            return yield* Effect.fail(
              new PermissionGroupNotFoundError({ id: params.id }),
            );
          }

          const t1 = performance.now();
          console.log(
            `Call to getPermissionGroupById took ${t1 - t0} milliseconds.`,
          );

          console.log(
            'permissionGroup',
            dbPermissionsGroupToPermissionGroup(permissionGroup, params.view),
          );

          // Use the new serializer and then transform to input format
          return dbPermissionsGroupToPermissionGroup(
            permissionGroup,
            params.view,
          );
        });
      };

      const createPermissionsGroup = (input: PermissionsGroupInput) => {
        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Transform input to database format
          const dbInput = yield* Schema.decode(PermissionsGroupInputToDBInput)(
            input,
          );

          // Check if permission group already exists
          const existingGroup =
            yield* permissionGroupsRepository.checkPermissionsGroupExists(
              dbInput.name,
            );

          if (existingGroup) {
            return yield* Effect.fail(
              new PermissionGroupAlreadyExistsError({ name: dbInput.name }),
            );
          }

          const createdGroup =
            yield* permissionGroupsRepository.createPermissionsGroup(dbInput);

          // Fetch the created group with permissions to get complete data
          const fullGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(
              createdGroup.id,
            );

          if (!fullGroup) {
            return yield* Effect.fail(
              new Error('Failed to fetch created permission group'),
            );
          }

          // Transform to input format for consistent API response
          return yield* Schema.decode(DbPermissionGroupToPermissionsGroupEdit)(
            fullGroup,
          );
        });
      };

      /**
       * Update a permission group
       */

      const updatePermissionsGroup = (params: UpdatePermissionsGroupParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Transform input to database format
          const dbInput = yield* Schema.decode(PermissionsGroupInputToDBInput)(
            params,
          );

          // Check if permission group exists
          const permissionGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(
              params.id,
            );
          if (!permissionGroup) {
            return yield* Effect.fail(
              new PermissionGroupNotFoundError({ id: params.id }),
            );
          }

          // Check if another permission group with the same name already exists
          const existingGroup =
            yield* permissionGroupsRepository.checkPermissionsGroupExists(
              dbInput.name,
            );
          if (existingGroup && existingGroup.id !== params.id) {
            return yield* Effect.fail(
              new PermissionGroupAlreadyExistsError({ name: dbInput.name }),
            );
          }

          yield* permissionGroupsRepository.updatePermissionsGroup({
            id: params.id,
            ...dbInput,
          });

          // Fetch the updated group with permissions to get complete data
          const fullGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(
              params.id,
            );

          if (!fullGroup) {
            return yield* Effect.fail(
              new Error('Failed to fetch updated permission group'),
            );
          }

          const t1 = performance.now();

          console.log(
            `Call to updatePermissionGroup took ${t1 - t0} milliseconds.`,
          );

          // Transform to input format for consistent API response
          return yield* Schema.decode(DbPermissionGroupToPermissionsGroupEdit)(
            fullGroup,
          );
        });
      };

      /**
       * Delete a permission group
       */
      const deletePermissionsGroup = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Check if permission group exists
          const permissionGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(id);
          if (!permissionGroup) {
            return yield* Effect.fail(new PermissionGroupNotFoundError({ id }));
          }

          const result =
            yield* permissionGroupsRepository.deletePermissionsGroup(id);
          const t1 = performance.now();

          console.log(
            `Call to deletePermissionGroup took ${t1 - t0} milliseconds.`,
          );

          return result.length > 0;
        });
      };

      return {
        getAllPermissionsGroups,
        getPermissionsGroupById,
        createPermissionsGroup,
        updatePermissionsGroup,
        deletePermissionsGroup,
      } as const;
    }),
  },
) {}
