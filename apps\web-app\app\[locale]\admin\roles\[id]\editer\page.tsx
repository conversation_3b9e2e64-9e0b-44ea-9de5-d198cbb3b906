import { EditRole } from '@/app/[locale]/admin/roles/[id]/editer/edit-role';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllPermissionsGroupsOptions } from '@/hooks/admin/permissions/permissions-groups.options';
import { getAllPermissionsOptions } from '@/hooks/admin/permissions/permissions.options';
import {
  getAllRolesOptions,
  getRoleByIdOptions,
} from '@/hooks/admin/permissions/roles.options';
import { redirect } from '@/lib/navigation';
import type { ResourcePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function EditRolePage({ params }: ResourcePageParams) {
  const { id, locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: `/admin/roles/${id}/editer` },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  // Prefetch the role data and related data
  await Promise.all([
    queryClient.prefetchQuery(getRoleByIdOptions(id, { view: 'edit' })),
    queryClient.prefetchQuery(getAllPermissionsOptions({ view: 'select' })),
    queryClient.prefetchQuery(
      getAllPermissionsGroupsOptions({ view: 'select' }),
    ),
    queryClient.prefetchQuery(getAllRolesOptions({ view: 'select' })),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="container mx-auto py-10">
        <EditRole id={id} />
      </div>
    </HydrationBoundary>
  );
}
