#!/bin/bash
set -e

# Navigate to the db-schema directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR" || exit 1

# Start the PostgreSQL container
echo "Starting PostgreSQL container..."
docker compose up -d

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until docker exec rie-database-postgres pg_isready -U ${PG_DATABASE_USER:-rie}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

echo "PostgreSQL is up and running!"

# Generate and run database migrations from db-schema
echo "Generating database migrations..."
pnpm db:generate
if [ $? -ne 0 ]; then
  echo "Database migration generation failed!"
  exit 1
fi

echo "Running database migrations..."
pnpm db:migrate
if [ $? -ne 0 ]; then
  echo "Database migration failed!"
  exit 1
fi

# Wait a moment to ensure migrations are fully applied
echo "Ensuring migrations are fully applied..."
sleep 2

# After PostgreSQL is ready and before data imports
echo "Creating database triggers..."
docker exec -i rie-database-postgres psql -U ${PG_DATABASE_USER:-rie} -d ${PG_DATABASE_NAME:-riedb} < src/triggers/default_role_trigger.sql
if [ $? -ne 0 ]; then
  echo "Failed to create default role trigger!"
  exit 1
fi

docker exec -i rie-database-postgres psql -U ${PG_DATABASE_USER:-rie} -d ${PG_DATABASE_NAME:-riedb} < src/triggers/link_user_to_person_trigger.sql
if [ $? -ne 0 ]; then
  echo "Failed to create user-person linking trigger!"
  exit 1
fi

docker exec -i rie-database-postgres psql -U ${PG_DATABASE_USER:-rie} -d ${PG_DATABASE_NAME:-riedb} < src/triggers/set_updated_at_trigger.sql
if [ $? -ne 0 ]; then
  echo "Failed to create set updated at trigger!"
  exit 1
fi

echo "Database triggers created successfully!"

echo "Applying set updated at trigger..."
docker exec -i rie-database-postgres psql -U ${PG_DATABASE_USER:-rie} -d ${PG_DATABASE_NAME:-riedb} < src/triggers/apply_set_updated_at_trigger.sql
if [ $? -ne 0 ]; then
  echo "Failed to apply set updated at trigger!"
  exit 1
fi

echo "Database setup complete!"
