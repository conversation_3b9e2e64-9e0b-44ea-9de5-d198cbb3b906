/**
 * Generic validation types for domain entities.
 * Provides type-safe validation error handling across all domain entities.
 */

// Generic field extraction utilities
export type RequiredFieldName<T> = {
  [K in keyof T]: T[K] extends string
    ? null extends T[K]
      ? never
      : undefined extends T[K]
        ? never
        : K
    : never;
}[keyof T] extends infer U
  ? U extends string
    ? U
    : never
  : never;

export type OptionalFieldName<T> = {
  [K in keyof T]: T[K] extends string | null | undefined
    ? null extends T[K]
      ? K
      : undefined extends T[K]
        ? K
        : never
    : never;
}[keyof T] extends infer U
  ? U extends string
    ? U
    : never
  : never;

// Generic ValidationErrors type
export type ValidationErrors<T = unknown> = Partial<
  Record<RequiredFieldName<T> | OptionalFieldName<T>, string>
>;

// Generic field validator type
export type FieldValidator<T, K extends keyof T> = (
  value: T[K],
  fieldName: K,
) => ValidationErrors<T>;
