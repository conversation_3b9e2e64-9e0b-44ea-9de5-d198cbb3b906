'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { useDeleteFundingProject } from '@/hooks/directory/funding-projects.hook';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import { Button } from '@/ui/button';
import type { FundingProjectList } from '@rie/domain/types';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const fundingProjectsColumns = (
  _locale: SupportedLocale,
): ColumnDef<FundingProjectList>[] => {
  const directoryEntity = 'fundingProject' as const;

  return [
    {
      accessorKey: 'text',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'titulaire',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.titulaire"
        />
      ),
      id: 'titulaire',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'infrastructure',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.infrastructure"
        />
      ),
      id: 'infrastructure',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'obtainingYear',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.obtainingYear"
        />
      ),
      id: 'obtainingYear',
      maxSize: 150,
      minSize: 100,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <FundingProjectActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const FundingProjectActions = ({ row }: { row: Row<FundingProjectList> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteFundingProject } = useDeleteFundingProject();
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.fundingProject;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-funding-project"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteFundingProject(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.text })}
        trigger={
          <Button
            size="icon"
            variant="destructive"
            data-testid="delete-funding-project"
          >
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
