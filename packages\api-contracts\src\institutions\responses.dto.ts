import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    RequiredLocalizedFieldDtoSchema,
} from '../common';

// Base schema for institutions
export const InstitutionBaseDtoSchema = Schema.Struct({
    id: Schema.String,
    isActive: Schema.Boolean,
    guidId: Schema.NullOr(Schema.String),
    typeId: Schema.String,
});

// DTO for lists of institutions (e.g., view='list')
export const InstitutionListItemDtoSchema = Schema.Struct({
    id: Schema.String,
    text: Schema.String,
    acronym: Schema.NullOr(Schema.String),
    establishmentType: Schema.NullOr(Schema.String),
    lastUpdatedAt: Schema.String,
    createdAt: Schema.String,
    uid: Schema.NullOr(Schema.String),
});

export type InstitutionListItemDTO = Schema.Schema.Type<
    typeof InstitutionListItemDtoSchema
>;

// Institution-specific translation schemas
export const InstitutionTranslationDtoSchema = Schema.Struct({
    name: RequiredLocalizedFieldDtoSchema,
    description: OptionalLocalizedFieldDtoSchema,
    otherNames: OptionalLocalizedFieldDtoSchema,
    acronyms: OptionalLocalizedFieldDtoSchema,
});

export const InstitutionTranslationInputDtoSchema = Schema.Struct({
    name: OptionalLocalizedFieldDtoSchema,
    description: OptionalLocalizedFieldDtoSchema,
    otherNames: OptionalLocalizedFieldDtoSchema,
    acronyms: OptionalLocalizedFieldDtoSchema,
});

// DTO for the detailed view of a single institution (e.g., view='detail')
export const InstitutionDetailResponseDtoSchema = Schema.extend(
    InstitutionBaseDtoSchema,
    Schema.Struct({
        translations: InstitutionTranslationDtoSchema,
    }),
);

export type InstitutionDetailResponseDTO = Schema.Schema.Type<
    typeof InstitutionDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const InstitutionEditResponseDtoSchema = Schema.extend(
    InstitutionBaseDtoSchema,
    Schema.Struct({
        translations: Schema.Array(InstitutionTranslationInputDtoSchema),
    }),
);
export type InstitutionEditResponseDTO = Schema.Schema.Type<
    typeof InstitutionEditResponseDtoSchema
>;