import { RoomCategoryMigrationConverter } from '../converters/08-01-convert-room-category-inserts';

async function convertRoomCategoryData() {
  // Create converter and run conversion
  const converter = new RoomCategoryMigrationConverter();
  await converter.convertFile();
}

// Run the conversion
convertRoomCategoryData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
