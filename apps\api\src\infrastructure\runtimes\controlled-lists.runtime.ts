import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  ControlledListsRepositoryLive,
  LocaleRepositoryLive,
} from '@rie/repositories';
import { ControlledListsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const ControlledListsServicesLayer = Layer.mergeAll(
  ControlledListsRepositoryLive.Default,
  ControlledListsServiceLive.Default,
  LocaleRepositoryLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const ControlledListsRuntime = ManagedRuntime.make(
  ControlledListsServicesLayer,
);
