import {
  type CampusDetailResponseDTO,
  CampusDetailResponseDtoSchema,
  type CampusEditResponseDTO,
  CampusEditResponseDtoSchema,
  type CampusListItemDTO,
  CampusListItemDtoSchema,
  type SelectOptionDTO,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import type { Campus } from '@rie/domain/aggregates';
import { DbCampusDataSchema } from '@rie/domain/schemas';
import type { CampusData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes a Campus aggregate into the shape required for a list view.
 *
 * Uses the Campus aggregate's composite translation logic to provide intelligent
 * locale fallback behavior, ensuring the best available translation is selected
 * for each field based on the requested locale and fallback preferences.
 */
export function serializeCampusToListItem(
  campus: Campus,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<CampusListItemDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbCampusDataSchema,
    CampusListItemDtoSchema,
    {
      strict: true,
      decode: (campusData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Campus aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = campus.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Ensure we always provide a non-empty string for text
            const nameCandidate = compositeTranslations.name.value;
            const safeText =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (campusData.sadId ?? campusData.id);

            // Try to compute jurisdiction from institution translations (prefer requested locale)
            type InstitutionTranslation = { locale: string; name: string | null };
            const institutionTranslations = (campusData as unknown as {
              institution?: { translations?: InstitutionTranslation[] };
            }).institution?.translations as InstitutionTranslation[] | undefined;
            let jurisdiction: string | null = null;
            if (Array.isArray(institutionTranslations)) {
              const preferred = institutionTranslations.find(
                (t) => t.locale === locale && t.name && t.name.trim() !== '',
              );
              const fallback = institutionTranslations.find(
                (t) => t.locale === fallbackLocale && t.name && t.name.trim() !== '',
              );
              const anyName = institutionTranslations.find(
                (t) => t.name && t.name.trim() !== '',
              );
              jurisdiction = preferred?.name ?? fallback?.name ?? anyName?.name ?? null;
            }

            return {
              id: campusData.id,
              /** We are forced to cast `compositeTranslations.name.value` as string
               * because in the database it is defined as string | null.
               * However, we have defined a rule that states that at least one translation must have a name.
               * Therefore, we can safely cast to string.
               **/
              text: safeText,
              jurisdiction,
              lastUpdatedAt: campusData.updatedAt,
              createdAt: campusData.createdAt,
              uid: null,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              campusData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize campus to list item',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    campus.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Campus ListItem] Failed for campus id=${campus.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes a Campus aggregate into the shape required for a detailed view.
 *
 * Uses the Campus aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for all translatable fields. The resulting DTO
 * contains the best available translation for each field based on the requested
 * locale and fallback preferences, formatted for detailed view consumption.
 */
export function serializeCampusToDetail(
  campus: Campus,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<CampusDetailResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbCampusDataSchema, // Source
    CampusDetailResponseDtoSchema, // Target
    {
      strict: true,
      decode: (campusData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Campus aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = campus.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Ensure we always provide a non-empty string for name
            const nameCandidate = compositeTranslations.name.value;
            const safeName =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (campusData.sadId ?? campusData.id);

            return {
              id: campusData.id,
              isActive: campusData.isActive ?? true,
              sadId: campusData.sadId,
              institutionId: campusData.institutionId,
              translations: {
                /** We are forced to cast `compositeTranslations.name.value` as string
                 * because in the database it is defined as string | null.
                 * However, we have defined a rule that states that at least one translation must have a name.
                 * Therefore, we can safely cast to string.
                 **/
                name: {
                  locale: compositeTranslations.name.locale,
                  value: safeName,
                },
              },
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              campusData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize campus to detail view',
            );
          },
        });
      },

      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    campus.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Campus Detail] Failed for campus id=${campus.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes a Campus aggregate into a simple value/label pair for select inputs.
 *
 * Uses the Campus aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for the label field. This ensures that select options
 * display the best available campus name based on the requested locale and fallback
 * preferences, providing a consistent user experience across different languages.
 */
export function serializeCampusToSelectOption(
  campus: Campus,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbCampusDataSchema,
    SelectOptionDtoSchema,
    {
      strict: true,
      decode: (campusData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Campus aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = campus.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Ensure we always provide a non-empty string for label
            const nameCandidate = compositeTranslations.name.value;
            const safeLabel =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (campusData.sadId ?? campusData.id);

            return {
              value: campusData.id,
              label: safeLabel,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              campusData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize campus to select option',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    campus.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Campus SelectOption] Failed for campus id=${campus.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes database campus data into the shape required for an edit form.
 *
 * This function transforms raw database campus data (including all translations)
 * into the format expected by the frontend edit form. Unlike other serializers
 * that work with Campus aggregates and provide locale-based fallback behavior,
 * this function works directly with database data and preserves all available
 * translations for editing purposes.
 *
 * The resulting DTO contains all campus fields plus an array of all available
 * translations, allowing the edit form to display and modify translations
 * in multiple locales simultaneously.
 */
export function serializeCampusToFormEdit(
  campusData: CampusData,
): Effect.Effect<CampusEditResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbCampusDataSchema,
    CampusEditResponseDtoSchema,
    {
      strict: true,
      decode: (data, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Transform database translations to the format expected by the edit form
            const transformedTranslations = data.translations.map(
              (translation) => ({
                name: {
                  locale: translation.locale,
                  value: translation.name,
                },
              }),
            );

            return {
              id: data.id,
              isActive: data.isActive ?? true,
              sadId: data.sadId,
              institutionId: data.institutionId,
              translations: transformedTranslations,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              data,
              error instanceof Error
                ? error.message
                : 'Failed to serialize campus to edit form',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return Schema.decode(transformer)(campusData);
}

/**
 * Serializes a Campus aggregate into the client edit form shape expected by the frontend.
 * Includes a resolved jurisdiction select option with value/label.
 */
export function serializeCampusToEditClient(
  campus: Campus,
  jurisdictionLabel: string | null,
): Effect.Effect<
  {
    id: string;
    jurisdiction: { value: string; label: string };
    pseudonym: string;
    name: Array<{ locale: string; value: string }>;
  },
  ParseResult.ParseError
> {
  const transformer = Schema.transformOrFail(
    DbCampusDataSchema,
    Schema.Struct({
      id: Schema.String,
      jurisdiction: Schema.Struct({
        value: Schema.String,
        label: Schema.String,
      }),
      pseudonym: Schema.String,
      name: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          value: Schema.String,
        }),
      ),
    }),
    {
      strict: true,
      decode: (data, _options, ast) =>
        ParseResult.try({
          try: () => ({
            id: data.id,
            jurisdiction: {
              value: data.institutionId ?? '',
              label:
                (jurisdictionLabel && jurisdictionLabel.trim() !== ''
                  ? jurisdictionLabel
                  : data.institutionId ?? '') || '',
            },
            pseudonym: data.sadId ?? '',
            name: data.translations.map((t) => ({
              locale: t.locale,
              value: t.name ?? '',
            })),
          }),
          catch(error) {
            console.error('[Serializer][Campus EditClient] Failed with input:', data);
            return new ParseResult.Type(
              ast,
              data,
              error instanceof Error
                ? error.message
                : 'Failed to serialize campus to edit client form',
            );
          },
        }),
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    campus.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    // Convert readonly properties/arrays to mutable shapes expected by the frontend form schema
    Effect.map((dto) => ({
      id: dto.id,
      jurisdiction: { value: dto.jurisdiction.value, label: dto.jurisdiction.label },
      pseudonym: dto.pseudonym,
      name: dto.name.map((n) => ({ locale: n.locale, value: n.value })),
    })),
  );
}

/**
 * Transforms client edit form payload into the domain input shape
 * consumed by creation/update flows (no separate mapper file).
 */
export const CampusEditClientToDomainInput = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.optional(Schema.String),
    jurisdiction: Schema.Struct({
      value: Schema.NullOr(Schema.String),
      label: Schema.NullOr(Schema.String),
    }),
    pseudonym: Schema.String,
    name: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        value: Schema.String,
      }),
    ),
  }),
  Schema.Struct({
    sadId: Schema.NullOr(Schema.String),
    institutionId: Schema.String,
    translations: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        name: Schema.String,
      }),
    ),
  }),
  {
    strict: true,
    decode: (data, _options, ast) =>
      ParseResult.try({
        try: () => ({
          sadId: data.pseudonym ?? null,
          institutionId: data.jurisdiction.value ?? '',
          translations: data.name.map((n) => ({
            locale: n.locale,
            name: n.value,
          })),
        }),
        catch(error) {
          return new ParseResult.Type(
            ast,
            data,
            error instanceof Error
              ? error.message
              : 'Failed to transform campus edit client payload to domain input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
