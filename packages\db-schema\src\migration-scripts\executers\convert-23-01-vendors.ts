import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { VendorMigrationConverter } from '../converters/23-01-convert-vendor-inserts';

async function convertVendorData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create converter instance
    const converter = new VendorMigrationConverter();
    // Convert the file
    await converter.convertFile();
    console.log(`Conversion completed. Output saved to ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertVendorData().catch(console.error);
