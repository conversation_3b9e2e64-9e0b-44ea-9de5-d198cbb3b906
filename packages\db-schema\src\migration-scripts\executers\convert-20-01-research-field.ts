import { ResearchFieldMigrationConverter } from '../converters/20-01-convert-research-field-inserts';

async function convertResearchFieldData() {
  try {
    // Initialize converter
    const converter = new ResearchFieldMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertResearchFieldData().catch(console.error);
