import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { BuildingMigrationConverter } from '../converters/30-01-convert-building-inserts';

async function convertBuildingData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new BuildingMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertBuildingData().catch(console.error);
