import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  EquipmentsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const EquipmentsServicesLayer = Layer.mergeAll(
  EquipmentsRepositoryLive.Default,
  EquipmentsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const EquipmentsRuntime = ManagedRuntime.make(EquipmentsServicesLayer);
