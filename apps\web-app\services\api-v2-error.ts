import { HTTPError, type NormalizedOptions } from 'ky';

export class APIV2Error<T = unknown> extends HTTPError {
  data?: T;

  constructor(
    message: string,
    response: Response,
    request: Request,
    options: NormalizedOptions,
    data?: T,
  ) {
    super(response, request, options);
    this.name = 'APIV2Error';
    this.message = message;
    this.data = data;
  }

  get status(): number {
    return this.response.status;
  }

  static fromError<T>(error: unknown): APIV2Error<T> | Error {
    if (error instanceof APIV2Error) {
      return error as APIV2Error<T>;
    }

    if (error instanceof HTTPError) {
      return new APIV2Error<T>(
        error.message,
        error.response,
        error.request,
        error.options,
      );
    }

    if (error instanceof Error) {
      return error;
    }

    // Unexpected error type
    return new Error('An unexpected error occurred');
  }
}
