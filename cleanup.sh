#!/bin/bash

# cleanup.sh - Comprehensive cleanup script for RIE monorepo
# This script removes build artifacts, dependencies, and cache files
# Safe to run multiple times - no errors if files/directories don't exist

set -e  # Exit on any error

echo "🧹 Starting cleanup of RIE monorepo..."

# Function to safely remove files/directories
safe_remove() {
    local path="$1"
    local description="$2"

    if [ -e "$path" ]; then
        echo "  Removing $description: $path"
        rm -rf "$path"
    fi
}

# Function to find and remove files by pattern
remove_by_pattern() {
    local pattern="$1"
    local description="$2"

    echo "  Searching for $description..."
    find . -name "$pattern" -type f -exec rm -f {} + 2>/dev/null || true
    find . -name "$pattern" -type d -exec rm -rf {} + 2>/dev/null || true
}

echo "📦 Removing node_modules directories..."
# Remove node_modules from root
safe_remove "node_modules" "root node_modules"
safe_remove ".pnpm-store" "pnpm store"

# Remove node_modules from all apps
for app_dir in apps/*/; do
    if [ -d "$app_dir" ]; then
        safe_remove "${app_dir}node_modules" "$(basename "$app_dir") node_modules"
    fi
done

# Remove node_modules from all packages
for pkg_dir in packages/*/; do
    if [ -d "$pkg_dir" ]; then
        safe_remove "${pkg_dir}node_modules" "$(basename "$pkg_dir") node_modules"
    fi
done

echo "🏗️  Removing build directories..."
# Remove build directories from root
safe_remove "build" "root build directory"
safe_remove "dist" "root dist directory"

# Remove build directories from apps
for app_dir in apps/*/; do
    if [ -d "$app_dir" ]; then
        app_name=$(basename "$app_dir")
        safe_remove "${app_dir}build" "$app_name build directory"
        safe_remove "${app_dir}dist" "$app_name dist directory"
        safe_remove "${app_dir}.next" "$app_name Next.js build"
        safe_remove "${app_dir}out" "$app_name Next.js export"
    fi
done

# Remove build directories from packages
for pkg_dir in packages/*/; do
    if [ -d "$pkg_dir" ]; then
        pkg_name=$(basename "$pkg_dir")
        safe_remove "${pkg_dir}build" "$pkg_name build directory"
        safe_remove "${pkg_dir}dist" "$pkg_name dist directory"
    fi
done

echo "📄 Removing TypeScript build info files..."
remove_by_pattern "tsconfig.build.tsbuildinfo" "TypeScript build info files"
remove_by_pattern "tsconfig.tsbuildinfo" "TypeScript build info files"
remove_by_pattern "*.tsbuildinfo" "all TypeScript build info files"

echo "🗂️  Removing cache and temporary files..."
safe_remove ".turbo" "Turbo cache"
safe_remove "coverage" "test coverage"

# Remove test artifacts from apps
for app_dir in apps/*/; do
    if [ -d "$app_dir" ]; then
        app_name=$(basename "$app_dir")
        safe_remove "${app_dir}coverage" "$app_name coverage"
        safe_remove "${app_dir}test-results" "$app_name test results"
        safe_remove "${app_dir}playwright-report" "$app_name Playwright reports"
        safe_remove "${app_dir}.nyc_output" "$app_name NYC output"
    fi
done

echo "🧽 Removing other temporary files..."
remove_by_pattern ".DS_Store" "macOS .DS_Store files"
remove_by_pattern "*.log" "log files"
remove_by_pattern ".env.local" "local environment files"

echo "✅ Cleanup completed successfully!"
echo ""
echo "💡 Next steps:"
echo "   - Run 'pnpm install --frozen-lockfile' to reinstall dependencies"
echo "   - Run 'pnpm build' to rebuild all packages"
echo ""