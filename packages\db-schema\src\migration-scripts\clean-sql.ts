#!/usr/bin/env bun

/**
 * SQL Preprocessing Script for MySQL Dump Sanitization
 *
 * This script sanitizes MySQL dump files to fix backslash escaping issues
 * that cause parsing problems in the migration system.
 *
 * Usage: bun run clean-sql.ts <input-file.sql> [--output <output-file.sql>] [--in-place]
 */

import * as fs from 'node:fs/promises';
import * as path from 'node:path';

interface TransformationStats {
  backslashEscapes: number;
  frenchContractions: number;
  totalReplacements: number;
  linesProcessed: number;
  insertStatementsProcessed: number;
  fileSize: {
    before: number;
    after: number;
  };
}

class SQLPreprocessor {
  private stats: TransformationStats = {
    backslashEscapes: 0,
    frenchContractions: 0,
    totalReplacements: 0,
    linesProcessed: 0,
    insertStatementsProcessed: 0,
    fileSize: { before: 0, after: 0 },
  };

  /**
   * Main transformation method that applies all sanitization rules
   */
  private transformSQL(content: string): string {
    this.stats.fileSize.before = content.length;

    // Split into lines for processing
    const lines = content.split('\n');
    this.stats.linesProcessed = lines.length;

    // Process each line
    const transformedLines = lines.map((line) => this.transformLine(line));

    const result = transformedLines.join('\n');
    this.stats.fileSize.after = result.length;

    return result;
  }

  /**
   * Transform a single line of SQL
   */
  private transformLine(line: string): string {
    // Skip non-INSERT lines to avoid modifying structure
    if (!line.trim().startsWith('INSERT INTO')) {
      return line;
    }

    this.stats.insertStatementsProcessed++;
    let transformedLine = line;

    // Apply transformations to INSERT statements
    transformedLine = this.fixBackslashEscaping(transformedLine);
    transformedLine = this.fixFrenchContractions(transformedLine);

    return transformedLine;
  }

  /**
   * Fix MySQL backslash escaping (\') to SQL standard ('')
   */
  private fixBackslashEscaping(line: string): string {
    // Replace \' with '' but be careful not to affect other backslash sequences
    const backslashEscapeRegex = /\\'/g;
    const matches = (line.match(backslashEscapeRegex) || []).length;

    if (matches > 0) {
      this.stats.backslashEscapes += matches;
      return line.replace(backslashEscapeRegex, "''");
    }

    return line;
  }

  /**
   * Fix French contractions and possessives within quoted strings
   * This handles patterns like d'analyse, l'équipement, s'applique, etc.
   */
  private fixFrenchContractions(line: string): string {
    let result = line;

    // Process quoted strings in the INSERT statement
    // This regex finds quoted strings while handling escaped quotes
    const quotedStringRegex = /'((?:[^'\\]|\\.)*)'/g;

    result = result.replace(quotedStringRegex, (match, content) => {
      // Within this quoted string, look for French contractions
      // Pattern: common French contractions followed by a letter
      const contractionPatterns = [
        // Common French contractions
        {
          pattern:
            /\b([dlsntmjc])'([a-zA-ZàâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ])/gi,
          desc: 'French contractions',
        },
        // Possessives and other patterns
        {
          pattern:
            /([a-zA-ZàâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ])'([a-zA-ZàâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ])/gi,
          desc: 'General contractions',
        },
      ];

      let transformedContent = content;
      let totalMatches = 0;

      for (const { pattern } of contractionPatterns) {
        const matches = (transformedContent.match(pattern) || []).length;
        if (matches > 0) {
          transformedContent = transformedContent.replace(pattern, "$1''$2");
          totalMatches += matches;
        }
      }

      if (totalMatches > 0) {
        this.stats.frenchContractions += totalMatches;
      }

      return `'${transformedContent}'`;
    });

    return result;
  }

  /**
   * Process the SQL file
   */
  async processSQLFile(inputPath: string, outputPath: string): Promise<void> {
    console.log(`🔄 Processing SQL file: ${inputPath}`);

    try {
      // Read the input file
      const content = await fs.readFile(inputPath, 'utf8');
      console.log(
        `📖 Read ${content.length.toLocaleString()} characters from input file`,
      );

      // Transform the content
      console.log('🔧 Applying transformations...');
      const transformedContent = this.transformSQL(content);

      // Write the output file
      await fs.writeFile(outputPath, transformedContent, 'utf8');
      console.log(
        `💾 Wrote ${transformedContent.length.toLocaleString()} characters to output file`,
      );

      // Calculate total replacements
      this.stats.totalReplacements =
        this.stats.backslashEscapes + this.stats.frenchContractions;

      // Log statistics
      this.logStatistics(inputPath, outputPath);
    } catch (error) {
      console.error('❌ Error processing SQL file:', error);
      throw error;
    }
  }

  /**
   * Log transformation statistics
   */
  private logStatistics(inputPath: string, outputPath: string): void {
    console.log('\n📊 Transformation Statistics:');
    console.log('================================');
    console.log(`Input file:              ${inputPath}`);
    console.log(`Output file:             ${outputPath}`);
    console.log(
      `Lines processed:         ${this.stats.linesProcessed.toLocaleString()}`,
    );
    console.log(
      `INSERT statements:       ${this.stats.insertStatementsProcessed.toLocaleString()}`,
    );
    console.log(
      `File size before:        ${this.stats.fileSize.before.toLocaleString()} bytes`,
    );
    console.log(
      `File size after:         ${this.stats.fileSize.after.toLocaleString()} bytes`,
    );

    const sizeDiff = this.stats.fileSize.after - this.stats.fileSize.before;
    const sizeDiffSign = sizeDiff >= 0 ? '+' : '';
    console.log(
      `Size difference:         ${sizeDiffSign}${sizeDiff.toLocaleString()} bytes`,
    );

    console.log('');
    console.log('Transformations applied:');
    console.log(
      `  Backslash escapes:     ${this.stats.backslashEscapes.toLocaleString()} replacements (\\' → '')`,
    );
    console.log(
      `  French contractions:   ${this.stats.frenchContractions.toLocaleString()} replacements (d' → d'')`,
    );
    console.log(
      `  Total replacements:    ${this.stats.totalReplacements.toLocaleString()}`,
    );

    if (this.stats.totalReplacements > 0) {
      console.log('\n✅ SQL file successfully sanitized!');
      console.log('   The file is now ready for migration processing.');
    } else {
      console.log('\n⚠️  No transformations were needed.');
      console.log('   The file appears to already be properly formatted.');
    }
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): {
  inputFile: string;
  outputFile?: string;
  inPlace: boolean;
} {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
SQL Preprocessing Script for MySQL Dump Sanitization

This script fixes MySQL backslash escaping issues that cause parsing problems
in the migration system, particularly with French text containing contractions.

Usage:
  bun run clean-sql.ts <input-file.sql> [options]

Options:
  --output <file>     Specify output file (default: <input>-cleaned.sql)
  --in-place         Modify the input file directly (overwrites original)
  --help, -h         Show this help message

Examples:
  bun run clean-sql.ts mysqldump.sql
  bun run clean-sql.ts mysqldump.sql --output clean-dump.sql
  bun run clean-sql.ts mysqldump.sql --in-place

Transformations applied:
  • Replace MySQL backslash escaping (\\') with SQL standard ('')
  • Fix French contractions (d'analyse → d''analyse)
  • Handle possessives (l'équipement → l''équipement)
  • Process common patterns (s'applique → s''applique)
  • Preserve SQL structure and formatting

Context:
  This addresses parsing issues in BaseConverter.parseCSVValues() where MySQL
  dumps use backslash escaping instead of SQL standard escaping, causing the
  CSV parser to fail when processing INSERT statements with quoted strings
  containing single quotes.
•`);
    process.exit(0);
  }

  const inputFile = args[0];
  if (!inputFile) {
    console.error('❌ Error: Input file is required');
    console.error('   Use --help for usage information');
    process.exit(1);
  }

  let outputFile: string | undefined;
  let inPlace = false;

  // Parse options
  for (let i = 1; i < args.length; i++) {
    if (args[i] === '--output' && i + 1 < args.length) {
      outputFile = args[i + 1];
      i++; // Skip next argument
    } else if (args[i] === '--in-place') {
      inPlace = true;
    } else {
      console.error(`❌ Error: Unknown option '${args[i]}'`);
      console.error('   Use --help for usage information');
      process.exit(1);
    }
  }

  // Determine output file
  if (inPlace) {
    outputFile = inputFile;
  } else if (!outputFile) {
    const ext = path.extname(inputFile);
    const base = path.basename(inputFile, ext);
    const dir = path.dirname(inputFile);
    outputFile = path.join(dir, `${base}-cleaned${ext}`);
  }

  return { inputFile, outputFile, inPlace };
}

/**
 * Validate input file exists and is readable
 */
async function validateInputFile(filePath: string): Promise<void> {
  try {
    const stats = await fs.stat(filePath);
    if (!stats.isFile()) {
      throw new Error(`${filePath} is not a file`);
    }

    // Check if file is readable
    await fs.access(filePath, fs.constants.R_OK);

    console.log(
      `📁 Input file: ${filePath} (${stats.size.toLocaleString()} bytes)`,
    );
  } catch (error) {
    console.error(`❌ Error: Cannot access input file '${filePath}'`);
    if (error instanceof Error) {
      console.error(`   ${error.message}`);
    }
    process.exit(1);
  }
}

/**
 * Validate output directory is writable
 */
async function validateOutputPath(
  outputPath: string,
  inPlace: boolean,
): Promise<void> {
  const outputDir = path.dirname(outputPath);

  try {
    // Check if output directory exists and is writable
    await fs.access(outputDir, fs.constants.W_OK);

    if (!inPlace) {
      console.log(`📁 Output file: ${outputPath}`);
    }
  } catch (error) {
    console.error(`❌ Error: Cannot write to output directory '${outputDir}'`);
    if (error instanceof Error) {
      console.error(`   ${error.message}`);
    }
    process.exit(1);
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  console.log('🧹 SQL Preprocessing Script for MySQL Dump Sanitization');
  console.log('========================================================\n');

  try {
    const { inputFile, outputFile, inPlace } = parseArguments();

    // Validate input file
    await validateInputFile(inputFile);

    // Validate output path
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    await validateOutputPath(outputFile!, inPlace);

    // Warn about in-place modification
    if (inPlace) {
      console.log('⚠️  WARNING: --in-place will overwrite the original file!');
      console.log('   Make sure you have a backup if needed.\n');
    }

    // Create processor and run
    const processor = new SQLPreprocessor();
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    await processor.processSQLFile(inputFile, outputFile!);

    console.log('\n🎉 Processing completed successfully!');

    if (!inPlace) {
      console.log('\nNext steps:');
      console.log(`1. Review the cleaned file: ${outputFile}`);
      console.log(
        '2. Run your migration: bun run src/migration-scripts/orchestrator.ts',
      );
    }
  } catch (error) {
    console.error('\n💥 Processing failed:', error);
    process.exit(1);
  }
}

// Run the script - Check if this script is being run directly
const isMainModule =
  process.argv[1]?.includes('clean-sql.ts') ||
  process.argv[1]?.includes('clean-sql');

if (isMainModule) {
  main();
}
