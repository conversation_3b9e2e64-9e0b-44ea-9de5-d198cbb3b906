import { env } from '@/env';
import { APIV2Error } from '@/services/api-v2-error';
import { COOKIE_PREFIX } from '@rie/constants';
import ky, {
  type AfterResponseHook,
  type BeforeErrorHook,
  type BeforeRequestHook,
  type BeforeR<PERSON><PERSON>Hook,
  type KyInstance,
  type Options,
} from 'ky';

const isServer = typeof window === 'undefined';

const getServerSideAuthCookies = async () => {
  try {
    // Dynamically import cookies only on server
    const { cookies } = await import('next/headers');
    const cookieStore = await cookies();

    // Get all cookies and format them as a proper cookie header
    const authCookies = cookieStore
      .getAll()
      .filter((cookie) => cookie.name.startsWith(COOKIE_PREFIX))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    return authCookies || null;
  } catch (e) {
    console.error('Failed to access cookies:', e);
    return null;
  }
};

const beforeRequest: BeforeRequestHook[] = [
  async (request) => {
    if (isServer) {
      const cookieHeader = await getServerSideAuthCookies();
      if (cookieHeader) {
        request.headers.set('cookie', cookieHeader);
      }
    }
    return request;
  },
];

const beforeError: BeforeErrorHook[] = [
  async (error) => {
    const { response } = error;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    let errorData: any;
    let errorMessage = 'An error occurred';

    try {
      // Try to parse error response as JSON
      errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
    } catch (e) {
      // If JSON parsing fails, try to get text
      try {
        errorMessage = await response.text();
      } catch (textError) {
        // If text extraction fails, use status text
        errorMessage = response.statusText;
      }
    }

    return new APIV2Error(
      errorMessage,
      error.response,
      error.request,
      error.options,
      errorData,
    );
  },
];

const afterResponse: AfterResponseHook[] = [
  // Handle 401 responses by attempting session refresh before failing
  async (request, _options, response) => {
    if (response.status === 401) {
      try {
        const { authClient } = await import('@/lib/better-auth');
        const { data: session } = await authClient.getSession({
          query: { disableCookieCache: true },
        });

        if (session) {
          // Session is still valid, retry the original request
          return ky(request);
        }
      } catch (error) {
        console.warn('Session refresh failed:', error);
        // Re-throw the error to ensure the original promise from the API client rejects.
        throw error;
      }
      // If the session is invalid, do nothing. Let the original 401 propagate to beforeError.
      // The beforeError hook will then convert it to APIV2Error.
      return response;
    }
  },
];

const beforeRetry: BeforeRetryHook[] = [
  async ({ error, retryCount }) => {
    // If it's a 401 error, validate session before retry (both server and client)
    if (
      error.name === 'HTTPError' &&
      'response' in error &&
      error.response &&
      typeof error.response === 'object' &&
      'status' in error.response &&
      error.response.status === 401
    ) {
      try {
        const { authClient } = await import('@/lib/better-auth');
        const { data: session } = await authClient.getSession({
          query: { disableCookieCache: true },
        });

        if (!session) {
          // No valid session, stop retrying and throw original error
          throw error;
        }
      } catch (refreshError) {
        console.warn(
          `Retry ${retryCount}: Session refresh failed`,
          refreshError,
        );
        throw refreshError; // Stop retrying
      }
    }
  },
];

export const createApiClient = async () => {
  // Base configuration
  const config: Options = {
    prefixUrl: env.NEXT_PUBLIC_API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include' as const,
    retry: {
      limit: 3,
      methods: ['get', 'put', 'head', 'delete', 'options', 'trace'],
      statusCodes: [408, 413, 429, 500, 502, 503, 504],
      afterStatusCodes: [413, 429, 503], // Respect Retry-After header
      maxRetryAfter: 30000, // Max 30 seconds
    },
    timeout: 30000, // 30 second timeout
    hooks: {
      beforeRetry,
      beforeRequest,
      beforeError,
      afterResponse,
    },
  };

  if (isServer) {
    const cookie = await getServerSideAuthCookies();
    if (cookie) {
      config.headers = {
        ...config.headers,
        cookie,
      };
    }
  }

  return ky.extend(config);
};

// Singleton instance for client-side
let clientInstance: KyInstance | null = null;

// Get or create the API client
export const getApiClient = async () => {
  if (!isServer && clientInstance) {
    return clientInstance;
  }

  const client = await createApiClient();

  if (!isServer) {
    clientInstance = client;
  }

  return client;
};
