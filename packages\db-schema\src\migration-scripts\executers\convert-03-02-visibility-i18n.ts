import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { VisibilityI18nMigrationConverter } from '../converters/03-02-convert-visibility-i18n-inserts';

async function convertVisibilityI18nData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new VisibilityI18nMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertVisibilityI18nData().catch(console.error);
