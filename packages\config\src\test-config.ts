import { resolve } from 'node:path';
import { config } from 'dotenv';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { ConfigLive } from './index';

// Test-specific environment loading
export const loadTestEnvironment = () => {
  // Load test-specific .env files
  config({ path: resolve(process.cwd(), '.env.test') });
  config({ path: resolve(process.cwd(), '.env.test.local') });
  config({ path: resolve(__dirname, '../../../.env.test') }); // Loads from project root
};

export const TestConfigLayer = Layer.effectDiscard(
  Effect.sync(() => loadTestEnvironment()),
).pipe(Layer.provide(ConfigLive.Default));
