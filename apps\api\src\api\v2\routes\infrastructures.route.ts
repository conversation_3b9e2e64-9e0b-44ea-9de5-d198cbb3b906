import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InfrastructuresRuntime } from '@/infrastructure/runtimes/infrastructures.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  InfrastructureDataSchema,
  InfrastructureInputSchema,
  ResourceIdSchema,
  ResourceQuerySchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { InfrastructuresServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  permissionForResource,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllInfrastructuresRoute = describeRoute({
  description:
    'Lister toutes les infrastructures avec filtrage optimisé au niveau base de données. Les utilisateurs non authentifiés voient uniquement les infrastructures avec une visibilité "public". Les utilisateurs authentifiés voient les infrastructures selon leurs permissions.',
  operationId: 'getAllInfrastructures',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(InfrastructureDataSchema)),
        },
      },
      description:
        'Infrastructures retournées (publiques pour les utilisateurs non authentifiés, filtrées selon les permissions pour les utilisateurs authentifiés)',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const getInfrastructureByIdRoute = describeRoute({
  description:
    'Obtenir une infrastructure par ID. Les utilisateurs non authentifiés peuvent accéder aux infrastructures avec une visibilité "public". Les utilisateurs authentifiés utilisent le système de permissions hiérarchique.',
  operationId: 'getInfrastructureById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(
        Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
      ),
      description: 'Type de vue pour la ressource (detail ou edit)',
      example: 'detail',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale pour les traductions de réponse (ex: fr, en)',
      example: 'fr',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureDataSchema),
        },
      },
      description: 'Infrastructure trouvée',
    },
    403: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
              infrastructureId: Schema.String,
            }),
          ),
        },
      },
      description: 'Accès interdit - Infrastructure non publique',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const createInfrastructureRoute = describeRoute({
  description: 'Créer une infrastructure',
  operationId: 'createInfrastructure',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InfrastructureInputSchema),
        example: {
          guidId: 'guid_infra_123',
          typeId: 'infra_type_001',
          statusId: 'infra_status_001',
          website: 'https://example-infrastructure.com',
          isFeatured: false,
          visibilityId: 'visibility_001',
          translations: [
            {
              locale: 'en',
              name: 'Advanced Research Infrastructure',
              description:
                'State-of-the-art research facility for advanced studies',
              otherNames: 'ARI',
              acronyms: 'ARI',
            },
            {
              locale: 'fr',
              name: 'Infrastructure de Recherche Avancée',
              description:
                'Installation de recherche de pointe pour les études avancées',
              otherNames: 'IRA',
              acronyms: 'IRA',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureDataSchema),
        },
      },
      description: 'Infrastructure créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Clé étrangère non trouvée - Le type, statut ou visibilité n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const updateInfrastructureRoute = describeRoute({
  description: 'Mettre à jour une infrastructure',
  operationId: 'updateInfrastructure',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InfrastructureInputSchema),
        example: {
          guidId: 'guid_infra_updated_123',
          typeId: 'infra_type_002',
          statusId: 'infra_status_002',
          website: 'https://updated-infrastructure.com',
          is_featured: true,
          visibilityId: 'visibility_002',
          translations: [
            {
              locale: 'en',
              name: 'Updated Advanced Research Infrastructure',
              description: 'Updated state-of-the-art research facility',
              otherNames: 'Updated ARI',
              acronyms: 'UARI',
            },
            {
              locale: 'fr',
              name: 'Infrastructure de Recherche Avancée Mise à Jour',
              description: 'Installation de recherche de pointe mise à jour',
              otherNames: 'IRA Mise à Jour',
              acronyms: 'IRAMAJ',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureDataSchema),
        },
      },
      description: 'Infrastructure mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée ou clé étrangère non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const deleteInfrastructureRoute = describeRoute({
  description: 'Supprimer une infrastructure',
  operationId: 'deleteInfrastructure',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Infrastructure supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

const infrastructuresRoute = new Hono<{
  Variables: HonoVariables;
}>();

infrastructuresRoute.get('/', getAllInfrastructuresRoute, async (ctx) => {
  // For getAllInfrastructures, we allow both authenticated and unauthenticated access
  // Unauthenticated users get public infrastructures, authenticated users get filtered results
  const program = Effect.gen(function* () {
    const user = ctx.get('user');
    const infrastructureService = yield* InfrastructuresServiceLive;
    return yield* infrastructureService.getAllInfrastructures(user?.id);
  });

  const result = await InfrastructuresRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.get(
  '/:id',
  getInfrastructureByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('query', ResourceQuerySchema),
  async (ctx) => {
    const user = ctx.get('user');
    const id = ctx.req.param('id');
    const { view, locale } = ctx.req.valid('query');

    // Allow both authenticated and unauthenticated access
    // Unauthenticated users can access public infrastructures, authenticated users use permission system
    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.getInfrastructureById({
        id,
        userId: user?.id,
        view,
        locale,
      });
    });

    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.post(
  '/',
  createInfrastructureRoute,
  validator('json', InfrastructureInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.createInfrastructure({
        ...body,
        modifiedBy: user.id,
      });
    }).pipe(
      withPolicy(checkPermission('infrastructure:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.put(
  '/:id',
  updateInfrastructureRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator('json', InfrastructureInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.updateInfrastructure({
        id,
        infrastructure: {
          ...body,
          modifiedBy: user.id,
        },
      });
    }).pipe(
      withPolicy(permissionForResource('infrastructure', 'update', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.delete(
  '/:id',
  deleteInfrastructureRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.deleteInfrastructure(id);
    }).pipe(
      withPolicy(permissionForResource('infrastructure', 'delete', id)),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );

    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Infrastructure deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { infrastructuresRoute };
