import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { cn } from '@/lib/utils';
import type { SelectOption } from '@/types/common';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { MultiSelectNew } from '@/ui/multi-select-new';
import { type FieldValues, type Path, useFormContext } from 'react-hook-form';

type MultiselectNewFieldProps<TFieldData extends FieldValues> = {
  testId?: string;
  className?: string;
  fieldName: Path<TFieldData>;
  label: string;
  options: SelectOption[];
  placeholder?: string;
  required?: boolean;
  tooltip?: string;
};
export const MultiselectFieldNew = <TFieldData extends FieldValues>({
  testId,
  className,
  fieldName,
  label,
  options,
  placeholder,
  required,
  tooltip,
}: MultiselectNewFieldProps<TFieldData>) => {
  const { control } = useFormContext<TFieldData>();

  return (
    <FormField
      data-testid={testId}
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <LabelTooltip
            className={cn(className)}
            htmlFor={fieldName}
            label={label}
            required={required}
            tooltip={tooltip}
          />
          <FormControl>
            <MultiSelectNew
              id={field.name}
              onValueChange={field.onChange}
              options={options}
              placeholder={placeholder}
              defaultValue={field.value}
            />
          </FormControl>
          <FieldInfo>
            <FormMessage />
          </FieldInfo>
        </FormItem>
      )}
    />
  );
};
