/**
 * In-memory storage system for ID mappings used during database migration.
 * This replaces the file-based JSON storage to prevent race conditions when
 * multiple converters run in parallel.
 */

export interface IdMapping {
  [oldId: string]: string;
}

export interface TableMappings {
  [tableName: string]: IdMapping;
}

/**
 * Thread-safe in-memory storage for ID mappings.
 * Uses a singleton pattern to ensure all converters share the same storage instance.
 */
class IdMappingStorage {
  private static instance: IdMappingStorage;
  private mappings: Map<string, IdMapping> = new Map();
  private readonly locks: Map<string, Promise<void>> = new Map();

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the ID mapping storage.
   */
  public static getInstance(): IdMappingStorage {
    if (!IdMappingStorage.instance) {
      IdMappingStorage.instance = new IdMappingStorage();
    }
    return IdMappingStorage.instance;
  }

  /**
   * Store ID mappings for a specific table.
   * This method is thread-safe and handles concurrent access.
   *
   * @param tableName - The name of the table (MySQL table name)
   * @param mappings - The ID mappings to store
   */
  public async setTableMappings(
    tableName: string,
    mappings: IdMapping,
  ): Promise<void> {
    // Wait for any existing operation on this table to complete
    await this.waitForLock(tableName);

    // Create a lock for this operation
    const lockPromise = this.performSetOperation(tableName, mappings);
    this.locks.set(tableName, lockPromise);

    try {
      await lockPromise;
    } finally {
      // Clean up the lock
      this.locks.delete(tableName);
    }
  }

  /**
   * Retrieve ID mappings for a specific table.
   * This method is thread-safe and handles concurrent access.
   *
   * @param tableName - The name of the table (MySQL table name)
   * @returns The ID mappings for the table, or empty object if not found
   */
  public async getTableMappings(tableName: string): Promise<IdMapping> {
    // Wait for any existing write operation on this table to complete
    await this.waitForLock(tableName);

    // Return a copy of the mappings to prevent external modification
    const mappings = this.mappings.get(tableName);
    return mappings ? { ...mappings } : {};
  }

  /**
   * Get all stored mappings.
   * This method returns a deep copy to prevent external modification.
   *
   * @returns All table mappings
   */
  public async getAllMappings(): Promise<TableMappings> {
    // Wait for all locks to complete
    await Promise.all(this.locks.values());

    const allMappings: TableMappings = {};
    for (const [tableName, mappings] of Array.from(this.mappings.entries())) {
      allMappings[tableName] = { ...mappings };
    }
    return allMappings;
  }

  /**
   * Clear all stored mappings.
   * This is useful for testing or when starting a fresh migration.
   */
  public async clearAllMappings(): Promise<void> {
    // Wait for all locks to complete
    await Promise.all(this.locks.values());
    this.mappings.clear();
  }

  /**
   * Check if mappings exist for a specific table.
   *
   * @param tableName - The name of the table to check
   * @returns True if mappings exist for the table
   */
  public async hasTableMappings(tableName: string): Promise<boolean> {
    await this.waitForLock(tableName);
    return this.mappings.has(tableName);
  }

  /**
   * Get the number of tables with stored mappings.
   *
   * @returns The number of tables with mappings
   */
  public async getTableCount(): Promise<number> {
    await Promise.all(this.locks.values());
    return this.mappings.size;
  }

  /**
   * Wait for any existing lock on a table to complete.
   *
   * @param tableName - The name of the table
   */
  private async waitForLock(tableName: string): Promise<void> {
    const existingLock = this.locks.get(tableName);
    if (existingLock) {
      await existingLock;
    }
  }

  /**
   * Perform the actual set operation.
   * This is separated to make the locking mechanism cleaner.
   *
   * @param tableName - The name of the table
   * @param mappings - The mappings to store
   */
  private async performSetOperation(
    tableName: string,
    mappings: IdMapping,
  ): Promise<void> {
    // Merge with existing mappings if they exist
    const existingMappings = this.mappings.get(tableName) || {};
    const mergedMappings = { ...existingMappings, ...mappings };

    this.mappings.set(tableName, mergedMappings);
  }
}

/**
 * Get the singleton instance of the ID mapping storage.
 * This is the main export that should be used by converters.
 */
export const idMappingStorage = IdMappingStorage.getInstance();

/**
 * Convenience function to store ID mappings for a table.
 *
 * @param tableName - The MySQL table name
 * @param mappings - The ID mappings to store
 */
export async function storeTableIdMappings(
  tableName: string,
  mappings: IdMapping,
): Promise<void> {
  await idMappingStorage.setTableMappings(tableName, mappings);
}

/**
 * Convenience function to retrieve ID mappings for a table.
 *
 * @param tableName - The MySQL table name
 * @returns The ID mappings for the table, or empty object if not found
 */
export async function getTableIdMappings(
  tableName: string,
): Promise<IdMapping> {
  return await idMappingStorage.getTableMappings(tableName);
}

/**
 * Convenience function to clear all stored mappings.
 * This is useful for testing or when starting a fresh migration.
 */
export async function clearAllIdMappings(): Promise<void> {
  await idMappingStorage.clearAllMappings();
}
