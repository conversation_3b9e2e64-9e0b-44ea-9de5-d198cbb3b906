import type { ControlledListKey } from "@/types/common";

export const CONTROLLED_LIST_ENTITIES: Partial<Record<ControlledListKey, string>> = {
    building: 'buildings',
    campus: 'campuses',
    organisation: 'institutions',
    person: 'people',
    unit: 'units',
    establishment: 'institutions',
    local: 'rooms',
    supplier: 'vendors',
    fundingProjects: 'funding-projects',
    applicationSector: 'application-sectors',
    documentationCategory: 'documentation-categories',
    equipmentCategory: 'equipment-categories',
    equipmentStatus: 'equipment-statuses',
    equipmentType: 'equipment-types',
    establishmentType: 'establishment-types',
    excellenceCenter: 'excellence-centers',
    financingProjectType: 'financing-project-types',
    infrastructureStatus: 'infrastructure-statuses',
    infrastructureType: 'infrastructure-types',
    innovationLab: 'innovation-labs',
    jobType: 'job-types',
    numberType: 'number-types',
    roomCategory: 'roomCategories',
    purchasedEquipment: 'purchased-equipment',
    researchDomain: 'research-domains',
    technic: 'technics',
    unitType: 'unit-types',
    visibility: 'visibilities',
} as const;

// Type générique pour les résultats des listes contrôlées en vue select
export type ControlledListSelectResult = Array<{
    value: string;
    label: string;
}>;

// Type pour la réponse de l'API controlled-lists
export type ControlledListsResponse = Record<string, ControlledListSelectResult>;