import { AuthRuntime } from '@/runtime/auth.runtime';
import { AuthServiceLive } from '@/services/auth.service';
import { AccessTreeServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';

export const getUserSessionContext = async (userId: string) => {
  const getUserRolesEffect = Effect.gen(function* () {
    const authService = yield* AuthServiceLive;
    const accessTreeService = yield* AccessTreeServiceLive;

    const roles = yield* authService.getUserRoles(userId);
    const permissions = yield* accessTreeService.buildUserAccessTree(userId);

    return { roles, permissions };
  });

  return AuthRuntime.runPromise(getUserRolesEffect);
};
