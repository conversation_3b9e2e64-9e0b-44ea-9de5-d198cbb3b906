/**
 * BaseConverter Tests - Part 2: Column and CREATE TABLE parsing
 *
 * Tests for extractColumnNames and extractCreateStatement methods
 */

import { beforeEach, describe, expect, test } from 'vitest';
import {
  BATIMENT_CREATE_TABLE,
  CATEGORIE_EQUIPEMENT_CREATE_TABLE,
  COMPLEX_CREATE_TABLE,
  EXPECTED_COLUMNS,
  FULL_SQL_SAMPLE,
} from './test-data';
import { TestableBaseConverter } from './testable-base-converter';

describe('BaseConverter Critical Parsing Methods - Part 2', () => {
  let converter: TestableBaseConverter;

  beforeEach(() => {
    converter = new TestableBaseConverter();
  });

  describe('5. extractColumnNames - Column name extraction from CREATE TABLE', () => {
    test('should extract column names from batiment table', () => {
      const result = converter.testExtractColumnNames(BATIMENT_CREATE_TABLE);
      expect(result).toEqual(EXPECTED_COLUMNS.batiment);
    });

    test('should extract column names from categorie_equipement table', () => {
      const result = converter.testExtractColumnNames(
        CATEGORIE_EQUIPEMENT_CREATE_TABLE,
      );
      expect(result).toEqual(EXPECTED_COLUMNS.categorie_equipement);
    });

    test('should handle complex column definitions', () => {
      const result = converter.testExtractColumnNames(COMPLEX_CREATE_TABLE);
      expect(result).toEqual([
        'id',
        'enum_field',
        'text_field',
        'decimal_field',
      ]);
    });

    test('should handle columns with backticks', () => {
      const createTable = `CREATE TABLE \`test\` (
        \`id\` int(11) NOT NULL,
        \`name\` varchar(255) DEFAULT NULL,
        \`created_at\` timestamp DEFAULT CURRENT_TIMESTAMP
      );`;

      const result = converter.testExtractColumnNames(createTable);
      expect(result).toEqual(['id', 'name', 'created_at']);
    });

    test('should handle columns without backticks', () => {
      const createTable = `CREATE TABLE test (
        id int(11) NOT NULL,
        name varchar(255) DEFAULT NULL,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP
      );`;

      const result = converter.testExtractColumnNames(createTable);
      expect(result).toEqual(['id', 'name', 'created_at']);
    });

    test('should handle mixed quote styles', () => {
      const createTable = `CREATE TABLE test (
        \`id\` int(11) NOT NULL,
        "name" varchar(255) DEFAULT NULL,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP
      );`;

      const result = converter.testExtractColumnNames(createTable);
      // Note: The implementation only removes backticks, not double quotes
      expect(result).toEqual(['id', '"name"', 'created_at']);
    });

    test('should handle columns with constraints and keys', () => {
      const createTable = `CREATE TABLE test (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`name\` varchar(255) DEFAULT NULL,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`name\` (\`name\`),
        KEY \`idx_name\` (\`name\`)
      );`;

      const result = converter.testExtractColumnNames(createTable);
      expect(result).toEqual(['id', 'name']);
    });

    test('should handle foreign key constraints', () => {
      const createTable = `CREATE TABLE test (
        \`id\` int(11) NOT NULL,
        \`parent_id\` int(11) DEFAULT NULL,
        CONSTRAINT \`fk_parent\` FOREIGN KEY (\`parent_id\`) REFERENCES \`parent\` (\`id\`)
      );`;

      const result = converter.testExtractColumnNames(createTable);
      expect(result).toEqual(['id', 'parent_id']);
    });

    test('should throw error for empty CREATE TABLE statement', () => {
      expect(() => {
        converter.testExtractColumnNames('');
      }).toThrow(
        'Could not find table name and opening parenthesis in CREATE TABLE statement',
      );
    });

    test('should throw error for malformed CREATE TABLE statement', () => {
      expect(() => {
        converter.testExtractColumnNames('CREATE TABLE test');
      }).toThrow(
        'Could not find table name and opening parenthesis in CREATE TABLE statement',
      );
    });
  });

  describe('6. extractCreateStatement - CREATE TABLE statement extraction', () => {
    test('should extract CREATE TABLE statement for batiment', () => {
      const result = converter.testExtractCreateStatement(
        FULL_SQL_SAMPLE,
        'batiment',
      );
      expect(result).toContain('CREATE TABLE `batiment`');
      expect(result).toContain('`id` smallint(6) NOT NULL AUTO_INCREMENT');
      expect(result).toContain('PRIMARY KEY (`id`)');
    });

    test('should extract CREATE TABLE statement for batiment_trad', () => {
      const result = converter.testExtractCreateStatement(
        FULL_SQL_SAMPLE,
        'batiment_trad',
      );
      expect(result).toContain('CREATE TABLE `batiment_trad`');
      expect(result).toContain('`data_id` smallint(6) NOT NULL');
    });

    test('should throw error for non-existent table', () => {
      expect(() => {
        converter.testExtractCreateStatement(FULL_SQL_SAMPLE, 'nonexistent');
      }).toThrow(
        'Could not find CREATE TABLE statement for table: nonexistent',
      );
    });

    test('should handle table names with different quote styles', () => {
      const sqlContent = `
        CREATE TABLE test (id int);
        CREATE TABLE \`quoted_test\` (id int);
        CREATE TABLE "double_quoted" (id int);
      `;

      const result1 = converter.testExtractCreateStatement(sqlContent, 'test');
      expect(result1).toContain('CREATE TABLE test');

      const result2 = converter.testExtractCreateStatement(
        sqlContent,
        'quoted_test',
      );
      expect(result2).toContain('CREATE TABLE `quoted_test`');

      const result3 = converter.testExtractCreateStatement(
        sqlContent,
        'double_quoted',
      );
      expect(result3).toContain('CREATE TABLE "double_quoted"');
    });

    test('should handle case-insensitive table names', () => {
      const sqlContent = `
        CREATE TABLE \`Test\` (id int);
        CREATE TABLE \`TEST\` (id int);
        CREATE TABLE \`test\` (id int);
      `;

      const result = converter.testExtractCreateStatement(sqlContent, 'test');
      expect(result).toContain('CREATE TABLE');
      // Should find one of them (implementation dependent)
      expect(result.length).toBeGreaterThan(0);
    });

    test('should handle multi-line CREATE TABLE statements', () => {
      const sqlContent = `
        CREATE TABLE \`complex_table\` (
          \`id\` int(11) NOT NULL AUTO_INCREMENT,
          \`name\` varchar(255) DEFAULT NULL,
          \`description\` text,
          PRIMARY KEY (\`id\`),
          KEY \`idx_name\` (\`name\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
      `;

      const result = converter.testExtractCreateStatement(
        sqlContent,
        'complex_table',
      );
      expect(result).toContain('CREATE TABLE `complex_table`');
      expect(result).toContain('ENGINE=InnoDB');
    });

    test('should throw error for CREATE TABLE with IF NOT EXISTS (not supported by current regex)', () => {
      const sqlContent = `
        CREATE TABLE IF NOT EXISTS \`test\` (
          \`id\` int(11) NOT NULL
        );
      `;

      // The current regex doesn't support IF NOT EXISTS syntax
      expect(() => {
        converter.testExtractCreateStatement(sqlContent, 'test');
      }).toThrow('Could not find CREATE TABLE statement for table: test');
    });

    test('should throw error for empty SQL content', () => {
      expect(() => {
        converter.testExtractCreateStatement('', 'test');
      }).toThrow('Could not find CREATE TABLE statement for table: test');
    });
  });
});
