'use client';

import { fundingProjectsColumns } from '@/app/[locale]/bottin/projets-financement/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/funding-projects';
import type { SupportedLocale } from '@/types/locale';
import type { FundingProjectList } from '@rie/domain/types';

type FundingProjectsListProps = {
  locale: SupportedLocale;
};

export const FundingProjectsList = ({ locale }: FundingProjectsListProps) => {
  return (
    <DirectoryList<
      'fundingProjects',
      'list',
      FundingProjectList,
      Record<string, unknown>
    >
      directoryListKey="fundingProjects"
      view="list"
      locale={locale}
      columns={fundingProjectsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="funding-projects"
    />
  );
};
