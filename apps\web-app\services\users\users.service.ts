import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type { DbUser } from '@rie/db-schema/entity-types';

export const getAllUsers = async () => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.get<DbUser[]>('v2/users').json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to get users');
  }
};

export const getUserById = async (userId: string) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.get<DbUser>(`v2/users/${userId}`).json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to get user');
  }
};

// TODO: review this type when handling user roles
export const assignRoleToUser = async (data: {
  userId: string;
  roleId: string;
  resourceType: string | null;
  resourceId: string | null;
}) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.post(`v2/users/${data.userId}/roles`, {
      json: data,
    });
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to assign role to user');
  }
};

export const removeRoleFromUser = async (data: {
  userId: string;
  roleId: string;
}) => {
  try {
    const apiClient = await getApiClient();
    return await apiClient.delete(
      `v2/users/${data.userId}/roles/${data.roleId}`,
    );
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to remove role from user');
  }
};
