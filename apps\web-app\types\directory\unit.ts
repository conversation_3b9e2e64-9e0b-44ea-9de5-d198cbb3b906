import type { unitFormSections } from '@/constants/directory/unit';
import type { EntityLocaleDictionary } from '@/types/common';
import type { UnitFull } from '@/types/controlled-list';
import type { IdType } from '@/types/directory/project';

export type Unit = Omit<
  UnitFull,
  'descriptions' | 'typeContenu' | 'typeEtablissement'
>; //TODO: verifier si on les garde

export type UnitFormSectionKey = keyof typeof unitFormSections;

export type UnitPostPayload = {
  acronyms: EntityLocaleDictionary;
  parent?: IdType;
  descriptions?: EntityLocaleDictionary;
  names: EntityLocaleDictionary;
  pseudonymes: EntityLocaleDictionary;
  typeUnite: IdType;
  organizationId: number;
};
