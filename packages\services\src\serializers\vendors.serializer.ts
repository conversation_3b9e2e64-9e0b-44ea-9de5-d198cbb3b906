import {
  type VendorDetailResponseDTO,
  VendorDetailResponseDtoSchema,
  type VendorEditResponseDTO,
  VendorEditResponseDtoSchema,
  type VendorListItemDTO,
  VendorListItemDtoSchema,
  type SelectOptionDTO,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import type { Vendor } from '@rie/domain/aggregates';
import { DbVendorDataSchema } from '@rie/domain/schemas';
import type { VendorData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes a Vendor aggregate into the shape required for a list view.
 *
 * Uses the Vendor aggregate's composite translation logic to provide intelligent
 * locale fallback behavior, ensuring the best available translation is selected
 * for each field based on the requested locale and fallback preferences.
 */
export function serializeVendorToListItem(
  vendor: Vendor,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<VendorListItemDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbVendorDataSchema,
    VendorListItemDtoSchema,
    {
      strict: true,
      decode: (vendorData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Vendor aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = vendor.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            return {
              id: vendorData.id,
              name: compositeTranslations.name.value || vendorData.id,
              dateEnd: vendorData.endDate,
              lastUpdatedAt: vendorData.updatedAt,
              startDate: vendorData.startDate,
              endDate: vendorData.endDate,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              vendorData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize vendor to list view',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    vendor.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Vendor List] Failed for vendor id=${vendor.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes a Vendor aggregate into the shape required for a detailed view.
 *
 * Uses the Vendor aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for all translatable fields. The resulting DTO
 * contains the best available translation for each field based on the requested
 * locale and fallback preferences, formatted for detailed view consumption.
 */
export function serializeVendorToDetail(
  vendor: Vendor,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<VendorDetailResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbVendorDataSchema, // Source
    VendorDetailResponseDtoSchema, // Target
    {
      strict: true,
      decode: (vendorData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Vendor aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = vendor.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            /** We are forced to cast `compositeTranslations.name.value` as string
             * because in the database it is defined as string | null.
             * However, we have defined a rule that states that at least one translation must have a name.
             * Therefore, we can safely cast to string.
             **/
            const safeName = compositeTranslations.name.value || vendorData.id;

            return {
              id: vendorData.id,
              isActive: vendorData.isActive ?? true,
              startDate: vendorData.startDate,
              endDate: vendorData.endDate,
              translations: {
                name: {
                  locale: compositeTranslations.name.locale,
                  value: safeName,
                },
                website: compositeTranslations.website,
                description: compositeTranslations.description,
                otherNames: compositeTranslations.otherNames,
              },
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              vendorData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize vendor to detail view',
            );
          },
        });
      },

      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    vendor.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Vendor Detail] Failed for vendor id=${vendor.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes a Vendor aggregate into a simple value/label pair for select inputs.
 *
 * Uses the Vendor aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for the label field. This ensures that select options
 * display the best available vendor name based on the requested locale and fallback
 * preferences, providing a consistent user experience across different languages.
 */
export function serializeVendorToSelectOption(
  vendor: Vendor,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbVendorDataSchema,
    SelectOptionDtoSchema,
    {
      strict: true,
      decode: (vendorData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Vendor aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = vendor.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            return {
              value: vendorData.id,
              label: compositeTranslations.name.value || vendorData.id,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              vendorData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize vendor to select option',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    vendor.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Vendor Select] Failed for vendor id=${vendor.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes database vendor data into the shape required for an edit form.
 *
 * This function transforms raw database vendor data (including all translations)
 * into the format expected by the frontend edit form. Unlike other serializers
 * that work with Vendor aggregates and provide locale-based fallback behavior,
 * this function works directly with database data and preserves all available
 * translations for editing purposes.
 *
 * The resulting DTO contains all vendor fields plus an array of all available
 * translations, allowing the edit form to display and modify translations
 * in multiple locales simultaneously.
 */
export function serializeVendorToFormEdit(
  vendorData: VendorData,
): Effect.Effect<VendorEditResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbVendorDataSchema,
    VendorEditResponseDtoSchema,
    {
      strict: true,
      decode: (data, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Transform database translations to the format expected by the edit form
            const transformedTranslations = data.translations.map(
              (translation) => ({
                locale: translation.locale,
                name: translation.name || '',
                website: translation.website || undefined,
                description: translation.description || undefined,
                otherNames: translation.otherNames || undefined,
              }),
            );

            return {
              id: data.id,
              startDate: data.startDate,
              endDate: data.endDate,
              translations: transformedTranslations,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              data,
              error instanceof Error
                ? error.message
                : 'Failed to serialize vendor to edit form',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return Schema.decode(transformer)(vendorData);
}
