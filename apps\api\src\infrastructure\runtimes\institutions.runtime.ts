import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  InfrastructuresRepositoryLive,
  InstitutionsRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
  EquipmentsRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  InstitutionsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const InstitutionsServicesLayer = Layer.mergeAll(
  InstitutionsRepositoryLive.Default,
  InstitutionsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const InstitutionsRuntime = ManagedRuntime.make(
  InstitutionsServicesLayer,
);
