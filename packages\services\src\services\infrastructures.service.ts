import {
  InfrastructureForbiddenError,
  InfrastructureNotFoundError,
} from '@rie/domain/errors';
import type { InfrastructureInputSchema } from '@rie/domain/schemas';
import { isInfrastructurePubliclyAccessible } from '@rie/domain/services';
import type { ResourceViewType } from '@rie/domain/types';
import { InfrastructuresRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type InfrastructureInput = Schema.Schema.Type<typeof InfrastructureInputSchema>;

export class InfrastructuresServiceLive extends Effect.Service<InfrastructuresServiceLive>()(
  'InfrastructuresServiceLive',
  {
    dependencies: [InfrastructuresRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllInfrastructures = (userId?: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const infrastructuresRepository =
            yield* InfrastructuresRepositoryLive;

          // If no user is provided (unauthenticated), use optimized database-level filtering
          // for public infrastructures only
          const infrastructures = userId
            ? yield* infrastructuresRepository.findAllInfrastructures()
            : yield* infrastructuresRepository.findAllPublicInfrastructures();

          const t1 = performance.now();
          console.log(
            `Call to getAllInfrastructures (${userId ? 'authenticated' : 'unauthenticated'}) took ${t1 - t0} ms.`,
          );
          return infrastructures;
        });
      };

      const getInfrastructureById = (params: {
        id: string;
        userId?: string;
        view: ResourceViewType;
        locale?: string;
      }) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          // Add logging for debugging
          yield* Effect.logInfo(
            `Fetching infrastructure ${params.id} for user ${params.userId || 'anonymous'} with view ${params.view}`,
          );

          const repo = yield* InfrastructuresRepositoryLive;
          const infrastructure =
            params.view === 'detail'
              ? yield* repo.findInfrastructureByIdWithRelatedEquipments(
                  params.id,
                )
              : yield* repo.findInfrastructureById(params.id);

          if (!infrastructure) {
            yield* Effect.logWarning(
              `Infrastructure ${params.id} not found in database`,
            );
            return yield* Effect.fail(
              new InfrastructureNotFoundError({ id: params.id }),
            );
          }

          // If no user is provided (unauthenticated), check infrastructure visibility
          if (!params.userId) {
            const isPublic = isInfrastructurePubliclyAccessible(infrastructure);
            yield* Effect.logInfo(
              `Infrastructure ${params.id} public access check: ${isPublic}`,
            );

            if (!isPublic) {
              return yield* Effect.fail(
                new InfrastructureForbiddenError({
                  id: params.id,
                  reason: 'Infrastructure is not publicly accessible',
                }),
              );
            }
          }

          const t1 = performance.now();
          yield* Effect.logInfo(
            `Successfully fetched infrastructure ${params.id} in ${t1 - t0}ms`,
          );
          console.log(
            `Call to getInfrastructureById (${params.userId ? 'authenticated' : 'unauthenticated'}) took ${t1 - t0} ms.`,
          );

          // For now, return the raw infrastructure data regardless of view
          // TODO: Implement view-specific serialization like in UnitsService
          return infrastructure;
        });
      };

      const createInfrastructure = (infrastructure: InfrastructureInput) =>
        Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          return yield* repo.createInfrastructure({ infrastructure });
        });

      const updateInfrastructure = ({
        id,
        infrastructure,
      }: { infrastructure: InfrastructureInput; id: string }) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          const existingInfrastructure = yield* repo.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(new InfrastructureNotFoundError({ id }));
          }
          const updatedInfrastructure = yield* repo.updateInfrastructure({
            infrastructureId: id,
            infrastructure,
          });

          return updatedInfrastructure;
        });
      };

      const deleteInfrastructure = (id: string) => {
        return Effect.gen(function* () {
          const repo = yield* InfrastructuresRepositoryLive;
          const existingInfrastructure = yield* repo.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(new InfrastructureNotFoundError({ id }));
          }
          const result = yield* repo.deleteInfrastructure(id);
          return result.length > 0;
        });
      };

      return {
        getAllInfrastructures,
        getInfrastructureById,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) {}
