import { FundingProjectsList } from '@/app/[locale]/bottin/projets-financement/list-funding-projects';
import { getQueryClientOptions } from '@/constants/query-client';
import { getMultipleControlledListsSelectOptions } from '@/hooks/controlled-list/controlled-list.options';
import { getDirectoryListOptions } from '@/hooks/directory/directory-list.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function FundingProjectsPage({ params }: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/bottin/projets-financement' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  const controlledLists: ControlledListKey[] = [
    'person',
    'financingProjectType',
    'purchasedEquipment',
  ];

  await Promise.all([
    await queryClient.prefetchQuery(
      getDirectoryListOptions({
        directoryListKey: 'fundingProjects',
        locale,
        view: 'list',
      }),
    ),
    queryClient.prefetchQuery(
      getMultipleControlledListsSelectOptions({
        controlledListKeys: controlledLists,
        locale,
      }),
    ),
    // ...controlledLists.map((controlledListKey) =>
    //   queryClient.prefetchQuery(
    //     controlledListsOptions(controlledListKey, locale),
    //   ),
    // ),

    //TODO: Fetch infrastructures with the new endpoint
    // queryClient.prefetchQuery(
    //   allInfrastructuresOptions({
    //     params: defaultRieServiceParams(locale),
    //     queryParams: '',
    //   }),
    // ),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <FundingProjectsList locale={locale} />
    </HydrationBoundary>
  );
}
