'use client';

import { RoleForm } from '@/app/[locale]/admin/roles/(form)/role-form';
import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form-schema';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import {
  useGetRoleById,
  useUpdateRole,
} from '@/hooks/admin/permissions/roles.hook';
import type { RoleDetailForm } from '@rie/domain/types';

interface EditRoleProps {
  id: string;
}

// Transform RoleDetailForm to RoleFormSchema
const transformRoleDetailFormToFormSchema = (
  role: RoleDetailForm,
): RoleFormSchema => {
  return {
    name: role.name,
    description: role.description || '',
    directPermissions: role.directPermissions,
    permissionGroups: role.permissionGroups,
    parentRoles: role.parentRoles,
  };
};

export const EditRole = ({ id }: EditRoleProps) => {
  const { data: role, isLoading, error } = useGetRoleById(id, { view: 'edit' });
  const { mutate: updateRole, status } = useUpdateRole();

  if (isLoading) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error.message}</div>;
  }

  if (!role) {
    return <div className="p-4 text-red-500">Role not found</div>;
  }

  const handleOnSubmit = (data: RoleFormSchema) => {
    updateRole({ payload: data, id });
  };

  const initialData = transformRoleDetailFormToFormSchema(role);

  return (
    <RoleForm
      initialData={initialData}
      onSubmitAction={handleOnSubmit}
      status={status}
    />
  );
};
