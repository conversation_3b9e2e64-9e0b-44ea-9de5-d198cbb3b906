import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { InstitutionTypeMigrationConverter } from '../converters/12-01-convert-institution-type-inserts';

async function convertInstitutionTypeData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new InstitutionTypeMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertInstitutionTypeData().catch(console.error);
