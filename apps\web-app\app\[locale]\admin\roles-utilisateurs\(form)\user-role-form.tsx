'use client';

import {
  type UserRoleFormSchema,
  userRoleFormSchema,
} from '@/app/[locale]/admin/roles-utilisateurs/(form)/user-role-form.schema';
import { ComboboxField } from '@/components/form-fields/combobox-field';
import { useToast } from '@/components/hooks/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import type { SelectOption } from '@/types/common';
import { effectTsResolver } from '@hookform/resolvers/effect-ts';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const availableUsers = [
  { value: '1', label: '<PERSON>, <EMAIL>' },
  { value: '2', label: '<PERSON>, <EMAIL>' },
  { value: '3', label: '<PERSON>, <EMAIL>' },
  { value: '4', label: '<PERSON>, <EMAIL>' },
  { value: '5', label: '<PERSON>, micha<PERSON>.w<PERSON><EMAIL>' },
  { value: '6', label: '<PERSON>, <EMAIL>' },
  { value: '7', label: 'David Miller, <EMAIL>' },
  { value: '8', label: 'Jennifer Taylor, <EMAIL>' },
];

// Mock data for roles
const availableRoles = [
  { value: '1', label: 'User', description: 'Basic user role' },
  { value: '2', label: 'Editor', description: 'Can edit content' },
  { value: '3', label: 'Manager', description: 'Can manage resources' },
  { value: '4', label: 'Admin', description: 'Administrative access' },
  { value: '5', label: 'Super Admin', description: 'Complete system access' },
  { value: '6', label: 'Viewer', description: 'Read-only access' },
  { value: '7', label: 'Contributor', description: 'Can contribute content' },
  { value: '8', label: 'Analyst', description: 'Can analyze data' },
];

// Mock data for context types
const resourceTypes: SelectOption[] = [
  { value: 'none', label: 'None' },
  { value: 'institution', label: 'Institution' },
  { value: 'unit', label: 'Unit' },
  { value: 'department', label: 'Department' },
  { value: 'team', label: 'Team' },
  { value: 'project', label: 'Project' },
] as const;

type DomainKey = (typeof resourceTypes)[number]['value'];

// Mock data for context IDs by context type
const resourceIdsByType: Record<DomainKey, SelectOption[]> = {
  none: [],
  institution: [
    { value: '1', label: 'University of Example' },
    { value: '2', label: 'Example College' },
    { value: '3', label: 'Institute of Technology' },
  ],
  unit: [
    { value: '1', label: 'Academic Affairs' },
    { value: '2', label: 'Student Services' },
    { value: '3', label: 'Research Division' },
    { value: '4', label: 'Administrative Services' },
  ],
  department: [
    { value: '1', label: 'Computer Science' },
    { value: '2', label: 'Engineering' },
    { value: '3', label: 'Business' },
    { value: '4', label: 'Arts and Humanities' },
    { value: '5', label: 'Natural Sciences' },
  ],
  team: [
    { value: '1', label: 'Development Team' },
    { value: '2', label: 'Marketing Team' },
    { value: '3', label: 'Support Team' },
    { value: '4', label: 'Research Team' },
    { value: '5', label: 'Design Team' },
  ],
  project: [
    { value: '1', label: 'Website Redesign' },
    { value: '2', label: 'Mobile App Development' },
    { value: '3', label: 'Research Initiative' },
    { value: '4', label: 'Marketing Campaign' },
    { value: '5', label: 'Infrastructure Upgrade' },
  ],
} as const;

interface PermissionFormProps {
  initialData?: UserRoleFormSchema;
}

export function UserRoleForm({ initialData }: PermissionFormProps = {}) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();

  const defaultValues: Partial<UserRoleFormSchema> = {
    user: initialData?.user ?? { value: '', label: '' },
    role: initialData?.role ?? { value: '', label: '' },
    resourceType: initialData?.resourceType ?? { value: 'none', label: 'None' },
    resourceId: initialData?.resourceId ?? { value: '', label: '' },
  };

  const form = useForm<UserRoleFormSchema>({
    resolver: effectTsResolver(userRoleFormSchema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { formState, watch } = form;
  const { isDirty, isValid } = formState;
  const resourceType = watch('resourceType');

  async function onSubmit(data: UserRoleFormSchema) {
    setIsSubmitting(true);

    try {
      // Here you would typically send the data to your API
      console.log('Form submitted:', data);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: 'User permission saved',
        description: 'Successfully saved user permission',
      });

      // If this is a new permission form, you might want to reset the form
      if (!initialData) {
        form.reset(defaultValues);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save permission group. Please try again.',
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {initialData ? 'Edit User Role' : 'Assign Role to user'}
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            {formState.errors.root && (
              <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                {formState.errors.root.message}
              </div>
            )}
            <ComboboxField
              clearErrorsOnChange={false}
              fieldLabel="User"
              fieldName="user"
              options={availableUsers}
              placeholder="Select a user"
            />
            <ComboboxField
              clearErrorsOnChange={true}
              fieldLabel="Role"
              fieldName="role"
              options={availableRoles}
              placeholder="Select a role"
            />
            {/*<FormField*/}
            {/*  control={form.control}*/}
            {/*  name="role"*/}
            {/*  render={({ field }) => (*/}
            {/*    <FormItem>*/}
            {/*      <LabelTooltip label="Group Description" />*/}
            {/*      <FormControl>*/}
            {/*        <MultiSelectNew*/}
            {/*          onValueChange={field.onChange}*/}
            {/*          options={availableRoles}*/}
            {/*          defaultValue={field.value}*/}
            {/*        />*/}
            {/*      </FormControl>*/}
            {/*      <FieldInfo>*/}
            {/*        <FormMessage />*/}
            {/*      </FieldInfo>*/}
            {/*    </FormItem>*/}
            {/*  )}*/}
            {/*/>*/}
            <ComboboxField
              clearErrorsOnChange={false}
              fieldLabel="Resource Type"
              fieldName="resourceType"
              options={resourceTypes}
              placeholder="Select a resource"
            />
            {resourceType.value && resourceType.value !== '' ? (
              <ComboboxField
                clearErrorsOnChange={false}
                fieldLabel="Resource Id"
                fieldName="resourceId"
                options={resourceIdsByType[resourceType.value as DomainKey]}
                placeholder={`Select a resource Id for ${resourceType.label}`}
              />
            ) : null}
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button
              type="submit"
              disabled={isSubmitting || (!isDirty && !initialData) || !isValid}
            >
              {isSubmitting
                ? 'Saving...'
                : initialData
                  ? 'Update Permission group'
                  : 'Create Permission group'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
