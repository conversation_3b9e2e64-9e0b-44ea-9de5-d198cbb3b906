import { handleEffectError } from '@/api/v2/utils/error-handler';
import { BuildingsRuntime } from '@/infrastructure/runtimes/buildings.runtime';
import type { HonoVariables } from '@/types/common.type';
import {
  BuildingDetailResponseDtoSchema,
  BuildingEditResponseDtoSchema,
  BuildingFormRequestDtoSchema,
  BuildingListItemDtoSchema,
  SelectOptionDtoSchema
} from '@rie/api-contracts';
import {
  BuildingSchema,
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema
} from '@rie/domain/schemas';
import { BuildingsServiceLive } from '@rie/services';
import {
  CurrentUser,
  checkPermission,
  withPolicy,
} from '@rie/services/policies';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

export const getAllbuildingsRoute = describeRoute({
  description: 'Lister tous les b�timents',
  operationId: 'getAllBuildings',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(BuildingListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Building retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildinges'],
});

export const getBuildingByIdRoute = describeRoute({
  description: 'Obtenir un building par ID',
  operationId: 'getBuildingById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du building (format CUID)',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue: detail ou edit',
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              BuildingDetailResponseDtoSchema,
              BuildingEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Building trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Building non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildinges'],
});

export const createBuildingRoute = describeRoute({
  description: 'Créer un building',
  operationId: 'createBuilding',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(BuildingFormRequestDtoSchema),
        example: {
          sadId: 'NEW_BUILDING_001',
          isActive: true,
          institution: {
            value: 'sydsapwdh5p2m9z9tvnde2dp',
            label: 'University of Example',
          },
          names: [
            {
              locale: 'en',
              value: 'New Main Building',
            },
            {
              locale: 'fr',
              value: 'Nouveau Building Principal',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(BuildingSchema),
        },
      },
      description: 'Building créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - L'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildinges'],
});

export const updateBuildingRoute = describeRoute({
  description: 'Mettre à jour un building',
  operationId: 'updateBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du building (format CUID)',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(BuildingFormRequestDtoSchema),
        example: {
          sadId: 'UPDATED_BUILDING_001',
          isActive: true,
          institution: {
            value: 'sydsapwdh5p2m9z9tvnde2dp',
            label: 'University of Example',
          },
          names: [
            {
              locale: 'en',
              value: 'Updated Main Building',
            },
            {
              locale: 'fr',
              value: 'Building Principal Mis à Jour',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(BuildingSchema),
        },
      },
      description: 'Building mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Building non trouvé ou l'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildinges'],
});

export const deleteBuildingRoute = describeRoute({
  description: 'Supprimer un b�timent',
  operationId: 'deleteBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du building (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Building supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Building non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildinges'],
});

const buildingsRoute = new Hono<{
  Variables: HonoVariables;
}>();

buildingsRoute.get(
  '/',
  getAllbuildingsRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');
    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      // Use appropriate service method based on view parameter
      if (view === 'select') {
        return yield* buildingService.getAllBuildingsSelect({ locale, fallbackLocale });
      }
      return yield* buildingService.getAllBuildings();
    }).pipe(
      withPolicy(checkPermission('building:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Buildings][GET /] Calling building service...');
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Buildings][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Buildings][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.get(
  '/:id',
  getBuildingByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');
    const { id } = ctx.req.valid('param');
    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.getBuildingById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('building:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Buildings][GET /:id] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Buildings][GET /:id] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Buildings][GET /:id] success');
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.post(
  '/',
  createBuildingRoute,
  // Debug incoming body before schema validation to investigate 400s  
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Buildings][POST] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Buildings][POST] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Buildings][POST] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Buildings][POST] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Buildings][POST] Calling building service...');
      const buildingService = yield* BuildingsServiceLive;
      console.log('[Route][Buildings][POST] building service obtained, calling createBuilding...');
      try {
        const result = yield* buildingService.createBuilding({
          buildingDto: body,
          userId: user.id,
        });
        console.log('[Route][Buildings][POST] Service call completed successfully');
        return result;
      } catch (error) {
        console.error('[Route][Buildings][POST] Service call failed:', error);
        throw error;
      }
    }).pipe(
      withPolicy(checkPermission('building:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Buildings][POST] Running program...');
    const result = await BuildingsRuntime.runPromiseExit(program);
    console.log('[Route][Buildings][POST] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Buildings][POST] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Buildings][POST] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Buildings][POST] Success! Returning result');
      return ctx.json(result.value, 201);
    }

    console.log('[Route][Buildings][POST] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.put(
  '/:id',
  updateBuildingRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  // Debug incoming body before schema validation to investigate 400s
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Buildings][PUT] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Buildings][PUT] Failed to read raw body', e);
    }
    await next();
  },
  // Note: Removed strict validation to support both API contract and client form formats
  // The service layer handles mapping for both payload types
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Buildings][PUT] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Buildings][PUT] Building ID:', id);
    console.log('[Route][Buildings][PUT] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Buildings][PUT] Calling building service...');
      const buildingService = yield* BuildingsServiceLive;
      console.log('[Route][Buildings][PUT] building service obtained, calling updateBuilding...');
      const result = yield* buildingService.updateBuilding({
        id,
        buildingDto: body,
        userId: user.id,
      });
      console.log('[Route][Buildings][PUT] Service call completed successfully');
      return result;
    }).pipe(
      withPolicy(checkPermission('building:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Buildings][PUT] Running program...');
    const result = await BuildingsRuntime.runPromiseExit(program);
    console.log('[Route][Buildings][PUT] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Buildings][PUT] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Buildings][PUT] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Buildings][PUT] Success! Returning result');
      return ctx.json(result.value);
    }

    console.log('[Route][Buildings][PUT] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.delete(
  '/:id',
  deleteBuildingRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.deleteBuilding(id);
    }).pipe(
      withPolicy(checkPermission('building:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Building deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { buildingsRoute };

