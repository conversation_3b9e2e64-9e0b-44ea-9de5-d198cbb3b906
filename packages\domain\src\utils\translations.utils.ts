// TODO: This should definitely be in app config table
import type { Locale } from '../types';

export const getFallbackLocale = (locale: Locale) => {
  return locale === 'fr' ? 'en' : 'fr';
};

export const getLocalizedField = <
  T extends { id: string; locale: string },
  K extends keyof Omit<T, 'id' | 'locale'>,
>(
  translations: ReadonlyArray<T>,
  field: K,
  locale: Locale,
): T[K] | null => {
  const fallbackLocale = getFallbackLocale(locale);
  const isValidValue = (value: T[K]): boolean => {
    return (
      value !== null &&
      value !== undefined &&
      (typeof value !== 'string' || value.trim() !== '')
    );
  };

  // Tier 1: Try primary locale first
  const primaryTranslation = translations.find((t) => t.locale === locale);
  if (primaryTranslation && isValidValue(primaryTranslation[field])) {
    return primaryTranslation[field];
  }

  // Tier 2: Try fallback locale if different from primary
  if (fallbackLocale !== locale) {
    const fallbackTranslation = translations.find(
      (t) => t.locale === fallbackLocale,
    );
    if (fallbackTranslation && isValidValue(fallbackTranslation[field])) {
      return fallbackTranslation[field];
    }
  }

  // Tier 3: Try any available translation with valid value
  for (const translation of translations) {
    if (
      translation.locale !== locale &&
      translation.locale !== fallbackLocale &&
      isValidValue(translation[field])
    ) {
      return translation[field];
    }
  }

  return null;
};
