import type { establishmentFormSections } from '@/constants/directory/establishment';
import type { EstablishmentFull } from '@/types/building';
import type { EntityLocaleDictionary } from '@/types/common';
import type { IdType } from '@/types/directory/project';

export type Establishment = Omit<
  EstablishmentFull,
  'descriptions' | 'typeContenu'
>; //TODO: check if we want these fields

export type EstablishmentFormSectionKey =
  keyof typeof establishmentFormSections;

export type EstablishmentPostPayload = {
  typeEtablissement: IdType;
  acronyms: EntityLocaleDictionary;
  names: EntityLocaleDictionary;
  pseudonym: EntityLocaleDictionary;
  descriptions?: EntityLocaleDictionary;
  affiliations?: {
    fonction: string;
    person: IdType;
    typeFonction: IdType;
  }[];
};
