import * as Schema from 'effect/Schema';
import {
  OptionalLocalizedFieldDtoSchema,
  SelectOptionDtoSchema,
} from '../common';

/**
a * Describes the entire request body for creating or updating a unit.
 * This is the shape of the data the client will send to the API.
 **/

export const UnitFormRequestDtoSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  isActive: Schema.Boolean,
  type: SelectOptionDtoSchema,
  parent: SelectOptionDtoSchema,
  names: Schema.Array(OptionalLocalizedFieldDtoSchema),
  description: Schema.Array(OptionalLocalizedFieldDtoSchema),
  acronyms: Schema.Array(OptionalLocalizedFieldDtoSchema),
  otherNames: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type UnitFormRequestDTO = Schema.Schema.Type<
  typeof UnitFormRequestDtoSchema
>;
