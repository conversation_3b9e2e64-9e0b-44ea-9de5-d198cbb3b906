import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UsersRuntime } from '@/infrastructure/runtimes/users.runtime';
import type { HonoVariables } from '@/types/common.type';
import { DbUserRoleInputSchema } from '@rie/db-schema/entity-schemas';
import {
  AccessCheckResponseSchema,
  AccessTreeResponseSchema,
  CheckUserAccessSchema,
  CheckUserPermissionSchema,
  DbUserDetailWithRolesSchema,
  ErrorResponseSchema,
  PermissionCheckResponseSchema,
  ResourceIdSchema,
  ResourceTypeSchema,
  SuccessResponseSchema,
  UserPermissionsForResourceResponseSchema,
  UserRoleAssignmentResponseSchema,
} from '@rie/domain/schemas';
import type { ResourceType } from '@rie/domain/types';
import { UserPermissionsServiceLive, UsersServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

const AssignRoleToUserSchema = DbUserRoleInputSchema.omit(
  'userId',
  'createdAt',
  'grantedBy',
);

// User Routes
export const getUserByIdRoute = describeRoute({
  description:
    'Obtient un utilisateur par ID avec ses rôles et informations personnelles',
  operationId: 'getUserById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(DbUserDetailWithRolesSchema),
        },
      },
      description: 'Utilisateur retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Utilisateur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

export const getUserPermissionsForResourceRoute = describeRoute({
  description:
    "Obtient toutes les permissions d'un utilisateur pour une ressource spécifique",
  operationId: 'getUserPermissionsForResource',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
    {
      name: 'resourceType',
      in: 'path',
      required: true,
      schema: resolver(ResourceTypeSchema),
      description: 'Type de ressource',
    },
    {
      name: 'resourceId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la ressource',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UserPermissionsForResourceResponseSchema),
        },
      },
      description: 'Permissions retournées avec succès',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur non trouvé ou accès refusé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

export const getUserAccessTreeRoute = describeRoute({
  description: "Obtient l'arbre d'accès complet d'un utilisateur",
  operationId: 'getUserAccessTree',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(AccessTreeResponseSchema),
        },
      },
      description: "Arbre d'accès retourné avec succès",
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

// User Permissions Routes
export const assignRoleToUserRoute = describeRoute({
  description: 'Assigne un rôle à un utilisateur avec contexte optionnel',
  operationId: 'assignRoleToUser',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(AssignRoleToUserSchema),
        example: {
          roleId: 'cm123456789abcdef',
          resourceType: 'institution',
          resourceId: 'cm987654321fedcba',
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(UserRoleAssignmentResponseSchema),
        },
      },
      description: 'Rôle assigné avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Données invalides',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur ou rôle non trouvé',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Rôle déjà assigné',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

export const removeRoleFromUserRoute = describeRoute({
  description: "Retire un rôle d'un utilisateur",
  operationId: 'removeRoleFromUser',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
    {
      name: 'roleId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du rôle à retirer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(SuccessResponseSchema),
        },
      },
      description: 'Rôle retiré avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Données invalides',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur, rôle ou assignation non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

export const checkUserPermissionRoute = describeRoute({
  description: 'Vérifie si un utilisateur a une permission spécifique',
  operationId: 'checkUserPermission',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CheckUserPermissionSchema),
        example: {
          userId: 'cm123456789abcdef',
          domain: 'equipment',
          action: 'read',
          resourceId: 'cm987654321fedcba',
          resourceType: 'infrastructure',
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PermissionCheckResponseSchema),
        },
      },
      description: 'Vérification de permission effectuée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Données invalides',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

export const checkUserAccessRoute = describeRoute({
  description: 'Vérifie si un utilisateur a accès à une ressource spécifique',
  operationId: 'checkUserAccess',
  parameters: [
    {
      name: 'userId',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'utilisateur",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CheckUserAccessSchema),
        example: {
          userId: 'cm123456789abcdef',
          resourceType: 'equipment',
          resourceId: 'cm987654321fedcba',
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(AccessCheckResponseSchema),
        },
      },
      description: "Vérification d'accès effectuée",
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Données invalides',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Utilisateur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(ErrorResponseSchema),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Utilisateurs'],
});

// Create the router
const usersRoute = new Hono<{ Variables: HonoVariables }>();

// Get a user by ID
usersRoute.get('/:id', getUserByIdRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const usersService = yield* UsersServiceLive;
    return yield* usersService.getUserById(id);
  });

  const maybeUser = await UsersRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, maybeUser);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(maybeUser)) {
    return ctx.json(maybeUser.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Assign role to user
usersRoute.post(
  '/:userId/roles',
  assignRoleToUserRoute,
  validator('json', AssignRoleToUserSchema),
  async (ctx) => {
    const userId = ctx.req.param('userId');
    const grantedBy = ctx.get('user')?.id;
    const { roleId, resourceType, resourceId } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;
      return yield* userPermissionsService.assignRoleToUser({
        userId,
        roleId,
        resourceType,
        resourceId,
        grantedBy,
      });
    });

    const result = await UsersRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Remove role from user
usersRoute.delete(
  '/:userId/roles/:roleId',
  removeRoleFromUserRoute,
  async (ctx) => {
    const userId = ctx.req.param('userId');
    const roleId = ctx.req.param('roleId');

    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;
      return yield* userPermissionsService.removeRoleFromUser({
        userId,
        roleId,
      });
    });

    const result = await UsersRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({
        success: result.value,
        message: result.value
          ? 'Role removed successfully'
          : 'Role removal failed',
      });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Check user permission
usersRoute.post(
  '/:userId/permissions/check',
  checkUserPermissionRoute,
  validator('json', CheckUserPermissionSchema),
  async (ctx) => {
    const userId = ctx.req.param('userId');
    const { domain, action, resourceId } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;
      const hasPermission = yield* userPermissionsService.userHasPermission({
        userId,
        domain,
        action,
        resourceId,
      });

      return {
        hasPermission,
        userId,
        domain,
        action,
        resourceId,
      };
    });

    const result = await UsersRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Check user access to resource
usersRoute.post(
  '/:userId/access/check',
  checkUserAccessRoute,
  validator('json', CheckUserAccessSchema),
  async (ctx) => {
    const userId = ctx.req.param('userId');
    const { resourceType, resourceId } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;
      const hasAccess = yield* userPermissionsService.userHasAccessToResource(
        userId,
        resourceType,
        resourceId,
      );

      return {
        hasAccess,
        userId,
        resourceType,
        resourceId,
      };
    });

    const result = await UsersRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Get user access tree
usersRoute.get('/:userId/access-tree', getUserAccessTreeRoute, async (ctx) => {
  const userId = ctx.req.param('userId');

  const program = Effect.gen(function* () {
    const userPermissionsService = yield* UserPermissionsServiceLive;
    return yield* userPermissionsService.getUserAccessTree(userId);
  });

  const result = await UsersRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Get user permissions for specific resource
usersRoute.get(
  '/:userId/permissions/:resourceType/:resourceId',
  getUserPermissionsForResourceRoute,
  async (ctx) => {
    const userId = ctx.req.param('userId');
    const resourceType = ctx.req.param('resourceType') as ResourceType;
    const resourceId = ctx.req.param('resourceId');

    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;
      const permissions =
        yield* userPermissionsService.getUserPermissionsForResource(
          userId,
          resourceType,
          resourceId,
        );

      return {
        permissions,
        userId,
        resourceType,
        resourceId,
      };
    });

    const result = await UsersRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export default usersRoute;
