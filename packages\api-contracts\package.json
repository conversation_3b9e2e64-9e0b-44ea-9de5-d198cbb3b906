{"name": "@rie/api-contracts", "version": "1.0.0", "type": "module", "description": "Database schema package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "dependencies": {"@rie/biome-config": "workspace:*"}, "peerDependencies": {"effect": "^3.17.13"}, "devDependencies": {"@types/bun": "^1.2.21", "@types/node": "^24.0.3", "typescript": "^5.9.2"}}