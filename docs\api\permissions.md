# Permissions System

This document describes both the comprehensive role-based access control (RBAC) and attribute-based access control (ABAC) systems used in the RIE API, including permission groups, roles, and context-aware authorization.

## 🎯 System Overview

The RIE permission system combines Role-Based Access Control (RBAC) with Attribute-Based Access Control (ABAC) elements and hierarchical resource access:

```text
Users → Roles → Permission Groups → Permissions
```

### Key Features

- **Context-Aware**: Permissions can be scoped to specific resources
- **Hierarchical**: Institution → Unit → Infrastructure → Equipment inheritance
- **Flexible**: Support for multiple permission assignment strategies
- **Performance**: Optimized with access tree caching
- **Secure**: Fail-safe defaults and comprehensive validation

### Core Principle

**Any permission group granting write access (`create`, `update`, `delete`) implicitly includes the necessary read permissions (`read`)**. This ensures users can always see what they are acting upon.

## 🏗️ Architecture Components

### 1. Permissions

Individual capabilities following the format `domain:action`:

- `equipment:read` - View equipment
- `equipment:create` - Create new equipment
- `infrastructure:manage` - Full infrastructure management

### 2. Permission Groups

Technical, reusable "keychains" that bundle related permissions:

- `CanViewEquipment` - Equipment viewing capabilities
- `EquipmentManagement` - Full equipment management
- `UserManagement` - Complete user administration

### 3. Roles

Business functions that combine permission groups:

- `Researcher` - Basic research capabilities
- `Lab Manager` - Lab-specific management
- `Administrator` - System-wide administration

### 4. Context

Resource-specific scope for permissions:

- **Institution**: University-wide access
- **Unit**: Department/faculty-specific access
- **Infrastructure**: Research infrastructure-specific access
- **Equipment**: Individual equipment access
- **None**: Global/system-wide access

## 📋 Permission Groups Catalog

### User Management Domain

| Permission Group | Description                  | Permissions          |
|:-----------------|:-----------------------------|:---------------------|
| `CanViewUsers`   | View, search, and sort users | `[19]`               |
| `CanCreateUser`  | Create new users             | `[6, 19]`            |
| `CanEditUser`    | Modify and deactivate users  | `[7, 11, 19]`        |
| `CanDeleteUser`  | Delete users                 | `[10, 19]`           |
| `UserManagement` | Full user management         | `[6, 7, 10, 11, 19]` |

### Equipment Management Domain

| Permission Group      | Description                | Permissions                                            |
|:----------------------|:---------------------------|:-------------------------------------------------------|
| `CanViewEquipment`    | View equipment and details | `[52, 54, 61, 64, 65, 66, 79]`                         |
| `CanCreateEquipment`  | Create new equipment       | `[35, 52, 54, 61, 64, 65, 66, 79]`                     |
| `CanEditEquipment`    | Modify equipment           | `[36, 37, 39, 40, 41, 42, 52, 54, 61, 64, 65, 66, 79]` |
| `CanDeleteEquipment`  | Delete equipment           | `[38, 52, 54, 61, 64, 65, 66, 79]`                     |
| `EquipmentManagement` | Full equipment management  | `[35, 36, 37, 38, 39, 40, 41, 42]`                     |

### Infrastructure Management Domain

| Permission Group           | Description                     | Permissions                                |
|:---------------------------|:--------------------------------|:-------------------------------------------|
| `CanViewInfrastructures`   | View infrastructures and export | `[52, 62, 78]`                             |
| `CanCreateInfrastructure`  | Create infrastructures          | `[27, 52, 62, 78]`                         |
| `CanEditInfrastructure`    | Modify infrastructures          | `[28, 29, 31, 32, 33, 34, 52, 62, 69, 78]` |
| `CanDeleteInfrastructure`  | Delete infrastructures          | `[30, 52, 62, 78]`                         |
| `InfrastructureManagement` | Full infrastructure management  | `[27, 28, 29, 30, 31, 32, 33, 34, 69]`     |

### Permission Group Management

| Permission Group                  | Description                     | Permissions |
|:----------------------------------|:--------------------------------|:------------|
| `CanViewPermissionGroups`         | View permission groups          | `[15, 19]`  |
| `CanManagePermissionGroupMembers` | Assign/remove users from groups | `[14]`      |

## 🎭 Role Definitions

### Standard Roles

#### User (Default Role)

- **Description**: Basic user with minimal permissions
- **Auto-assigned**: Yes (on user registration)
- **Permission Groups**: Basic viewing capabilities
- **Context**: Usually none or specific resources

#### Researcher

- **Description**: Research staff with equipment access
- **Permission Groups**:
  - `CanViewEquipment`
  - `CanViewInfrastructures`
- **Context**: Usually scoped to specific infrastructures

#### Lab Manager

- **Description**: Laboratory management role
- **Permission Groups**:
  - `EquipmentManagement`
  - `CanViewInfrastructures`
  - `CanEditInfrastructure`
- **Context**: Scoped to specific units or infrastructures

#### Administrator

- **Description**: System administrator with full access
- **Permission Groups**:
  - `UserManagement`
  - `EquipmentManagement`
  - `InfrastructureManagement`
  - `CanManagePermissionGroupMembers`
- **Context**: Institution-wide or none

## 🔄 Permission Checking Flow

### 1. Authentication

```typescript
// User authenticates and establishes a session
const session = await authService.login(credentials);
// Session is stored server-side and session cookie is set in browser
// Subsequent requests include session cookie automatically
```

### 2. Authorization Middleware

Our system uses two middleware components that work together to establish user context:

#### User Context Middleware (Applied to all routes)

```typescript
// userContextMiddleware - Sets user context for all requests
export const userContextMiddleware = async (ctx: Context, next: Next) => {
  const session = await auth.api.getSession({ headers: ctx.req.raw.headers });

  if (!session) {
    ctx.set('user', null);
    ctx.set('session', null);
    ctx.set('permissions', null);
    return next();
  }

  ctx.set('user', session.user);
  ctx.set('session', session.session);
  ctx.set('permissions', session.permissions);
  return next();
};
```

#### Auth Middleware (Applied to protected routes)

```typescript
// authMiddleware - Enforces authentication for protected routes
const authMiddleware = async (ctx: Context, next: Next) => {
  // Allow OPTIONS requests to pass through
  if (ctx.req.method === 'OPTIONS') {
    return next();
  }

  const userSession = await auth.api.getSession({
    headers: ctx.req.raw.headers,
  });

  if (!userSession?.session) {
    return new Response('Unauthorized', { status: 401 });
  }

  ctx.set('session', userSession.session);
  await next();
};
```

#### Middleware Application Order

```typescript
// In app.ts - middleware is applied in this order:
app.use('*', createUserContextMiddleware());  // Sets user context for all routes

app.use(
  '/api/*',
  except(
    ['/api/v2/auth/*'],  // Exclude auth routes from auth requirement
    createAuthMiddleware(),  // Require authentication for API routes
  ),
);
```

#### Route Handler Usage

```typescript
// In route handlers, user and session are available from context
const user = ctx.get('user');        // User object from session
const session = ctx.get('session');  // Session object
const permissions = ctx.get('permissions');  // User permissions (optional)

// Typical route pattern
if (!user || !session) {
  return ctx.json({ error: 'Unauthorized' }, 401);
}

// User and session are now available for permission checks
```

### 3. Fine-grained Permission Checks

```typescript
// Route-level permission validation using policies
const program = Effect.gen(function* () {
  const permissionService = yield* UserPermissionsServiceLive;
  return yield* permissionService.canUpdateEquipment(user.id, equipmentId);
}).pipe(
  withPolicy(permissionForResource('equipment', 'update', equipmentId)),
  Effect.provideService(CurrentUser, {
    sessionId: session.id,
    userId: UserId.make(user.id),
  })
);
```

### 4. Hierarchical Access Resolution

```typescript
// Check permissions with context inheritance
// Equipment → Infrastructure → Unit → Institution
const program = Effect.gen(function* () {
  const accessTreeService = yield* AccessTreeServiceLive;
  const accessTree = yield* accessTreeService.getUserAccessTree(user.id);
  return accessTree.hasPermission(permission, context);
});
```

## Permission Checking Execution Flow

This section documents the two-step verification used by the policy helper `permissionForResource()` and how it executes through the services.

### Dual verification: WHERE vs WHAT

- WHERE (hierarchical access): Can the user access this specific resource in the hierarchy?
  - Implemented by `userHasAccessToResource()` → `AccessTreeServiceLive.userHasAccessTo()` → `buildUserAccessTree()`
  - Admin users bypass this step (admins always pass the access check), but see note below about permissions
- WHAT (permission validation): Does the user possess the required `domain:action` permission through their roles?
  - Implemented inside `UserPermissionsServiceLive.userHasPermission()` by checking the user’s role permissions
  - Admin users do NOT automatically pass this step; the admin role must include the permission via permission groups

Note: Access ≠ Permission. Passing the access (WHERE) check does not grant the permission (WHAT). Both must pass to allow the operation.

### Execution call chain (from route to services)

- `withPolicy(permissionForResource('unit', 'delete', unitId))`
  - calls `permissionForResource(domain, action, resourceId)` (policy)
    - resolves `UserPermissionsServiceLive`
    - calls `userHasPermission({ userId, domain, action, resourceId })`
      - IF `resourceId` is present:
        - calls `userHasAccessToResource(userId, domain, resourceId)`
          - calls `AccessTreeServiceLive.userHasAccessTo(userId, resourceType, resourceId)`
            - if user has admin role → returns `true`
            - else calls `buildUserAccessTree(userId)` and checks the resource membership
      - IF access is granted (or no `resourceId` was provided):
        - checks that some user role has a permission matching `domain:action`
      - returns `true` only if both access (when applicable) and permission checks succeed

### Code example (route protection)

```ts
// Route handler pattern used in the API
const program = Effect.gen(function* () {
  const unitService = yield* UnitsServiceLive;
  return yield* unitService.deleteUnit(unitId);
}).pipe(
  withPolicy(permissionForResource('unit', 'delete', unitId)),
  Effect.provideService(CurrentUser, {
    sessionId: session.id,
    userId: UserId.make(user.id),
  }),
);
```

Internally, the policy resolves `UserPermissionsServiceLive` and executes:

```ts
// Pseudocode of userHasPermission with resource
if (resourceId) {
  const hasAccess = yield* userHasAccessToResource(userId, domain, resourceId);
  if (!hasAccess) return false; // WHERE failed
}

// WHAT: check role permissions
const user = yield* usersRepository.findUserPermissions(userId);
for (const ur of user.userRoles) {
  if (ur.role.rolePermissions?.some(rp => rp.permission.domain === domain && rp.permission.action === action)) {
    return true;
  }
}
return false;
```

### Why both steps are necessary

- **Prevent privilege escalation**: Users must be within the correct scope (WHERE) and also hold the specific capability (WHAT)
- **Preserve least-privilege**: Access scope alone cannot grant powerful operations like delete/update without the proper permission
- **Make admin behavior explicit**: Admin users bypass the WHERE check but must still possess the WHAT permission through their role's permission groups

### Practical scenarios

- Has access but lacks permission
  - User has an institution-level role that grants access to all units in that institution, but their role only includes read permissions. WHERE passes; WHAT fails (no `unit:delete`), operation denied.

- Has permission but no access
  - User’s role includes `unit:delete` but is scoped to another institution. WHAT could be satisfied in general, but WHERE fails for this specific `unitId`, operation denied.

### Where to check permissions (by layer)

- Middleware Level (coarse-grained): For entire route groups requiring general roles; stops unauthorized requests early
- Route Handler Level (resource-specific): Use `withPolicy(checkPermission(...))` or `withPolicy(permissionForResource(...))` for specific operations tied to route params
- Service Level (business logic): For complex, multi-resource scenarios inside services, call `userHasAccessToResource` / `userHasPermission` directly

See also: API Architecture → “Route protection with policies and two-step verification” (docs/architecture/api-architecture.md)

- Admin user
  - Admin passes WHERE automatically (access check is bypassed), but still needs the `unit:delete` permission through the admin role’s permission groups for WHAT to pass.

## 🏛️ Hierarchical Access Model

### Resource Hierarchy

```text
Institution (University)
├── Unit (Faculty/Department)
│   ├── Infrastructure (Research Lab)
│   │   ├── Equipment (Microscope)
│   │   └── Equipment (Centrifuge)
│   └── Infrastructure (Another Lab)
└── Unit (Another Department)
```

### Permission Inheritance

- **Institution-level** permissions apply to all child resources
- **Unit-level** permissions apply to all infrastructures and equipment within
- **Infrastructure-level** permissions apply to all equipment within
- **Equipment-level** permissions apply only to specific equipment

### Example Scenarios

#### Scenario 1: Lab Manager

```typescript
// User has "Lab Manager" role for "Biochemistry Lab" infrastructure
// Can manage all equipment within that lab
// Cannot access equipment in other labs
```

#### Scenario 2: Department Head

```typescript
// User has "Administrator" role for "Biology Department" unit
// Can manage all infrastructures and equipment within the department
// Cannot access resources in other departments
```

#### Scenario 3: University Administrator

```typescript
// User has "Administrator" role at institution level
// Can manage all resources across the entire university
```

## 🔧 Implementation Details

### Database Schema

#### Core Tables

```sql
-- Users and roles relationship with context
CREATE TABLE user_roles (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  role_id UUID REFERENCES roles(id),
  resource_type VARCHAR(50), -- 'institution', 'unit', 'infrastructure', 'equipment'
  resource_id UUID,          -- ID of the specific resource
  granted_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Role inheritance for hierarchical roles
CREATE TABLE role_inheritance (
  id UUID PRIMARY KEY,
  parent_role_id UUID REFERENCES roles(id),
  child_role_id UUID REFERENCES roles(id)
);

-- Permission groups for easier management
CREATE TABLE permission_groups (
  id UUID PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  description TEXT
);

-- Role to permission group assignments
CREATE TABLE role_permission_groups (
  role_id UUID REFERENCES roles(id),
  permission_group_id UUID REFERENCES permission_groups(id),
  PRIMARY KEY (role_id, permission_group_id)
);
```

### Service Implementation

#### UserPermissionsService

```typescript
export class UserPermissionsServiceLive implements UserPermissionsService {
  // Check if user has specific permission
  async hasPermission(
    userId: string,
    permission: string,
    context?: ResourceContext
  ): Promise<boolean> {
    const accessTree = await this.getAccessTree(userId)
    return accessTree.hasPermission(permission, context)
  }

  // Assign role to user with context
  async assignRole(
    userId: string,
    roleId: string,
    context?: ResourceContext,
    grantedBy?: string
  ): Promise<void> {
    // Implementation with validation and audit logging
  }

  // Get user's effective permissions
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    // Build complete permission set with context
  }
}
```

### API Integration

#### Route Protection

```typescript
// Protect entire route groups
app.use('/api/v2/admin/*', requireRole('Administrator'))

// Protect specific endpoints
app.use('/api/v2/equipment/*', requirePermission('equipment:read'))

// Context-aware protection
app.use('/api/v2/equipment/:id', requirePermissionForResource('equipment:update'))
```

#### Use Case Validation

```typescript
const updateEquipment = (id: string, data: UpdateData) =>
  Effect.gen(function* (_) {
    const { user, permissionService } = yield* _(Effect.service<Dependencies>())

    // Check permission with context
    const canUpdate = yield* _(
      permissionService.canUpdateEquipment(user.id, id)
    )

    if (!canUpdate) {
      yield* _(Effect.fail(new UnauthorizedError()))
    }

    // Proceed with update
  })
```

## 🧪 Testing Permissions

### Unit Tests

```typescript
describe('Permission System', () => {
  it('should grant access to lab manager for equipment in their lab', async () => {
    const user = await createTestUser()
    await assignRole(user.id, 'Lab Manager', {
      type: 'infrastructure',
      id: 'lab-1'
    })

    const hasAccess = await permissionService.canUpdateEquipment(
      user.id,
      'equipment-in-lab-1'
    )

    expect(hasAccess).toBe(true)
  })
})
```

### Integration Tests

```typescript
describe('Equipment API', () => {
  it('should allow lab manager to update equipment in their lab', async () => {
    const response = await request(app)
      .put('/api/v2/equipment/equipment-1')
      .set('Authorization', `Bearer ${labManagerToken}`)
      .send(updateData)

    expect(response.status).toBe(200)
  })
})
```

## 🚀 Best Practices

### 1. Principle of Least Privilege

- Grant minimum permissions necessary
- Use context-specific roles when possible
- Regular permission audits

### 2. Fail-Safe Defaults

- Deny access by default
- Explicit permission grants required
- Comprehensive error handling

### 3. Performance Optimization

- Cache access trees for active users
- Batch permission checks when possible
- Optimize database queries

### 4. Audit and Monitoring

- Log all permission changes
- Monitor access patterns
- Regular security reviews

## 🆘 Troubleshooting

### Common Issues

#### Permission Denied Errors

1. Check user's assigned roles and contexts
2. Verify permission group assignments
3. Check resource hierarchy and inheritance
4. Review access tree cache

#### Performance Issues

1. Monitor access tree cache hit rates
2. Optimize permission queries
3. Consider permission denormalization for hot paths

#### Role Assignment Problems

1. Validate context relationships
2. Check role inheritance chains
3. Verify permission group memberships

---

## 🔧 Implementation Guidelines

### 1. Server-Side Permission Verification

Always verify permissions server-side before any protected action:

```typescript
// In route handlers or services
const program = Effect.gen(function* () {
  const userPermissionsService = yield* UserPermissionsServiceLive;

  // Check permission before proceeding
  const hasPermission = yield* userPermissionsService.userHasPermission({
    userId,
    domain: 'equipment',
    action: 'update',
    resourceId: equipmentId,
  });

  if (!hasPermission) {
    return yield* Effect.fail(new PermissionDeniedError());
  }

  // Proceed with protected logic
});
```

### 2. Policy Module Integration

Use the `policy` module for declarative permission checks:

```typescript
withPolicy(checkPermission('equipment:read'))(
  Effect.gen(function* (_) {
    // Protected logic here
  })
)
```

### 3. Complex Permission Scenarios

Compose policies for complex permission scenarios using logical operators:

```typescript
// Require ALL permissions (AND logic)
withPolicy(all([
  checkPermission('equipment:read'),
  checkPermission('infrastructure:read')
]))(effect)

// Require ANY permission (OR logic)
withPolicy(any([
  permissionForResource('equipment', 'delete', equipmentId),
  all([
    permissionForResource('infrastructure', 'update', infrastructureId),
    checkPermission('equipment:update')
  ])
]))(effect)

// Complex business rule example
const deleteEquipment = (equipmentId: string, infrastructureId: string) =>
  Effect.gen(function* () {
    yield* equipmentService.delete(equipmentId);
    return { success: true };
  }).pipe(
    withPolicy(
      any([
        // Can delete equipment directly
        permissionForResource('equipment', 'delete', equipmentId),
        // OR can update infrastructure AND has general equipment update permission
        all([
          permissionForResource('infrastructure', 'update', infrastructureId),
          checkPermission('equipment:update')
        ])
      ])
    )
  );
```

### 4. Role Management Best Practices

#### Assign Roles with Proper Context

```typescript
// For institution-wide access
yield* userPermissionsService.assignRoleToUser({
  userId,
  roleId: 'institution-manager',
  resourceType: 'institution',
  resourceId: institutionId,
  grantedBy: currentUserId,
});

// For global access (use sparingly)
yield* userPermissionsService.assignRoleToUser({
  userId,
  roleId: 'system-admin',
  resourceType: null,
  resourceId: null,
});
```

#### Check User Access Before Operations

```typescript
// Always verify access before showing resources
const hasAccess = yield* userPermissionsService.userHasAccessToResource(
  userId,
  'infrastructure',
  infrastructureId
);

if (!hasAccess) {
  return yield* Effect.fail(new ResourceNotFoundError());
}
```

## 🏗️ Service Architecture

### UserPermissionsServiceLive Dependencies

```typescript
dependencies: [
  UsersServiceLive.Default,      // User data access
  AccessTreeServiceLive.Default, // Hierarchical access caching
  RolesServiceLive.Default,      // Role management
  Database.PgDatabase,           // Database transactions
]
```

### Service Separation

- **UsersServiceLive**: Handles user CRUD operations and profile management
- **UserPermissionsServiceLive**: Dedicated to permission and role management
- **AccessTreeServiceLive**: Manages hierarchical access tree building and caching
- **RolesServiceLive**: Manages role definitions and role-permission relationships

### Domain-Specific Policy Services

For complex business logic, create domain-specific policy services that encapsulate business rules:

```typescript
// Equipment-specific policies
class EquipmentPoliciesLive extends Effect.Service<EquipmentPoliciesLive>()(
  'EquipmentPoliciesLive',
  {
    dependencies: [UserPermissionsServiceLive.Default],
    effect: Effect.gen(function* () {
      const userPermissions = yield* UserPermissionsServiceLive;

      const canReadEquipment = (equipmentId: string) =>
        policy((user) => userPermissions.userHasPermission({
          userId: user.userId,
          domain: 'equipment',
          action: 'read',
          resourceId: equipmentId,
        }));

      const canUpdateEquipment = (equipmentId: string) =>
        policy((user) => userPermissions.userHasPermission({
          userId: user.userId,
          domain: 'equipment',
          action: 'update',
          resourceId: equipmentId,
        }));

      const canManageInfrastructureEquipment = (infrastructureId: string) =>
        policy((user) => userPermissions.userHasPermission({
          userId: user.userId,
          domain: 'infrastructure',
          action: 'update',
          resourceId: infrastructureId,
        }));

      return {
        canReadEquipment,
        canUpdateEquipment,
        canManageInfrastructureEquipment,
      } as const;
    }),
  },
) {}

// Usage in routes
const equipmentRoutes = Effect.gen(function* () {
  const equipmentPolicies = yield* EquipmentPoliciesLive;

  const getEquipment = (equipmentId: string) =>
    Effect.gen(function* () {
      const equipment = yield* equipmentService.findById(equipmentId);
      return equipment;
    }).pipe(
      withPolicy(equipmentPolicies.canReadEquipment(equipmentId))
    );

  return { getEquipment };
});
```

## 🚨 Error Handling

### Permission-Related Errors

```typescript
// User not found
UserNotFoundError({ id: userId })

// Role not found
RoleNotFoundError({ id: roleId })

// Duplicate role assignment
UserRoleAlreadyExistsError({
  userId,
  roleId,
  resourceType,
  resourceId
})

// Role assignment not found for removal
UserRoleNotFoundError({
  userId,
  roleId,
  resourceType,
  resourceId
})
```

### Error Handling Best Practices

1. **Graceful Degradation**: When permission checks fail, provide meaningful error messages
2. **Security**: Don't expose sensitive information in error messages
3. **Logging**: Log permission failures for security monitoring
4. **User Experience**: Provide clear feedback when access is denied

## 🔒 Security Considerations

### 1. Principle of Least Privilege

- Users start with minimal permissions (only "User" role)
- Grant additional permissions only as needed
- Use context-specific roles instead of global permissions when possible

### 2. Defense in Depth

- Multiple layers of permission checking (middleware, route, service)
- Server-side validation is mandatory, client-side is for UX only
- Database constraints prevent invalid role assignments

### 3. Audit Trail

- All role assignments include `grantedBy` field
- Timestamps track when permissions were granted
- Permission changes should be logged for compliance

### 4. Cache Security

- Access trees are user-specific and cannot be accessed by other users
- Cache invalidation prevents stale permissions after role changes
- Session-based cache expiration aligns with authentication

## 📊 Performance & Caching

### Access Tree Caching

- **Cache Duration**: User access trees are cached for 30 minutes (SESSION_EXPIRES_IN_SECONDS)
- **Cache Invalidation**: Automatic invalidation when roles change or permission updates occur
- **Performance Optimization**: Implemented using the Effect-based cache system in AccessTreeServiceLive
- **Cache Key Strategy**: User ID-based caching for efficient lookups

### Performance Considerations

- Monitor cache hit rates for access trees
- Consider database indexing on frequently queried fields
- Implement pagination for large role/permission lists

## 📈 Audit and Monitoring

- **Permission Changes**: Logged with the user who made the change (`grantedBy` field)
- **Role Assignments**: Track who granted the role and when (`createdAt`, `grantedBy`)
- **Permission Denials**: Should be logged for security monitoring and compliance
- **Access Patterns**: Monitor unusual access patterns for security threats
- **Performance Metrics**: Track permission check performance and cache hit rates

## 🧪 Testing Permissions

### Unit Testing UserPermissionsServiceLive

```typescript
describe('UserPermissionsServiceLive', () => {
  it('should assign role to user with context', async () => {
    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.assignRoleToUser({
        userId: 'test-user-id',
        roleId: 'test-role-id',
        resourceType: 'institution',
        resourceId: 'test-institution-id',
        grantedBy: 'admin-user-id',
      });
    });

    const result = await UserPermissionsRuntime.runPromise(program);
    expect(result).toBeDefined();
  });

  it('should check user permissions correctly', async () => {
    const program = Effect.gen(function* () {
      const userPermissionsService = yield* UserPermissionsServiceLive;

      return yield* userPermissionsService.userHasPermission({
        userId: 'test-user-id',
        domain: 'equipment',
        action: 'read',
        resourceId: 'test-equipment-id',
      });
    });

    const hasPermission = await UserPermissionsRuntime.runPromise(program);
    expect(typeof hasPermission).toBe('boolean');
  });
});
```

### Service Layer Integration

For complex business logic that requires permission context, integrate policies directly into service methods:

```typescript
class EquipmentServiceLive extends Effect.Service<EquipmentServiceLive>()(
  'EquipmentServiceLive',
  {
    dependencies: [EquipmentPoliciesLive.Default, EquipmentRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;
      const equipmentRepository = yield* EquipmentRepositoryLive;

      const transferEquipment = (
        equipmentId: string,
        fromInfrastructureId: string,
        toInfrastructureId: string
      ) => Effect.gen(function* () {
        // Business logic with embedded permission checks
        const canRemoveFromSource = equipmentPolicies.canManageInfrastructureEquipment(
          fromInfrastructureId
        );

        const canAddToTarget = equipmentPolicies.canManageInfrastructureEquipment(
          toInfrastructureId
        );

        // Both checks must pass
        return yield* Effect.gen(function* () {
          yield* equipmentRepository.update(equipmentId, {
            infrastructureId: toInfrastructureId,
          });
          return { success: true };
        }).pipe(
          withPolicy(all([canRemoveFromSource, canAddToTarget]))
        );
      });

      return { transferEquipment } as const;
    }),
  },
) {}
```

### Policy Testing Strategies

#### Unit Testing Individual Policies

```typescript
import { describe, it, expect } from 'vitest';
import { Effect } from 'effect';
import { EquipmentPoliciesLive } from '@rie/services';
import { CurrentUser } from '@rie/services/policies';

describe('Equipment Policies', () => {
  const mockUser = (userId: string): CurrentUser['Type'] => ({
    sessionId: 'test-session',
    userId,
  });

  it('should allow equipment read access with proper permissions', async () => {
    const program = Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;

      return yield* Effect.succeed('allowed').pipe(
        withPolicy(equipmentPolicies.canReadEquipment('equipment-123'))
      );
    }).pipe(
      Effect.provideService(CurrentUser, mockUser('user-with-permissions'))
    );

    const result = await Effect.runPromise(program);
    expect(result).toBe('allowed');
  });
});
```

#### Integration Testing with Mock Services

```typescript
import { describe, it, expect } from 'vitest';
import { Effect, Layer } from 'effect';
import { UserPermissionsServiceLive } from '@rie/services';

describe('Equipment Routes Integration', () => {
  const mockUserPermissionsService = {
    userHasPermission: ({ userId, domain, action, resourceId }) => {
      // Mock implementation based on test scenarios
      return Effect.succeed(true);
    },
    userHasAccessToResource: (userId, resourceType, resourceId) => {
      return Effect.succeed(true);
    },
  };

  const TestLayer = Layer.succeed(UserPermissionsServiceLive, mockUserPermissionsService);

  it('should handle equipment creation with proper permissions', async () => {
    const program = Effect.gen(function* () {
      const equipmentPolicies = yield* EquipmentPoliciesLive;

      return yield* Effect.succeed({ id: 'new-equipment' }).pipe(
        withPolicy(equipmentPolicies.canCreateEquipment)
      );
    }).pipe(
      Effect.provide(TestLayer),
      Effect.provideService(CurrentUser, mockUser('test-user'))
    );

    const result = await Effect.runPromise(program);
    expect(result.id).toBe('new-equipment');
  });
});
```

### Integration Testing

- Test permission inheritance through the hierarchy
- Verify cache invalidation on role changes
- Test error scenarios (user not found, role not found, etc.)
- Validate transaction rollback on failures

## 📋 Common Usage Patterns

### 1. Equipment Management Scenario

```typescript
// User assigned as equipment manager for a specific infrastructure
yield* userPermissionsService.assignRoleToUser({
  userId: 'technician-id',
  roleId: 'equipment-manager-role',
  resourceType: 'infrastructure',
  resourceId: 'lab-infrastructure-id',
  grantedBy: 'supervisor-id',
});

// Check if user can modify equipment in that infrastructure
const canModify = yield* userPermissionsService.userHasPermission({
  userId: 'technician-id',
  domain: 'equipment',
  action: 'update',
  resourceId: 'microscope-id',
});
```

### 2. Institution Administrator Scenario

```typescript
// Assign institution-wide admin role
yield* userPermissionsService.assignRoleToUser({
  userId: 'admin-id',
  roleId: 'institution-admin-role',
  resourceType: 'institution',
  resourceId: 'university-id',
  grantedBy: 'super-admin-id',
});

// Admin automatically has access to all units, infrastructures, and equipment
const accessTree = yield* userPermissionsService.getUserAccessTree('admin-id');
// accessTree.institutions includes 'university-id'
// accessTree.units includes all units under the university
// accessTree.infrastructures includes all infrastructures under those units
// accessTree.equipments includes all equipment under those infrastructures
```

### 3. Multi-Context User Scenario

```typescript
// User can have roles in multiple contexts
yield* userPermissionsService.assignRoleToUser({
  userId: 'researcher-id',
  roleId: 'researcher-role',
  resourceType: 'unit',
  resourceId: 'chemistry-unit-id',
  grantedBy: 'unit-head-id',
});

yield* userPermissionsService.assignRoleToUser({
  userId: 'researcher-id',
  roleId: 'equipment-specialist-role',
  resourceType: 'infrastructure',
  resourceId: 'spectroscopy-lab-id',
  grantedBy: 'lab-manager-id',
});

// User now has different permissions in different contexts
const userRoles = yield* userPermissionsService.getUserRolesWithContext('researcher-id');
// Returns array with both role assignments and their contexts
```

## 🚀 Migration and Deployment

### Database Migrations

- Ensure all permission-related tables are properly migrated
- Seed default roles and permissions
- Create the default "User" role that's assigned automatically

### Rollback Strategy

- Maintain backup of role assignments before major changes
- Test permission changes in staging environment
- Have rollback scripts for critical permission modifications

## 🎯 Policy Integration Best Practices

### 1. Use Domain-Specific Policies
Create policy services for each domain (Equipment, Infrastructure, etc.) to encapsulate business rules:

```typescript
// Good: Domain-specific policy service
const equipmentPolicies = yield* EquipmentPoliciesLive;
const policy = equipmentPolicies.canUpdateEquipment(equipmentId);

// Avoid: Generic permission checks everywhere
const hasPermission = yield* userPermissions.userHasPermission({
  userId, domain: 'equipment', action: 'update', resourceId: equipmentId
});
```

### 2. Compose Policies for Complex Rules
Use `all()` and `any()` to create complex authorization rules:

```typescript
// Complex business rule: Can delete equipment if they can either:
// 1. Delete equipment directly, OR
// 2. Update the infrastructure AND have general equipment permissions
withPolicy(
  any([
    permissionForResource('equipment', 'delete', equipmentId),
    all([
      permissionForResource('infrastructure', 'update', infrastructureId),
      checkPermission('equipment:update')
    ])
  ])
)
```

### 3. Resource-Specific Checks
Always use `permissionForResource()` when checking permissions for specific resources to leverage hierarchical access:

```typescript
// Good: Resource-specific check (includes hierarchical access)
withPolicy(permissionForResource('equipment', 'update', equipmentId))

// Limited: General permission check (no resource context)
withPolicy(checkPermission('equipment:update'))
```

### 4. Performance Considerations
- The policy system automatically benefits from existing permission caching
- Policies fail fast with `Forbidden` errors, providing clear feedback
- Use domain-specific policies to reduce code duplication

### 5. Error Handling
Policies integrate with Effect's error handling system:

```typescript
const program = Effect.gen(function* () {
  // Business logic here
}).pipe(
  withPolicy(permissionForResource('equipment', 'delete', equipmentId)),
  Effect.catchTag('Forbidden', (error) =>
    Effect.succeed({ error: 'Access denied', message: error.message })
  )
);
```

---

**Next**: [Authentication Guide](./authentication.md) | [API Endpoints](./endpoints/)
