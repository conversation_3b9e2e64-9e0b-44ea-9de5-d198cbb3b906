import { EquipmentCategoryParentsMigrationConverter } from '../converters/07-03-convert-equipment-category-parents-inserts';

async function convertEquipmentCategoryParentsData() {
  // Create converter and run conversion
  const converter = new EquipmentCategoryParentsMigrationConverter();
  await converter.convertFile();
}

// Run the conversion
convertEquipmentCategoryParentsData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
