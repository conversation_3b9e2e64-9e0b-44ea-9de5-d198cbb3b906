import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  BuildingsRepositoryLive,
  CampusesRepositoryLive,
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  InstitutionsRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  BuildingsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const BuildingsServicesLayer = Layer.mergeAll(
  BuildingsRepositoryLive.Default,
  BuildingsServiceLive.Default,
  CampusesRepositoryLive.Default,
  InstitutionsRepositoryLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const BuildingsRuntime = ManagedRuntime.make(BuildingsServicesLayer);
