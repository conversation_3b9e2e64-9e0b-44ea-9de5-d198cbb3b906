import type {
  DbPermission,
  DbUserRoleInput,
} from '@rie/db-schema/entity-types';
import { userRoles } from '@rie/db-schema/schemas';
import {
  RoleNotFoundError,
  UserNotFoundError,
  UserRoleAlreadyExistsError,
  UserRoleNotFoundError,
} from '@rie/domain/errors';
import type {
  PermissionAction,
  ResourceType,
  UserDetailWithRoles,
} from '@rie/domain/types';
import { Database } from '@rie/postgres-db';
import { UsersRepositoryLive } from '@rie/repositories';
import { and, eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import { AccessTreeServiceLive } from './access-tree.service';
import { RolesServiceLive } from './roles.service';
import { UsersServiceLive } from './users.service';

export interface UserPermissionCheckParams {
  userId: string;
  domain: ResourceType;
  action: PermissionAction;
  resourceId?: string;
}

export class UserPermissionsServiceLive extends Effect.Service<UserPermissionsServiceLive>()(
  'UserPermissionsServiceLive',
  {
    dependencies: [
      UsersServiceLive.Default,
      AccessTreeServiceLive.Default,
      RolesServiceLive.Default,
    ],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      /**
       * Get all roles assigned to a user with their context information
       */
      const getUserRolesWithContext = (
        userId: string,
      ): Effect.Effect<
        UserDetailWithRoles['roles'],
        Database.DatabaseError | UserNotFoundError,
        UsersServiceLive | UsersRepositoryLive
      > => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersService = yield* UsersServiceLive;

          // First verify user exists
          const user = yield* usersService.getUserById(userId);
          if (!user) {
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          const t1 = performance.now();
          console.log(
            `Call to getUserRolesWithContext took ${t1 - t0} milliseconds.`,
          );
          return user.roles;
        });
      };

      /**
       * Assign a role to a user with optional context
       */
      const assignRoleToUser = ({
        userId,
        roleId,
        resourceType,
        resourceId,
        grantedBy,
      }: DbUserRoleInput) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersService = yield* UsersServiceLive;
          const rolesService = yield* RolesServiceLive;

          // Verify user exists
          const user = yield* usersService.getUserById(userId);
          if (!user) {
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          // Verify role exists
          const role = yield* rolesService.getRoleById(roleId, {
            view: 'detail',
          });
          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id: roleId }));
          }

          // Check if user already has this role with the same context
          const existingUserRole = user.roles?.find(
            (ur: NonNullable<typeof user.roles>[number]) =>
              ur.id === roleId &&
              ur.resourceType === resourceType &&
              ur.resourceId === resourceId,
          );

          if (existingUserRole) {
            return yield* Effect.fail(
              new UserRoleAlreadyExistsError({
                userId,
                roleId,
                resourceType,
                resourceId,
              }),
            );
          }

          // Create the user role assignment
          const userRoleData: DbUserRoleInput = {
            userId,
            roleId,
            resourceType,
            resourceId,
            grantedBy,
          };

          const result = yield* dbClient.transaction((tx) =>
            Effect.gen(function* () {
              const [newUserRole] = yield* tx((client) =>
                client.insert(userRoles).values(userRoleData).returning({
                  userId: userRoles.userId,
                  roleId: userRoles.roleId,
                  resourceType: userRoles.resourceType,
                  resourceId: userRoles.resourceId,
                  grantedBy: userRoles.grantedBy,
                  createdAt: userRoles.createdAt,
                }),
              );

              return newUserRole;
            }),
          );

          const t1 = performance.now();
          console.log(`Call to assignRoleToUser took ${t1 - t0} milliseconds.`);

          return result;
        });
      };

      /**
       * Remove a role from a user
       */
      const removeRoleFromUser = ({
        userId,
        roleId,
      }: Pick<DbUserRoleInput, 'userId' | 'roleId'>) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersRepository = yield* UsersRepositoryLive;

          // Verify user exists
          const user = yield* usersRepository.findUserById(userId);
          if (!user) {
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          // Check if user has this role
          const existingUserRole = user.userRoles?.find(
            (ur) => ur.role.id === roleId,
          );

          if (!existingUserRole) {
            return yield* Effect.fail(
              new UserRoleNotFoundError({
                userId,
                roleId,
              }),
            );
          }

          // Remove the user role assignment
          const whereCondition = and(
            eq(userRoles.userId, userId),
            eq(userRoles.roleId, roleId),
          );

          const result = yield* dbClient.transaction((tx) =>
            Effect.gen(function* () {
              const deletedRows = yield* tx((client) =>
                client.delete(userRoles).where(whereCondition).returning({
                  userId: userRoles.userId,
                  roleId: userRoles.roleId,
                }),
              );

              return deletedRows.length > 0;
            }),
          );

          const t1 = performance.now();
          console.log(
            `Call to removeRoleFromUser took ${t1 - t0} milliseconds.`,
          );

          return result;
        });
      };

      /**
       * Check if a user has access to a specific resource using the access tree
       */
      const userHasAccessToResource = (
        userId: string,
        resourceType: ResourceType,
        resourceId: string,
      ) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const accessTreeService = yield* AccessTreeServiceLive;

          const hasAccess = yield* accessTreeService.userHasAccessTo(
            userId,
            resourceType,
            resourceId,
          );

          const t1 = performance.now();
          console.log(
            `Call to userHasAccessToResource took ${t1 - t0} milliseconds.`,
          );

          return hasAccess;
        });
      };

      /**
       * Check if a user has a specific permission for a domain/action
       * This method checks permissions through roles and considers context
       */
      const userHasPermission = ({
        userId,
        domain,
        action,
        resourceId,
      }: UserPermissionCheckParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersRepository = yield* UsersRepositoryLive;

          console.log(`[DEBUG] userHasPermission called with userId: ${userId}, domain: ${domain}, action: ${action}, resourceId: ${resourceId}`);

          // If checking for a specific resource, first verify access
          if (resourceId) {
            const hasResourceAccess = yield* userHasAccessToResource(
              userId,
              domain,
              resourceId,
            );
            const t1 = performance.now();
            console.log(
              `Call to userHasPermission took ${t1 - t0} milliseconds.`,
            );

            if (!hasResourceAccess) {
              console.log(`[DEBUG] User ${userId} does not have access to resource ${resourceId}`);
              return false;
            }
          }

          // Use findUserPermissions to get all user permissions directly
          const userWithPermissions =
            yield* usersRepository.findUserPermissions(userId);

          if (!userWithPermissions) {
            console.log(`[DEBUG] User not found: ${userId}`);
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          console.log(`[DEBUG] User found with ${userWithPermissions.userRoles.length} roles`);

          // Check if any of the user's roles have the required permission
          for (const userRole of userWithPermissions.userRoles) {
            console.log(`[DEBUG] Checking role: ${userRole.role.id}`);

            // Check direct role permissions
            const directPermissions = userRole.role.rolePermissions || [];
            console.log(`[DEBUG] Role has ${directPermissions.length} direct permissions`);

            for (const rp of directPermissions) {
              console.log(`[DEBUG] Direct permission: ${rp.permission.domain}:${rp.permission.action}`);
            }

            const hasRolePermission = directPermissions.some(
              (rp) =>
                rp.permission.domain === domain &&
                rp.permission.action === action,
            );

            if (hasRolePermission) {
              console.log(`[DEBUG] Found matching direct permission: ${domain}:${action}`);
              const t1 = performance.now();
              console.log(
                `Call to userHasPermission took ${t1 - t0} milliseconds.`,
              );
              return true;
            }

            // Check permissions from permission groups
            const rolePermissionGroups = (userRole.role as any).rolePermissionGroups || [];
            console.log(`[DEBUG] Role has ${rolePermissionGroups.length} permission groups`);

            for (const rpg of rolePermissionGroups) {
              console.log(`[DEBUG] Permission group: ${rpg.group.name} (${rpg.group.id})`);
              const groupPermissions = rpg.group.permissions || [];
              console.log(`[DEBUG] Group has ${groupPermissions.length} permissions`);

              for (const gp of groupPermissions) {
                console.log(`[DEBUG] Group permission: ${gp.permission.domain}:${gp.permission.action}`);
              }
            }

            const hasGroupPermission = rolePermissionGroups.some(
              (rpg: any) =>
                rpg.group.permissions?.some(
                  (gp: any) =>
                    gp.permission.domain === domain &&
                    gp.permission.action === action,
                ),
            );

            if (hasGroupPermission) {
              console.log(`[DEBUG] Found matching group permission: ${domain}:${action}`);
              const t1 = performance.now();
              console.log(
                `Call to userHasPermission took ${t1 - t0} milliseconds.`,
              );
              return true;
            }
          }

          // Check direct user permissions from user_permissions table
          // const hasDirectUserPermission = userWithPermissions.directPermissions?.some(
          //   (dp) =>
          //     dp.permission.domain === domain &&
          //     dp.permission.action === action,
          // );

          // if (hasDirectUserPermission) {
          //   const t1 = performance.now();
          //   console.log(
          //     `Call to userHasPermission took ${t1 - t0} milliseconds.`,
          //   );
          //   return true;
          // }

          console.log(`[DEBUG] No matching permission found for: ${domain}:${action}`);
          const t1 = performance.now();
          console.log(
            `Call to userHasPermission took ${t1 - t0} milliseconds.`,
          );
          return false;
        });
      };

      /**
       * Get all permissions a user has for a specific resource
       */
      const getUserPermissionsForResource = (
        userId: string,
        resourceType: ResourceType,
        resourceId: string,
      ) => {
        return Effect.gen(function* () {
          // First check if user has access to the resource
          const hasAccess = yield* userHasAccessToResource(
            userId,
            resourceType,
            resourceId,
          );

          if (!hasAccess) {
            return [];
          }

          const usersRepository = yield* UsersRepositoryLive;
          const userWithPermissions =
            yield* usersRepository.findUserPermissions(userId);

          if (!userWithPermissions) {
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          // Collect all permissions from all roles (direct + groups)
          const userPermissions = userWithPermissions.userRoles.reduce<
            Omit<DbPermission, 'createdAt' | 'updatedAt'>[]
          >((acc, userRole) => {
            // Add direct role permissions
            if (userRole.role.rolePermissions) {
              for (const rp of userRole.role.rolePermissions) {
                if (!acc.find((p) => p.id === rp.permission.id)) {
                  acc.push(rp.permission);
                }
              }
            }

            // Add permissions from permission groups
            if ((userRole.role as any).rolePermissionGroups) {
              for (const rpg of (userRole.role as any).rolePermissionGroups) {
                if (rpg.group.permissions) {
                  for (const gp of rpg.group.permissions) {
                    if (!acc.find((p) => p.id === gp.permission.id)) {
                      acc.push(gp.permission);
                    }
                  }
                }
              }
            }
            return acc;
          }, []);

          return userPermissions;
        });
      };

      /**
       * Get all permissions a user has across all domains and actions
       * This is useful for session data and general permission checking
       */
      const getAllUserPermissions = (userId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const usersRepository = yield* UsersRepositoryLive;
          const userWithPermissions =
            yield* usersRepository.findUserPermissions(userId);

          if (!userWithPermissions) {
            return yield* Effect.fail(new UserNotFoundError({ id: userId }));
          }

          // Collect all permissions from all roles (direct + groups)
          const userPermissions = userWithPermissions.userRoles.reduce<
            Omit<DbPermission, 'createdAt' | 'updatedAt'>[]
          >((acc, userRole) => {
            // Add direct role permissions
            if (userRole.role.rolePermissions) {
              for (const rp of userRole.role.rolePermissions) {
                if (!acc.find((p) => p.id === rp.permission.id)) {
                  acc.push(rp.permission);
                }
              }
            }

            // Add permissions from permission groups
            if ((userRole.role as any).rolePermissionGroups) {
              for (const rpg of (userRole.role as any).rolePermissionGroups) {
                if (rpg.group.permissions) {
                  for (const gp of rpg.group.permissions) {
                    if (!acc.find((p) => p.id === gp.permission.id)) {
                      acc.push(gp.permission);
                    }
                  }
                }
              }
            }
            return acc;
          }, []);

          const allPermissions = userPermissions;

          const t1 = performance.now();
          console.log(
            `Call to getAllUserPermissions took ${t1 - t0} milliseconds.`,
          );

          return allPermissions;
        });
      };

      /**
       * Get the access tree for a user (delegating to AccessTreeService)
       */
      const getUserAccessTree = (userId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const accessTreeService = yield* AccessTreeServiceLive;

          const accessTree =
            yield* accessTreeService.buildUserAccessTree(userId);

          const t1 = performance.now();
          console.log(
            `Call to getUserAccessTree took ${t1 - t0} milliseconds.`,
          );

          return accessTree;
        });
      };

      return {
        getUserRolesWithContext,
        assignRoleToUser,
        removeRoleFromUser,
        userHasAccessToResource,
        userHasPermission,
        getUserPermissionsForResource,
        getAllUserPermissions,
        getUserAccessTree,
      } as const;
    }),
  },
) { }
