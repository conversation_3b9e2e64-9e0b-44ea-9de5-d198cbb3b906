import { NormalizedNullableStringSchema } from '@rie/api-contracts';
import * as Schema from 'effect/Schema';

export const CampusAddressSchema = Schema.Struct({
  building: Schema.Struct({
    value: NormalizedNullableStringSchema,
    label: NormalizedNullableStringSchema,
  }),
  local: Schema.Struct({
    value: NormalizedNullableStringSchema,
    label: NormalizedNullableStringSchema,
  }),
});

export const CivicAddressSchema = Schema.Struct({
  street: NormalizedNullableStringSchema,
  city: NormalizedNullableStringSchema,
  countryCode: NormalizedNullableStringSchema,
  fullAddress: NormalizedNullableStringSchema,
  postalCode: NormalizedNullableStringSchema,
  placeId: NormalizedNullableStringSchema,
  state: NormalizedNullableStringSchema,
  streetName: NormalizedNullableStringSchema,
  streetNumber: NormalizedNullableStringSchema,
  lat: NormalizedNullableStringSchema,
  lon: NormalizedNullableStringSchema,
});

export const CivicCampusAddressSchema = Schema.Union(
  Schema.Struct({
    addressType: Schema.Literal('campusAddress'),
    data: CampusAddressSchema,
  }),
  Schema.Struct({
    addressType: Schema.Literal('civicAddress'),
    data: CivicAddressSchema,
  }),
);
