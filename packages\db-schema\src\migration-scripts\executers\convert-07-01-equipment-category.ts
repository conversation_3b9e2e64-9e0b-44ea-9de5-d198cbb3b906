import { EquipmentCategoryMigrationConverter } from '../converters/07-01-convert-equipment-category-inserts';

async function convertEquipmentCategoryData() {
  // Create converter and run conversion
  const converter = new EquipmentCategoryMigrationConverter();
  await converter.convertFile();
}

// Run the conversion
convertEquipmentCategoryData().catch((error) => {
  console.error('Conversion failed:', error);
  process.exit(1);
});
