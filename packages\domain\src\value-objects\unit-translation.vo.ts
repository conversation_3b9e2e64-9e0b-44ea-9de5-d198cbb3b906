import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type { ParseError } from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DbUnitI18NDataSchema } from '../schemas';
import type { UnitTranslationFields } from '../types';

export const UnitTranslationData = Data.case<UnitTranslationFields>();

/**
 * Represents a single, immutable, and validated translation for a unit.
 * Its responsibility is to hold the value and enforce its invariants.
 * It does not handle data transformation between layers.
 */
export class UnitTranslation {
  // The constructor is kept private to enforce creation via the validated factory method.
  private constructor(private readonly data: UnitTranslationFields) {}

  /**
   * Creates a new UnitTranslation value object from raw data.
   * It validates the input against the UnitTranslationSchema.
   * This is the single, authoritative entry point for creating a valid UnitTranslation.
   */
  static create(input: unknown): Effect.Effect<UnitTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbUnitI18NDataSchema)(input),
      Effect.map(
        (validatedData) =>
          new UnitTranslation(UnitTranslationData(validatedData)),
      ),
    );
  }

  // --- Accessors ---

  getLocale() {
    return this.data.locale;
  }

  getName() {
    return this.data.name;
  }

  getDescription() {
    return this.data.description;
  }

  getOtherNames() {
    return this.data.otherNames;
  }

  getAcronyms() {
    return this.data.acronyms;
  }

  /**
   * Converts the value object to its raw data representation.
   * Useful for persistence or serialization.
   */
  toRaw(): UnitTranslationFields {
    return this.data;
  }

  /**
   * Checks for equality with another UnitTranslation object based on its values.
   */
  equals(other: UnitTranslation): boolean {
    return (
      this.data.locale === other.data.locale &&
      this.data.name === other.data.name &&
      this.data.description === other.data.description &&
      this.data.otherNames === other.data.otherNames &&
      this.data.acronyms === other.data.acronyms
    );
  }
}
