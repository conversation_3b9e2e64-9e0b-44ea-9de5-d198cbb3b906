# API Architecture

This document explains the architecture and design patterns used in the RIE API server, built with Hono, Effect-ts, and following Clean Architecture principles.

## 🏗️ Architecture Overview

The API follows Clean Architecture with clear separation of concerns and dependency inversion:

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (HTTP)                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Routes    │  │ Middleware  │  │   OpenAPI   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                Application Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  Use Cases  │  │    Ports    │  │ Interfaces  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                Domain Layer (Shared)                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Models    │  │   Errors    │  │   Rules     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│              Infrastructure Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │Repositories │  │  Services   │  │  Database   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
apps/api/src/
├── api/                           # API Layer (HTTP concerns)
│   └── v2/                        # API version 2
│       ├── middleware/            # HTTP middleware
│       │   ├── auth.middleware.ts
│       │   ├── cors.middleware.ts
│       │   └── validation.middleware.ts
│       └── routes/                # Route definitions
│           ├── auth.routes.ts
│           ├── users.routes.ts
│           └── index.ts
│
├── application/                   # Application Layer (Use Cases)
│   ├── ports/                     # Interfaces (Dependency Inversion)
│   │   └── repositories/          # Repository interfaces
│   │       ├── user.repository.ts
│   │       └── permission.repository.ts
│   └── use-cases/                 # Business use cases
│       ├── auth/
│       ├── users/
│       └── permissions/
│
├── infrastructure/                # Infrastructure Layer (Implementations)
│   ├── config/                    # Configuration management
│   │   ├── database.config.ts
│   │   └── auth.config.ts
│   ├── repositories/              # Repository implementations
│   │   ├── user.repository.impl.ts
│   │   └── permission.repository.impl.ts
│   ├── services/                  # External service implementations
│   │   ├── email.service.ts
│   │   └── notification.service.ts
│   └── runtimes/                  # Effect-ts runtime configurations
│       └── main.runtime.ts
│
├── app.ts                         # Application setup and configuration
└── index.ts                      # Application entry point
```

## 🎯 Core Principles

### 1. Clean Architecture

- **Dependency Rule**: Dependencies point inward toward the domain
- **Independence**: Business logic is independent of frameworks and databases
- **Testability**: Easy to test each layer in isolation

### 2. Domain-Driven Design (DDD)

- **Domain Models**: Rich domain objects with behavior
- **Ubiquitous Language**: Consistent terminology across code and business
- **Bounded Contexts**: Clear boundaries between different business areas

### 3. Functional Programming with Effect-ts

- **Pure Functions**: Predictable, testable functions
- **Immutability**: Data structures don't change after creation
- **Composability**: Small functions combine to create complex behavior
- **Type Safety**: Compile-time guarantees about program behavior

## 🔧 Technology Stack

### Core Technologies

- **Runtime**: Bun (fast JavaScript runtime)
- **Framework**: Hono (lightweight, edge-ready web framework)
- **Effect System**: Effect-ts (functional programming runtime)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth
- **Validation**: Effect Schemas
- **Documentation**: OpenAPI/Swagger

### Why These Choices?

#### Hono Framework

```typescript
// Lightweight, fast, and TypeScript-first
const app = new Hono()
  .use('*', cors())
  .use('/api/*', authMiddleware)
  .route('/api/v2', apiRoutes)
```

**Benefits**:

- Minimal overhead and fast performance
- Built for edge runtime compatibility
- Excellent TypeScript support
- Middleware ecosystem
- OpenAPI integration

#### Effect-ts for Business Logic

```typescript
// Type-safe, composable business logic
const createUser = Effect.gen(function* (_) {
  const { userRepository, emailService } = yield* _(
    Effect.service<CreateUserDeps>()
  )

  const user = yield* _(userRepository.create(userData))
  yield* _(emailService.sendWelcomeEmail(user.email))

  return user
})
```

**Benefits**:

- Type-safe dependency injection
- Composable error handling
- Resource management
- Testability through dependency substitution

## 🏛️ Layer Details

### API Layer (`src/api/v2/`)

Handles HTTP concerns and API contracts.

#### Responsibilities

- HTTP request/response handling
- Input validation and serialization
- Authentication and authorization
- API documentation generation
- Error handling and status codes

#### Key Components

**Routes**:

```typescript
// User routes example
export const userRoutes = new Hono()
  .get('/', listUsers)
  .post('/', createUser)
  .get('/:id', getUser)
  .put('/:id', updateUser)
  .delete('/:id', deleteUser)
```

**Middleware**:

```typescript
// Authentication middleware
export const authMiddleware = async (c: Context, next: Next) => {
  const token = c.req.header('Authorization')?.replace('Bearer ', '')

  if (!token) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const user = await validateToken(token)
  c.set('user', user)

  await next()
}
```

### Application Layer (`src/application/`)

Orchestrates business workflows and defines interfaces.

#### Ports (Interfaces)

Define contracts for external dependencies:

```typescript
// Repository interface
export interface UserRepository {
  findById(id: string): Effect.Effect<User | null, DatabaseError>
  create(data: CreateUserData): Effect.Effect<User, DatabaseError>
  update(id: string, data: UpdateUserData): Effect.Effect<User, DatabaseError>
  delete(id: string): Effect.Effect<void, DatabaseError>
}
```

#### Use Cases

Implement business workflows:

```typescript
// Create user use case
interface CreateUserDeps {
  userRepository: UserRepository
  emailService: EmailService
  permissionService: PermissionService
}

export const createUser = (userData: CreateUserData) =>
  Effect.gen(function* (_) {
    const { userRepository, emailService, permissionService } =
      yield* _(Effect.service<CreateUserDeps>())

    // Validate business rules
    yield* _(validateUserData(userData))

    // Create user
    const user = yield* _(userRepository.create(userData))

    // Set default permissions
    yield* _(permissionService.assignDefaultRole(user.id))

    // Send welcome email
    yield* _(emailService.sendWelcomeEmail(user.email))

    return user
  })
```

### Domain Layer (`packages/domain/`)

Contains business logic, models, and rules (shared package).

#### Domain Models

```typescript
// User domain model
export class User extends Data.Class<{
  readonly id: string
  readonly email: Email
  readonly name: Name
  readonly status: UserStatus
  readonly createdAt: Date
}> {
  // Domain methods
  isActive(): boolean {
    return this.status === UserStatus.Active
  }

  canAccessResource(resource: Resource): boolean {
    // Business logic for access control
    return this.permissions.some(p => p.allows(resource))
  }
}
```

#### Domain Errors

```typescript
// Type-safe error handling
export class UserNotFoundError extends Data.TaggedError("UserNotFoundError")<{
  readonly userId: string
  readonly message: string
}> {}

export class InvalidEmailError extends Data.TaggedError("InvalidEmailError")<{
  readonly email: string
  readonly reason: string
}> {}
```

### Infrastructure Layer (`src/infrastructure/`)

Implements interfaces and handles external concerns.

#### Repository Implementations

```typescript
// User repository implementation
export class UserRepositoryImpl implements UserRepository {
  constructor(private db: Database) {}

  findById(id: string): Effect.Effect<User | null, DatabaseError> {
    return Effect.tryPromise({
      try: () => this.db.query.users.findFirst({
        where: eq(users.id, id)
      }),
      catch: (error) => new DatabaseError({ cause: error })
    }).pipe(
      Effect.map(userData => userData ? User.make(userData) : null)
    )
  }

  // ... other methods
}
```

## 🔄 Request Flow

### Typical API Request Flow

1. **HTTP Request**: Client sends request to API endpoint
2. **Middleware**: Authentication, CORS, validation middleware run
3. **Route Handler**: Hono route handler receives request
4. **Input Validation**: Request data validated against schemas
5. **Use Case Execution**: Business logic executed via Effect-ts
6. **Repository Access**: Data persistence through repository layer
7. **Response Serialization**: Data formatted for API response
8. **HTTP Response**: Response sent back to client

### Example Flow Diagram

```
Client Request
      ↓
┌─────────────┐
│ CORS        │ ← Middleware Layer
│ Auth        │
│ Validation  │
└─────┬───────┘
      ↓
┌─────────────┐
│ Route       │ ← API Layer
│ Handler     │
└─────┬───────┘
      ↓
┌─────────────┐
│ Use Case    │ ← Application Layer
│ Execution   │
└─────┬───────┘
      ↓
┌─────────────┐
│ Repository  │ ← Infrastructure Layer
│ Database    │
└─────┬───────┘
      ↓
Client Response
```

## 🔐 Authentication & Authorization

### Authentication Flow

1. User provides credentials
2. Better Auth validates credentials
3. JWT token issued with user context
4. Token included in subsequent requests
5. Middleware validates token and extracts user

### Authorization Model

- **Role-Based Access Control (RBAC)**
- **Permission Groups**: Roles contain permission groups
- **Context-Aware**: Permissions scoped to resources
- **Hierarchical**: Permission inheritance

```typescript
// Permission checking in use cases
const updateUser = (userId: string, data: UpdateUserData) =>
  Effect.gen(function* (_) {
    const { user, permissionService } = yield* _(
      Effect.service<UpdateUserDeps>()
    )

    // Check permissions
    const canUpdate = yield* _(
      permissionService.canUpdateUser(user.id, userId)
    )

    if (!canUpdate) {
      yield* _(Effect.fail(new UnauthorizedError()))
    }

    // Proceed with update...
  })
```

### Route protection with policies and two-step verification

The API protects resource operations using the policy system from the services package. A common pattern is:

```ts
withPolicy(permissionForResource('unit', 'delete', unitId))(effect)
```

This enforces a two-step verification:

1) WHERE (hierarchical access): Verify the current user can access the specific resource using the access tree
   - `permissionForResource` → `UserPermissionsServiceLive.userHasPermission(...)`
   - calls `userHasAccessToResource(userId, resourceType, resourceId)`
   - delegates to `AccessTreeServiceLive.userHasAccessTo()` which may return true immediately for admin users, otherwise evaluates the user’s access tree built by `buildUserAccessTree()`

2) WHAT (permission validation): Verify the user’s roles include the required `domain:action` permission
   - `userHasPermission` inspects role permissions to match the required operation (e.g., `unit:delete`)
   - There is no admin bypass here; the admin role must include the permission via its permission groups

#### Execution flow diagram

```
Route → withPolicy(permissionForResource) → policy system
      → UserPermissionsServiceLive.userHasPermission
          → (if resourceId) userHasAccessToResource (WHERE)
              → AccessTreeServiceLive.userHasAccessTo
                  → (admin?) yes → true
                  → (admin?) no  → buildUserAccessTree → check membership
          → check role permissions (WHAT)
              → some role has domain:action? yes → allow
              → otherwise → deny
```

This separation ensures least-privilege by requiring both correct scope (WHERE) and explicit capability (WHAT).

#### Permission services architecture (overview)

- UserPermissionsServiceLive
  - Responsibilities: role assignment/removal, permission checks (WHAT), delegating hierarchical access checks (WHERE)
  - Depends on: UsersServiceLive, AccessTreeServiceLive, RolesServiceLive, Database (via PgDatabase)
- AccessTreeServiceLive
  - Responsibilities: build and cache hierarchical access trees, `userHasAccessTo` checks with admin bypass
  - Depends on: UsersRepositoryLive, UnitsRepositoryLive, InfrastructuresRepositoryLive
- RolesServiceLive / UsersServiceLive
  - Responsibilities: role definitions / user data access and composition used by permission checks

See full permission model, permissions catalog, and detailed flows in docs/api/permissions.md

## 🧪 Testing Strategy

### Unit Tests

Test individual functions and domain logic:

```typescript
describe('User domain model', () => {
  it('should validate email format', () => {
    const result = User.make({
      email: 'invalid-email',
      // ... other fields
    })

    expect(result).toEqual(Effect.fail(new InvalidEmailError()))
  })
})
```

### Integration Tests

Test use cases with real dependencies:

```typescript
describe('Create user use case', () => {
  it('should create user and send welcome email', async () => {
    const mockDeps = {
      userRepository: new MockUserRepository(),
      emailService: new MockEmailService()
    }

    const result = await Effect.runPromise(
      createUser(userData).pipe(
        Effect.provide(Layer.succeed(mockDeps))
      )
    )

    expect(result).toEqual(expect.objectContaining({
      email: userData.email
    }))
  })
})
```

### API Tests

Test HTTP endpoints:

```typescript
describe('User API', () => {
  it('should create user via POST /users', async () => {
    const response = await app.request('/api/v2/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    })

    expect(response.status).toBe(201)
    expect(await response.json()).toMatchObject({
      email: userData.email
    })
  })
})
```

## 📊 Performance Considerations

### Database Optimization

- Connection pooling with Drizzle
- Query optimization and indexing
- Avoiding N+1 query problems
- Strategic use of database transactions

### Caching Strategy

- In-memory caching for frequently accessed data
- Redis integration for distributed caching (future)
- HTTP caching headers for appropriate endpoints

### Error Handling

- Structured error responses
- Proper HTTP status codes
- Error logging and monitoring
- Graceful degradation

## 🚀 Deployment Considerations

### Environment Configuration

- Environment-specific configurations
- Secret management
- Database connection pooling
- Health check endpoints

### Monitoring & Observability

- Structured logging
- Performance metrics
- Error tracking
- Health monitoring

---

**Next**: [Frontend Architecture](./frontend-architecture.md) | [Database Design](./database-design.md)
