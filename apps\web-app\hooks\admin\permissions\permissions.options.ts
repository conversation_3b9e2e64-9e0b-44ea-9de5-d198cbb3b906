import {
  getAllPermissions,
  getPermissionById,
} from '@/services/permissions/permissions.service';
import type {
  PermissionResultType,
  ResourcePermissionResultType,
} from '@/types/permission.type';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getAllPermissionsOptions = <
  View extends CollectionViewParamType['view'] = 'list',
>({
  view,
}: CollectionViewParamType) => {
  return queryOptions<PermissionResultType<View>>({
    queryFn: () => getAllPermissions<View>({ view }),
    queryKey: ['permissions', { view }],
  });
};

interface GetPermissionByIdParams {
  id: string;
  view: ResourceViewType;
}

export const getPermissionByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: GetPermissionByIdParams) => {
  return queryOptions<ResourcePermissionResultType<View>>({
    queryFn: () => getPermissionById({ id, view }),
    queryKey: ['permissions', id, view],
    enabled: !!id,
  });
};
