import {
    type FundingProjectDetailResponseDTO,
    FundingProjectDetailResponseDtoSchema,
    type FundingProjectEditResponseDTO,
    FundingProjectEditResponseDtoSchema,
    type FundingProjectListItemDTO,
    FundingProjectListItemDtoSchema,
    type SelectOptionDTO,
    SelectOptionDtoSchema,
} from '@rie/api-contracts';
import type { FundingProject } from '@rie/domain/aggregates';
import { DbFundingProjectDataSchema, DbFundingProjectSchema } from '@rie/domain/schemas';
import type { FundingProjectData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes a FundingProject aggregate into the shape required for a list view.
 *
 * Uses the FundingProject aggregate's composite translation logic to provide intelligent
 * locale fallback behavior, ensuring the best available translation is selected
 * for each field based on the requested locale and fallback preferences.
 */
export function serializeFundingProjectToListItem(
    fundingProject: FundingProject,
    locale: string,
    fallbackLocale: string,
): Effect.Effect<FundingProjectListItemDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbFundingProjectSchema,
        FundingProjectListItemDtoSchema,
        {
            strict: true,
            decode: (fundingProjectData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // Use the FundingProject aggregate's composite translation logic for intelligent fallback
                        const compositeTranslations = fundingProject.getCompositeTranslations(
                            locale,
                            fallbackLocale,
                        );

                        // Ensure we always provide a non-empty string for text
                        const nameCandidate = compositeTranslations.name.value;
                        const safeText =
                            typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                                ? nameCandidate
                                : fundingProjectData.id;

                        return {
                            id: fundingProjectData.id,
                            text: safeText,
                            titulaire: null, // Will be populated from relations in real usage
                            infrastructure: null, // Will be populated from relations in real usage
                            lastUpdatedAt: fundingProjectData.updatedAt,
                            createdAt: fundingProjectData.createdAt,
                            holderId: fundingProjectData.holderId,
                            typeId: fundingProjectData.typeId,
                            fciId: fundingProjectData.fciId,
                            synchroId: fundingProjectData.synchroId,
                            obtainingYear: fundingProjectData.obtainingYear,
                            endDate: fundingProjectData.endDate,
                        };
                    },
                    catch(error) {
                        return new ParseResult.Type(
                            ast,
                            fundingProjectData,
                            error instanceof Error
                                ? error.message
                                : 'Failed to serialize funding project to list item',
                        );
                    },
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        fundingProject.toRaw(),
        Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
        Effect.catchAll((err) =>
            Effect.sync(() => {
                console.error(
                    `[Serializer][FundingProject ListItem] Failed for funding project id=${fundingProject.getId()}:`,
                    err,
                );
                throw err;
            }),
        ),
    );
}

/**
 * Serializes a FundingProject aggregate into the shape required for a detailed view.
 *
 * Uses the FundingProject aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for all translatable fields. The resulting DTO
 * contains the best available translation for each field based on the requested
 * locale and fallback preferences, formatted for detailed view consumption.
 */
export function serializeFundingProjectToDetail(
    fundingProject: FundingProject,
    locale: string,
    fallbackLocale: string,
): Effect.Effect<FundingProjectDetailResponseDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbFundingProjectSchema,
        FundingProjectDetailResponseDtoSchema,
        {
            strict: true,
            decode: (fundingProjectData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // Use the FundingProject aggregate's composite translation logic for intelligent fallback
                        const compositeTranslations = fundingProject.getCompositeTranslations(
                            locale,
                            fallbackLocale,
                        );

                        return {
                            id: fundingProjectData.id,
                            isActive: fundingProjectData.isActive ?? true,
                            holderId: fundingProjectData.holderId,
                            typeId: fundingProjectData.typeId,
                            fciId: fundingProjectData.fciId,
                            synchroId: fundingProjectData.synchroId,
                            obtainingYear: fundingProjectData.obtainingYear,
                            endDate: fundingProjectData.endDate,
                            translations: {
                                name: {
                                    locale: compositeTranslations.name.locale,
                                    value: compositeTranslations.name.value ?? '',
                                },
                                description: {
                                    locale: compositeTranslations.description.locale,
                                    value: compositeTranslations.description.value,
                                },
                            },
                            modifiedBy: fundingProjectData.modifiedBy ?? null,
                            createdAt: fundingProjectData.createdAt,
                            updatedAt: fundingProjectData.updatedAt,
                            holder: null, // Will be populated from relations
                            fundingType: null, // Will be populated from relations
                        };
                    },
                    catch(error) {
                        return new ParseResult.Type(
                            ast,
                            fundingProjectData,
                            error instanceof Error
                                ? error.message
                                : 'Failed to serialize funding project to detail view',
                        );
                    },
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        fundingProject.toRaw(),
        Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
        Effect.catchAll((err) =>
            Effect.sync(() => {
                console.error(
                    `[Serializer][FundingProject Detail] Failed for funding project id=${fundingProject.getId()}:`,
                    err,
                );
                throw err;
            }),
        ),
    );
}

/**
 * Serializes a FundingProject aggregate into a simple value/label pair for select inputs.
 *
 * Uses the FundingProject aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for the label field. This ensures that select options
 * display the best available funding project name based on the requested locale and fallback
 * preferences, providing a consistent user experience across different languages.
 */
export function serializeFundingProjectToSelectOption(
    fundingProject: FundingProject,
    locale: string,
    fallbackLocale: string,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbFundingProjectSchema,
        SelectOptionDtoSchema,
        {
            strict: true,
            decode: (fundingProjectData, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // Use the FundingProject aggregate's composite translation logic for intelligent fallback
                        const compositeTranslations = fundingProject.getCompositeTranslations(
                            locale,
                            fallbackLocale,
                        );

                        // Ensure we always provide a non-empty string for label
                        const nameCandidate = compositeTranslations.name.value;
                        const safeLabel =
                            typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                                ? nameCandidate
                                : fundingProjectData.id;

                        return {
                            value: fundingProjectData.id,
                            label: safeLabel,
                        };
                    },
                    catch(error) {
                        return new ParseResult.Type(
                            ast,
                            fundingProjectData,
                            error instanceof Error
                                ? error.message
                                : 'Failed to serialize funding project to select option',
                        );
                    },
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return pipe(
        fundingProject.toRaw(),
        Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
        Effect.catchAll((err) =>
            Effect.sync(() => {
                console.error(
                    `[Serializer][FundingProject SelectOption] Failed for funding project id=${fundingProject.getId()}:`,
                    err,
                );
                throw err;
            }),
        ),
    );
}

/**
 * Serializes database funding project data into the shape required for an edit form.
 *
 * This function transforms raw database funding project data (including all translations)
 * into the format expected by the frontend edit form. Unlike other serializers
 * that work with FundingProject aggregates and provide locale-based fallback behavior,
 * this function works directly with database data and preserves all available
 * translations for editing purposes.
 *
 * The resulting DTO contains all funding project fields plus an array of all available
 * translations, allowing the edit form to display and modify translations
 * in multiple locales simultaneously.
 */
export function serializeFundingProjectToFormEdit(
    fundingProjectData: FundingProjectData,
): Effect.Effect<FundingProjectEditResponseDTO, ParseResult.ParseError> {
    const transformer = Schema.transformOrFail(
        DbFundingProjectDataSchema,
        FundingProjectEditResponseDtoSchema,
        {
            strict: true,
            decode: (data, _options, ast) => {
                return ParseResult.try({
                    try: () => {
                        // Transform database translations to the format expected by the edit form
                        const transformedTranslations = data.translations.map(
                            (translation) => ({
                                name: {
                                    locale: translation.locale,
                                    value: translation.name ?? '',
                                },
                                description: {
                                    locale: translation.locale,
                                    value: translation.description,
                                },
                            }),
                        );

                        return {
                            id: data.id,
                            isActive: data.isActive ?? true,
                            holderId: data.holderId,
                            typeId: data.typeId,
                            fciId: data.fciId,
                            synchroId: data.synchroId,
                            obtainingYear: data.obtainingYear,
                            endDate: data.endDate,
                            translations: transformedTranslations,
                            modifiedBy: data.modifiedBy ?? null,
                            createdAt: data.createdAt,
                            updatedAt: data.updatedAt,
                            holder: null, // Will be populated from relations
                            fundingType: null, // Will be populated from relations
                            purchasedEquipment: [], // Will be populated from relations
                            associateResearchers: [], // Will be populated from relations
                            financedInfrastructures: [], // Will be populated from relations
                        };
                    },
                    catch(error) {
                        return new ParseResult.Type(
                            ast,
                            data,
                            error instanceof Error
                                ? error.message
                                : 'Failed to serialize funding project to edit form',
                        );
                    },
                });
            },
            encode: (val, _options, ast) =>
                ParseResult.fail(
                    new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                ),
        },
    );

    return Schema.decode(transformer)(fundingProjectData);
}

/**
 * Serializes a FundingProject aggregate into the client edit form shape expected by the frontend.
 * Includes resolved select options with value/label pairs.
 */
export function serializeFundingProjectToEditClient(
    fundingProject: FundingProject,
    holderLabel: string | null,
    fundingTypeLabel: string | null,
): Effect.Effect<
    {
        id: string;
        name: Array<{ locale: string; value: string }>;
        description: Array<{ locale: string; value: string }>;
        financingProjectType: { value: string; label: string };
        principalResearcher: { value: string; label: string };
        associateResearchers: Array<{ value: string; label: string }>;
        financedInfrastructures: Array<{ value: string; label: string }>;
        purchasedEquipment: Array<{ value: string; label: string }>;
        obtainingYear?: number;
        closingDate?: Date;
        synchroId: string;
        identificationFci: string;
        identificationIds: Array<{
            id?: string;
            identificationId: string;
            type: { locale: string; value: string };
        }>;
    },
    ParseResult.ParseError
> {
    return pipe(
        fundingProject.toRaw(),
        Effect.map((data) => {
            return {
                id: data.id,
                name: data.translations.map((t) => ({
                    locale: t.locale,
                    value: t.name ?? '',
                })),
                description: data.translations.map((t) => ({
                    locale: t.locale,
                    value: t.description ?? '',
                })),
                financingProjectType: {
                    value: data.typeId ?? '',
                    label: fundingTypeLabel ?? data.typeId ?? '',
                },
                principalResearcher: {
                    value: data.holderId ?? '',
                    label: holderLabel ?? data.holderId ?? '',
                },
                associateResearchers: [], // Default empty - will be populated by relations
                financedInfrastructures: [], // Default empty - will be populated by relations
                purchasedEquipment: [], // Default empty - will be populated by relations
                obtainingYear: data.obtainingYear ?? undefined,
                closingDate: data.endDate ? new Date(data.endDate) : undefined,
                synchroId: data.synchroId ?? '',
                identificationFci: data.fciId ?? '',
                identificationIds: [], // Default empty - will be populated by relations
            };
        }),
    );
}