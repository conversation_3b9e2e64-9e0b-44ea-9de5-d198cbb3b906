import {
  getDirectoryByIdOptions,
  getDirectoryListOptions,
} from '@/hooks/directory/directory-list.options';
import type { CollectionOptionsParams } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllVendorsOptions = <
  View extends CollectionViewParamType['view'],
>({
  locale,
  view,
}: CollectionOptionsParams<View>) =>
  getDirectoryListOptions({
    directoryListKey: 'supplier' as const,
    locale,
    view,
  });

export const getVendorByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getDirectoryByIdOptions({ directoryListKey: 'supplier', id, view } as const);
