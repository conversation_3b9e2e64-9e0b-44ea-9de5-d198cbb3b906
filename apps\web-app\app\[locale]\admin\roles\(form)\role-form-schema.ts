import {
  createOptionalStringOfMaxLengthSchema,
  createRequiredStringOfMaxLengthSchema,
} from '@rie/domain/schemas';
import { SelectOptionSchema } from '@rie/domain/schemas';
import * as Schema from 'effect/Schema';

export const roleFormSchema = Schema.Struct({
  name: createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 10,
    errorMessages: {
      required: () => 'Domain is required',
      maxLength: (issue) =>
        `Domain must be ${issue._tag.length} characters or less`,
    },
  }),
  description: createOptionalStringOfMaxLengthSchema({
    fieldMaxLength: 1500,
    maxLengthErrorMessage: (issue) =>
      `Description must be ${issue._tag.length} characters or less`,
  }),
  directPermissions: Schema.Array(SelectOptionSchema),
  permissionGroups: Schema.Array(SelectOptionSchema).pipe(
    Schema.minItems(1, {
      message: () => 'At least one permission group is required',
    }),
  ),
  parentRoles: Schema.Array(SelectOptionSchema),
});

export type RoleFormSchema = Schema.Schema.Type<typeof roleFormSchema>;
