# Monorepo Structure

This document explains the organization and structure of the RIE monorepo, built with Turborepo and pnpm workspaces.

## 🏗️ Overall Structure

```
rie-fullstack/
├── apps/                          # Applications (deployable units)
├── packages/                      # Shared libraries and configurations
├── docs/                          # Documentation
├── tests/                         # Integration test data and configs
├── .github/                       # GitHub workflows and templates
├── .vscode/                       # VS Code configuration
├── docker-compose.yml             # Development services
├── turbo.json                     # Turborepo configuration
├── pnpm-workspace.yaml            # pnpm workspace configuration
└── package.json                   # Root package.json
```

## 📱 Applications (`apps/`)

### `apps/api/` - Backend API Server

The main API server built with Hono and Effect-ts.

```
apps/api/
├── src/
│   ├── api/                       # API layer
│   │   └── v2/                    # API version 2
│   │       ├── middleware/        # API-specific middleware
│   │       └── routes/            # Route definitions
│   ├── application/               # Application use cases
│   │   ├── ports/                 # Input/Output ports (interfaces)
│   │   └── use-cases/             # Business use cases
│   ├── infrastructure/            # External services implementations
│   │   ├── config/                # Configuration management
│   │   ├── http/                  # HTTP-related functionality
│   │   ├── repositories/          # Repository implementations
│   │   ├── runtimes/              # Runtime configurations
│   │   └── services/              # External service implementations
│   ├── app.ts                     # Application setup
│   └── index.ts                   # Application entry point
├── drizzle/                       # Database migrations
├── package.json
├── tsconfig.json
└── .env.example
```

**Key Features**:

- Clean Architecture with clear layer separation
- Effect-ts for functional programming and dependency injection
- OpenAPI documentation generation
- Type-safe database operations with Drizzle

### `apps/web-app/` - Frontend Application

Next.js 15 application with App Router.

```
apps/web-app/
├── src/
│   ├── app/                       # Next.js App Router
│   │   ├── [locale]/              # Internationalized routes
│   │   ├── globals.css            # Global styles
│   │   └── layout.tsx             # Root layout
│   ├── components/                # React components
│   │   ├── ui/                    # Shadcn/UI components
│   │   ├── forms/                 # Form components
│   │   └── layout/                # Layout components
│   ├── lib/                       # Utility libraries
│   │   ├── api/                   # API client functions
│   │   ├── auth/                  # Authentication utilities
│   │   ├── hooks/                 # Custom React hooks
│   │   └── utils/                 # Utility functions
│   ├── stores/                    # Zustand stores
│   └── types/                     # TypeScript type definitions
├── public/                        # Static assets
├── messages/                      # i18n message files
├── .storybook/                    # Storybook configuration
├── package.json
├── next.config.js
└── tailwind.config.js
```

**Key Features**:

- Server-side rendering with Next.js App Router
- Internationalization (French/English)
- Type-safe API client
- Component library with Storybook

### `apps/web-app-e2e/` - End-to-End Tests

Playwright-based E2E testing suite.

```
apps/web-app-e2e/
├── tests/                         # Test files
├── playwright.config.ts           # Playwright configuration
├── package.json
└── .env.example
```

## 📦 Shared Packages (`packages/`)

### Core Business Logic

#### `packages/domain/` - Domain Layer

Contains business logic, domain models, and core types.

```
packages/domain/
├── src/
│   ├── schemas/                   # Zod schemas for validation
│   ├── types/                     # TypeScript type definitions
│   ├── transformers/              # Data transformation utilities
│   ├── utils/                     # Domain-specific utilities
│   ├── cache/                     # Caching utilities
│   └── policy/                    # Permission and policy logic
└── package.json
```

**Key Features**:

- Zod schemas for runtime validation
- Domain-driven design patterns
- Type-safe transformations
- Business rule implementations

#### `packages/db-schema/` - Database Schema

Drizzle schema definitions and migrations.

```
packages/db-schema/
├── src/
│   ├── auth/                      # Authentication schema
│   ├── main/                      # Main business schema
│   ├── migration-scripts/         # Migration utilities
│   └── index.ts                   # Schema exports
└── package.json
```

**Key Features**:

- Type-safe database schema definitions
- Multi-schema organization (auth, main)
- Migration management
- Relationship definitions

### Infrastructure & Utilities

#### `packages/postgres-db/` - Database Connection

PostgreSQL connection and configuration utilities.

```
packages/postgres-db/
├── src/
│   ├── connection.ts              # Database connection setup
│   ├── types.ts                   # Database type definitions
│   └── index.ts                   # Exports
└── package.json
```

#### `packages/auth/` - Authentication

Authentication utilities and Better Auth configuration.

```
packages/auth/
├── src/
│   ├── config.ts                  # Auth configuration
│   ├── types.ts                   # Auth type definitions
│   └── utils.ts                   # Auth utilities
└── package.json
```

#### `packages/utils/` - Utility Functions

Shared utility functions used across the monorepo.

```
packages/utils/
├── src/
│   ├── array.ts                   # Array utilities
│   ├── date.ts                    # Date utilities
│   ├── string.ts                  # String utilities
│   ├── validation.ts              # Validation utilities
│   └── index.ts                   # Exports
└── package.json
```

#### `packages/constants/` - Shared Constants

Application-wide constants and enums.

```
packages/constants/
├── src/
│   ├── api.ts                     # API-related constants
│   ├── app.ts                     # Application constants
│   ├── database.ts                # Database constants
│   └── index.ts                   # Exports
└── package.json
```

### Configuration Packages

#### `packages/typescript-config/` - TypeScript Configuration

Shared TypeScript configurations for different environments.

```
packages/typescript-config/
├── base.json                      # Base TypeScript config
├── nextjs.json                    # Next.js specific config
├── node.json                      # Node.js specific config
└── package.json
```

#### `packages/biome-config/` - Code Quality Configuration

Shared Biome configuration for linting and formatting.

```
packages/biome-config/
├── biome.json                     # Biome configuration
└── package.json
```

## 🔧 Configuration Files

### Root Level Configuration

#### `turbo.json` - Turborepo Configuration

Defines build pipeline and caching strategies.

```json
{
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "test": {
      "dependsOn": ["^build"]
    }
  }
}
```

#### `pnpm-workspace.yaml` - Workspace Configuration

Defines which directories are part of the workspace.

```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

#### `package.json` - Root Package Configuration

Defines workspace-level scripts and dependencies.

```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint"
  }
}
```

## 🔄 Dependency Management

### Internal Dependencies

Packages reference each other using the workspace protocol:

```json
{
  "dependencies": {
    "@rie/domain": "workspace:*",
    "@rie/utils": "workspace:*"
  }
}
```

### External Dependencies

- **Shared dependencies**: Installed at workspace root
- **Package-specific dependencies**: Installed in individual packages
- **Version synchronization**: Managed with Syncpack

### Dependency Graph

```
apps/api
├── @rie/domain
├── @rie/db-schema
├── @rie/postgres-db
├── @rie/auth
├── @rie/utils
└── @rie/constants

apps/web-app
├── @rie/domain
├── @rie/auth
├── @rie/utils
└── @rie/constants

packages/domain
├── @rie/utils
└── @rie/constants

packages/db-schema
└── @rie/constants
```

## 🚀 Build System

### Turborepo Pipeline

Turborepo orchestrates builds with dependency awareness:

1. **Shared packages** build first
2. **Applications** build after their dependencies
3. **Caching** prevents unnecessary rebuilds
4. **Parallel execution** where possible

### Build Outputs

- **packages/**: `dist/` directory with compiled TypeScript
- **apps/api/**: `dist/` directory with compiled server code
- **apps/web-app/**: `.next/` directory with Next.js build

## 🧪 Testing Strategy

### Package-Level Testing

Each package has its own test suite:

- Unit tests for utilities and business logic
- Integration tests for database operations
- Component tests for React components

### Application-Level Testing

- API integration tests
- Frontend component tests
- E2E tests for user workflows

### Cross-Package Testing

- Shared test utilities in packages
- Mock implementations for testing
- Integration tests across package boundaries

## 📝 Development Workflow

### Adding New Packages

1. Create directory in `packages/`
2. Add `package.json` with workspace dependencies
3. Update root `package.json` if needed
4. Add to Turborepo pipeline in `turbo.json`

### Adding New Applications

1. Create directory in `apps/`
2. Configure build and dev scripts
3. Add dependencies on shared packages
4. Update Docker configuration if needed

### Managing Dependencies

```bash
# Add dependency to specific package
cd packages/utils
pnpm add lodash

# Add dependency to workspace root
pnpm add -w typescript

# Update all dependencies
pnpm update -r

# Check for version mismatches
pnpm syncpack list-mismatches
```

## 🔍 Best Practices

### Package Design

- **Single Responsibility**: Each package has a clear purpose
- **Minimal Dependencies**: Avoid unnecessary dependencies
- **Clear Interfaces**: Well-defined exports and APIs
- **Documentation**: README for each package

### Dependency Management

- **Workspace Protocol**: Use `workspace:*` for internal deps
- **Version Consistency**: Keep external deps in sync
- **Peer Dependencies**: Use for shared libraries
- **Dev Dependencies**: Keep build tools at workspace root

### Code Organization

- **Consistent Structure**: Similar directory structure across packages
- **Clear Boundaries**: Respect package boundaries
- **Type Safety**: Export types from packages
- **Testing**: Test packages in isolation

---

**Next**: [API Architecture](./api-architecture.md) | [Frontend Architecture](./frontend-architecture.md)
