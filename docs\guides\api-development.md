# API Development Guide

This guide covers everything you need to know about developing and extending the RIE API server.

## 🚀 Getting Started

### Prerequisites

- Completed [installation guide](../getting-started/installation.md)
- Understanding of [API architecture](../architecture/api-architecture.md)
- Basic knowledge of TypeScript and functional programming

### Development Setup

```bash
# Navigate to API directory
cd apps/api

# Start development server
pnpm dev

# API will be available at http://localhost:4000
# API docs at http://localhost:4000/api/doc
```

## 🏗️ Project Structure

```text
apps/api/src/
├── api/v2/                        # HTTP layer
│   ├── middleware/                # HTTP middleware
│   └── routes/                    # Route definitions
├── application/                   # Business logic
│   ├── ports/                     # Interfaces
│   └── use-cases/                 # Use case implementations
├── infrastructure/                # External integrations
│   ├── repositories/              # Data access implementations
│   ├── services/                  # External services
│   └── config/                    # Configuration
├── app.ts                         # App setup
└── index.ts                       # Entry point
```

## 🛠️ Common Development Tasks

### Adding a New API Endpoint

#### 1. Define the Domain Model (if needed)

First, add domain models in `packages/domain/src/`:

```typescript
// packages/domain/src/models/equipment.ts
export class Equipment extends Data.Class<{
  readonly id: string
  readonly name: string
  readonly type: EquipmentType
  readonly status: EquipmentStatus
  readonly location: string
  readonly createdAt: Date
}> {
  // Domain methods
  isAvailable(): boolean {
    return this.status === EquipmentStatus.Available
  }
}

// Export from packages/domain/src/index.ts
export { Equipment } from './models/equipment'
```

#### 2. Create Repository Interface

Define the interface in `apps/api/src/application/ports/repositories/`:

```typescript
// equipment.repository.ts
export interface EquipmentRepository {
  findById(id: string): Effect.Effect<Equipment | null, DatabaseError>
  findAll(filters?: EquipmentFilters): Effect.Effect<Equipment[], DatabaseError>
  create(data: CreateEquipmentData): Effect.Effect<Equipment, DatabaseError>
  update(id: string, data: UpdateEquipmentData): Effect.Effect<Equipment, DatabaseError>
  delete(id: string): Effect.Effect<void, DatabaseError>
}
```

#### 3. Implement Repository

Create the implementation in `apps/api/src/infrastructure/repositories/`:

```typescript
// equipment.repository.impl.ts
export class EquipmentRepositoryImpl implements EquipmentRepository {
  constructor(private db: Database) {}

  findById(id: string): Effect.Effect<Equipment | null, DatabaseError> {
    return Effect.tryPromise({
      try: () => this.db.query.equipment.findFirst({
        where: eq(equipment.id, id)
      }),
      catch: (error) => new DatabaseError({ cause: error })
    }).pipe(
      Effect.map(data => data ? Equipment.make(data) : null)
    )
  }

  findAll(filters?: EquipmentFilters): Effect.Effect<Equipment[], DatabaseError> {
    return Effect.tryPromise({
      try: () => {
        let query = this.db.select().from(equipment)
        
        if (filters?.type) {
          query = query.where(eq(equipment.type, filters.type))
        }
        
        return query
      },
      catch: (error) => new DatabaseError({ cause: error })
    }).pipe(
      Effect.map(data => data.map(Equipment.make))
    )
  }

  // ... other methods
}
```

#### 4. Create Use Cases

Implement business logic in `apps/api/src/application/use-cases/equipment/`:

```typescript
// create-equipment.use-case.ts
interface CreateEquipmentDeps {
  equipmentRepository: EquipmentRepository
  permissionService: PermissionService
}

export const createEquipment = (data: CreateEquipmentData) =>
  Effect.gen(function* (_) {
    const { equipmentRepository, permissionService } = 
      yield* _(Effect.service<CreateEquipmentDeps>())
    
    // Check permissions
    const user = yield* _(Effect.service<AuthenticatedUser>())
    const canCreate = yield* _(
      permissionService.canCreateEquipment(user.id)
    )
    
    if (!canCreate) {
      yield* _(Effect.fail(new UnauthorizedError()))
    }
    
    // Validate business rules
    yield* _(validateEquipmentData(data))
    
    // Create equipment
    const equipment = yield* _(equipmentRepository.create(data))
    
    return equipment
  })
```

#### 5. Create API Routes

Add routes in `apps/api/src/api/v2/routes/`:

```typescript
// equipment.routes.ts
import { createRoute, OpenAPIHono } from '@hono/zod-openapi'
import { z } from 'zod'

const app = new OpenAPIHono()

// Define schemas
const EquipmentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['MICROSCOPE', 'CENTRIFUGE', 'SPECTROMETER']),
  status: z.enum(['AVAILABLE', 'IN_USE', 'MAINTENANCE']),
  location: z.string(),
  createdAt: z.string().datetime()
})

const CreateEquipmentSchema = z.object({
  name: z.string().min(1),
  type: z.enum(['MICROSCOPE', 'CENTRIFUGE', 'SPECTROMETER']),
  location: z.string().min(1)
})

// Define routes
const listEquipmentRoute = createRoute({
  method: 'get',
  path: '/equipment',
  summary: 'List equipment',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.array(EquipmentSchema)
        }
      },
      description: 'List of equipment'
    }
  }
})

const createEquipmentRoute = createRoute({
  method: 'post',
  path: '/equipment',
  summary: 'Create equipment',
  request: {
    body: {
      content: {
        'application/json': {
          schema: CreateEquipmentSchema
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: EquipmentSchema
        }
      },
      description: 'Created equipment'
    }
  }
})

// Implement handlers
app.openapi(listEquipmentRoute, async (c) => {
  const result = await Effect.runPromise(
    listEquipment().pipe(
      Effect.provide(createAppLayer())
    )
  )
  
  return c.json(result, 200)
})

app.openapi(createEquipmentRoute, async (c) => {
  const data = c.req.valid('json')
  
  const result = await Effect.runPromise(
    createEquipment(data).pipe(
      Effect.provide(createAppLayer())
    )
  )
  
  return c.json(result, 201)
})

export { app as equipmentRoutes }
```

#### 6. Register Routes

Add to main routes file:

```typescript
// apps/api/src/api/v2/routes/index.ts
import { equipmentRoutes } from './equipment.routes'

export const apiRoutes = new Hono()
  .route('/users', userRoutes)
  .route('/equipment', equipmentRoutes)  // Add this line
  // ... other routes
```

### Working with Database

#### Running Migrations

```bash
cd apps/api

# Generate migration from schema changes
bun run db:generate-pg

# Apply migrations
bun run db:migrate-pg

# Open database studio
bun run db:studio-pg
```

#### Adding New Tables

1. Define schema in `packages/db-schema/src/main/`:

```typescript
// equipment.schema.ts
export const equipment = pgTable('equipment', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(),
  status: varchar('status', { length: 50 }).notNull().default('AVAILABLE'),
  location: varchar('location', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})
```

2. Export from schema index:

```typescript
// packages/db-schema/src/main/index.ts
export { equipment } from './equipment.schema'
```

3. Generate and run migration:

```bash
bun run db:generate-pg
bun run db:migrate-pg
```

### Error Handling

#### Define Custom Errors

```typescript
// Domain errors
export class EquipmentNotFoundError extends Data.TaggedError("EquipmentNotFoundError")<{
  readonly equipmentId: string
}> {}

export class EquipmentInUseError extends Data.TaggedError("EquipmentInUseError")<{
  readonly equipmentId: string
  readonly currentUser: string
}> {}
```

#### Handle Errors in Routes

```typescript
app.openapi(getEquipmentRoute, async (c) => {
  const { id } = c.req.valid('param')
  
  const result = await Effect.runPromise(
    getEquipment(id).pipe(
      Effect.provide(createAppLayer()),
      Effect.catchAll((error) => {
        if (error._tag === 'EquipmentNotFoundError') {
          return Effect.succeed({ error: 'Equipment not found', status: 404 })
        }
        return Effect.succeed({ error: 'Internal server error', status: 500 })
      })
    )
  )
  
  if ('error' in result) {
    return c.json({ error: result.error }, result.status)
  }
  
  return c.json(result, 200)
})
```

### Authentication & Authorization

#### Protecting Routes

```typescript
// Add auth middleware to routes that need protection
app.use('/equipment/*', authMiddleware)

// Or protect specific routes
app.openapi(createEquipmentRoute, authMiddleware, async (c) => {
  // Route handler
})
```

#### Checking Permissions in Use Cases

```typescript
const updateEquipment = (id: string, data: UpdateEquipmentData) =>
  Effect.gen(function* (_) {
    const { equipmentRepository, permissionService } = 
      yield* _(Effect.service<UpdateEquipmentDeps>())
    
    const user = yield* _(Effect.service<AuthenticatedUser>())
    
    // Check if user can update this specific equipment
    const canUpdate = yield* _(
      permissionService.canUpdateEquipment(user.id, id)
    )
    
    if (!canUpdate) {
      yield* _(Effect.fail(new UnauthorizedError()))
    }
    
    // Proceed with update
    const equipment = yield* _(equipmentRepository.update(id, data))
    return equipment
  })
```

## 🧪 Testing

### Unit Tests

Test individual functions and domain logic:

```typescript
// equipment.test.ts
import { describe, it, expect } from 'vitest'
import { Equipment, EquipmentStatus } from '@rie/domain'

describe('Equipment domain model', () => {
  it('should determine availability correctly', () => {
    const equipment = Equipment.make({
      id: '1',
      name: 'Microscope',
      type: 'MICROSCOPE',
      status: EquipmentStatus.Available,
      location: 'Lab A',
      createdAt: new Date()
    })
    
    expect(equipment.isAvailable()).toBe(true)
  })
})
```

### Integration Tests

Test use cases with mock dependencies:

```typescript
// create-equipment.test.ts
import { describe, it, expect } from 'vitest'
import { Effect, Layer } from 'effect'
import { createEquipment } from '../use-cases/equipment/create-equipment'

describe('Create equipment use case', () => {
  it('should create equipment successfully', async () => {
    const mockRepository = {
      create: vi.fn().mockReturnValue(Effect.succeed(mockEquipment))
    }
    
    const mockPermissionService = {
      canCreateEquipment: vi.fn().mockReturnValue(Effect.succeed(true))
    }
    
    const result = await Effect.runPromise(
      createEquipment(equipmentData).pipe(
        Effect.provide(Layer.succeed({
          equipmentRepository: mockRepository,
          permissionService: mockPermissionService
        }))
      )
    )
    
    expect(result).toEqual(mockEquipment)
    expect(mockRepository.create).toHaveBeenCalledWith(equipmentData)
  })
})
```

### API Tests

Test HTTP endpoints:

```typescript
// equipment.api.test.ts
import { describe, it, expect } from 'vitest'
import { app } from '../app'

describe('Equipment API', () => {
  it('should create equipment', async () => {
    const response = await app.request('/api/v2/equipment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer valid-token'
      },
      body: JSON.stringify({
        name: 'Test Microscope',
        type: 'MICROSCOPE',
        location: 'Lab A'
      })
    })
    
    expect(response.status).toBe(201)
    const result = await response.json()
    expect(result).toMatchObject({
      name: 'Test Microscope',
      type: 'MICROSCOPE'
    })
  })
})
```

## 🔧 Development Tools

### Database Tools

```bash
# Open Drizzle Studio
bun run db:studio-pg

# Reset database (development only)
bun run db:reset-pg

# Seed database
bun run db:seed-pg
```

### API Documentation

The API automatically generates OpenAPI documentation:

- **Swagger UI**: <http://localhost:4000/api/doc>
- **OpenAPI JSON**: <http://localhost:4000/api/doc/json>

### Debugging

#### VS Code Debugging

Add to `.vscode/launch.json`:

```json
{
  "name": "Debug API",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/apps/api/src/index.ts",
  "cwd": "${workspaceFolder}/apps/api",
  "runtimeExecutable": "bun",
  "runtimeArgs": ["--inspect"],
  "env": {
    "NODE_ENV": "development"
  }
}
```

#### Console Debugging

```typescript
// Add logging to Effect chains
const createEquipment = (data: CreateEquipmentData) =>
  Effect.gen(function* (_) {
    yield* _(Effect.log(`Creating equipment: ${data.name}`))
    
    const equipment = yield* _(equipmentRepository.create(data))
    
    yield* _(Effect.log(`Created equipment with ID: ${equipment.id}`))
    
    return equipment
  })
```

## 📊 Performance Tips

### Database Optimization

```typescript
// Use select() for specific columns
const findEquipmentSummary = () =>
  Effect.tryPromise({
    try: () => db.select({
      id: equipment.id,
      name: equipment.name,
      status: equipment.status
    }).from(equipment),
    catch: (error) => new DatabaseError({ cause: error })
  })

// Use joins instead of separate queries
const findEquipmentWithLocation = () =>
  Effect.tryPromise({
    try: () => db.select()
      .from(equipment)
      .leftJoin(locations, eq(equipment.locationId, locations.id)),
    catch: (error) => new DatabaseError({ cause: error })
  })
```

### Caching

```typescript
// Cache frequently accessed data
const getCachedEquipmentTypes = Effect.cached(
  getEquipmentTypes(),
  Duration.minutes(30)
)
```

## 🚀 Deployment

### Environment Variables

Ensure all required environment variables are set:

```env
# Database
PG_DATABASE_URL=postgresql://user:pass@localhost:5432/rie_prod

# Authentication
BETTER_AUTH_SECRET=your-production-secret
BETTER_AUTH_URL=https://your-domain.com

# API
PORT=8000
NODE_ENV=production
```

### Health Checks

The API includes health check endpoints:

```typescript
// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
})
```

## 🆘 Troubleshooting

### Common Issues

#### Database Connection Errors

```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check connection string
echo $PG_DATABASE_URL
```

#### Effect-ts Type Errors

- Ensure all dependencies are properly provided in the Effect chain
- Check that service interfaces match implementations
- Use `Effect.provide()` to supply dependencies

#### Authentication Issues

- Verify JWT token format and expiration
- Check middleware order (auth middleware should run before protected routes)
- Ensure user context is properly set

---

**Next**: [Frontend Development Guide](./frontend-development.md) | [Testing Guide](./testing.md)
