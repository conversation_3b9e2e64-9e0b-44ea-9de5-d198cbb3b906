import type { PermissionsGroupFormSchema } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form.schema';
import { useToast } from '@/components/hooks/use-toast';
import {
  getAllPermissionsGroupsOptions,
  getPermissionsGroupByIdOptions,
} from '@/hooks/admin/permissions/permissions-groups.options';
import { useRouter } from '@/lib/navigation';
import {
  createPermissionsGroup,
  deletePermissionsGroup,
  updatePermissionsGroup,
} from '@/services/permissions/permissions-groups.service';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const useGetAllPermissionsGroups = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return useQuery(getAllPermissionsGroupsOptions<View>({ view }));
};

interface GetPermissionsGroupByIdParams {
  id: string;
  view: ResourceViewType;
}

export const useGetPermissionsGroupById = <View extends ResourceViewType>({
  id,
  view,
}: GetPermissionsGroupByIdParams) => {
  return useQuery(getPermissionsGroupByIdOptions<View>({ id, view }));
};

export const useCreatePermissionsGroup = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (payload: PermissionsGroupFormSchema) => {
      return await createPermissionsGroup(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissionGroups'] });
      toast({
        title: 'Success',
        description: 'Permission group created successfully',
        variant: 'success',
      });
      router.push('/admin/groupes-permissions');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to create permission group',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdatePermissionsGroups = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      payload,
      id,
    }: { payload: PermissionsGroupFormSchema; id: string }) =>
      updatePermissionsGroup({ id, payload }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissionGroups'] });
    },
  });
};

export const useDeletePermissionsGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deletePermissionsGroup(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissionGroups'] });
    },
  });
};
