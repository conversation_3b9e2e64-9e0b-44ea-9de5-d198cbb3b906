import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { getInfrastructureByIdWithRelatedEquipments } from '@/services/infrastructures.service';
import { mapInfrastructureWithEquipmentsToDetails } from '@/services/mappers/infrastructure.mapper';
import type { SupportedLocale } from '@/types/locale';
import type { InfrastructureWithRelatedEquipments } from '@rie/domain/schemas';
import { queryOptions, useQuery } from '@tanstack/react-query';
import type { HTTPError } from 'ky';

export function infrastructureByIdWithAssociatedEquipmentsOptions(
  id: string,
  locale: SupportedLocale,
) {
  return queryOptions<
    InfrastructureWithRelatedEquipments,
    HTTPError,
    ReturnType<typeof mapInfrastructureWithEquipmentsToDetails>
  >({
    queryFn: () => getInfrastructureByIdWithRelatedEquipments(id, locale),
    queryKey: ['infrastructure', id, locale, { view: 'detail' }],
    select: (data) => mapInfrastructureWithEquipmentsToDetails(data, locale),
  });
}

export const useGetInfrastructureByIdWithAssociatedEquipments = (
  id: string,
) => {
  const locale = useAvailableLocale();
  return useQuery(
    infrastructureByIdWithAssociatedEquipmentsOptions(id, locale),
  );
};
