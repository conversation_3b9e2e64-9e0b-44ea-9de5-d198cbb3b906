import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DuplicateLocaleError } from '../errors';
import { DbVendorI18NCreationSchema } from '../schemas';
import type { VendorTranslationFields } from '../types';
import type { VendorTranslation } from './vendor-translation.vo';

/**
 * A value object representing a collection of VendorTranslations.
 */
export class VendorTranslationCollection {
  private constructor(
    public readonly translations: readonly VendorTranslation[],
  ) { }

  static create(
    translations: readonly VendorTranslation[],
  ): Effect.Effect<VendorTranslationCollection, DuplicateLocaleError> {
    return pipe(
      Effect.succeed(translations),
      Effect.flatMap(ensureNoDuplicateLocales),
      Effect.map(
        (validTranslations) =>
          new VendorTranslationCollection(validTranslations),
      ),
    );
  }

  /**
   * Converts the translation collection to a persistence-ready array format.
   *
   * This method transforms the domain value objects into a format suitable for database
   * persistence operations (updates/inserts). It ensures that all translations have
   * required database identifiers and validates the data structure.
   *
   * **Purpose:**
   * - Prepares translation data for database persistence operations
   * - Validates that all translations have required database IDs
   * - Transforms domain objects to database-compatible format
   *
   * **Schema Transformation:**
   * - Source: `DbVendorI18NCreationSchema` (dataId, locale, name, website, description, otherNames)
   * - Target: `DbVendorI18NCreationSchema + { id: string }` (adds required id field)
   *
   * **Validation Rules:**
   * - All translations must have a non-undefined `id` field
   * - Data must conform to `DbVendorI18NCreationSchema` structure
   * - Encoding operations are explicitly forbidden (decode-only transformer)
   *
   * **Error Scenarios:**
   * - `ParseResult.ParseError` if any translation lacks an `id` field
   * - `ParseResult.ParseError` if data doesn't match expected schema structure
   *
   * @returns Effect containing array of validated translation objects with required IDs
   * @throws ParseResult.ParseError when translations are missing IDs or invalid
   *
   * @example
   * ```typescript
   * // Usage in repository layer for database updates
   * const persistenceData = yield* collection.toPersistenceArray();
   * yield* dbClient.update(vendorsI18N).set(persistenceData);
   * ```
   *
   * @example
   * ```typescript
   * // Error case - translation without ID
   * const invalidCollection = yield* VendorTranslationCollection.create([
   *   yield* VendorTranslation.create({ locale: 'en', name: 'Test', dataId: 'vendor1' })
   *   // Missing id field will cause toPersistenceArray to fail
   * ]);
   * const result = yield* invalidCollection.toPersistenceArray(); // Throws ParseError
   * ```
   */
  toPersistenceArray(): Effect.Effect<
    (VendorTranslationFields & { id: string })[],
    ParseResult.ParseError
  > {
    // Create target schema that extends creation schema with required ID field
    const TargetSchema = Schema.extend(
      DbVendorI18NCreationSchema,
      Schema.Struct({ id: Schema.String }),
    );

    // Schema transformer that validates presence of ID field
    const transformer = Schema.transformOrFail(
      TargetSchema, // Use TargetSchema as input since we've already added IDs
      TargetSchema,
      {
        strict: true,
        decode: (item, _, ast) => {
          // Item should already have an ID at this point
          return ParseResult.succeed(item);
        },
        // Explicitly forbid encoding to ensure this is decode-only
        encode: (val, _options, ast) =>
          ParseResult.fail(
            new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
          ),
      },
    );

    // Convert domain objects to raw data format
    const rawTranslations = this.translations.map((t) => t.toRaw());

    // Apply validation and transformation to all translations
    // Add temporary IDs to items that don't have them
    const itemsWithIds = rawTranslations.map(
      (item): VendorTranslationFields & { id: string } => {
        if (item.id !== undefined && item.id !== null && item.id.length > 0) {
          return item as VendorTranslationFields & { id: string };
        }
        // Generate temporary ID for items without one
        return {
          ...item,
          id: `temp_${Date.now()}_${Math.random()}`,
        } as VendorTranslationFields & { id: string };
      },
    );

    return Effect.all(
      itemsWithIds.map((item) => Schema.decode(transformer)(item)),
    );
  }

  /**
   * Finds a single translation for a specific locale, without any fallbacks.
   */
  findByLocale(locale: string): VendorTranslation | undefined {
    return this.translations.find((t) => t.getLocale() === locale);
  }

  /**
   * Builds a composite object of translation fields with locale-based fallbacks.
   *
   * Implements a three-tier fallback strategy for each field:
   * 1. Primary locale (requested locale)
   * 2. Fallback locale
   * 3. Any available locale with non-empty value
   * 4. Graceful degradation (requested locale with null value)
   */
  getCompositeTranslations(locale: string, fallbackLocale: string) {
    const isValidValue = (
      value: string | null | undefined,
    ): value is string => {
      return value !== null && value !== undefined && value.trim() !== '';
    };

    const findField = (getter: (t: VendorTranslation) => string | null) => {
      // Tier 1: Try primary locale first
      const primaryTranslation = this.findByLocale(locale);
      if (primaryTranslation) {
        const primaryValue = getter(primaryTranslation);
        if (isValidValue(primaryValue)) {
          return { locale, value: primaryValue };
        }
      }

      // Tier 2: Try fallback locale if different from primary
      if (fallbackLocale !== locale) {
        const fallbackTranslation = this.findByLocale(fallbackLocale);
        if (fallbackTranslation) {
          const fallbackValue = getter(fallbackTranslation);
          if (isValidValue(fallbackValue)) {
            return { locale: fallbackLocale, value: fallbackValue };
          }
        }
      }

      // Tier 3: Search through all other available translations
      for (const translation of this.translations) {
        const translationLocale = translation.getLocale();
        // Skip locales we've already checked
        if (
          translationLocale !== locale &&
          translationLocale !== fallbackLocale
        ) {
          const value = getter(translation);
          if (isValidValue(value)) {
            return { locale: translationLocale, value };
          }
        }
      }

      // Tier 4: Graceful degradation - return requested locale with null value
      return { locale, value: null };
    };

    return {
      name: findField((t) => t.getName()),
      website: findField((t) => t.getWebsite()),
      description: findField((t) => t.getDescription()),
      otherNames: findField((t) => t.getOtherNames()),
    };
  }

  /**
   * Returns all VendorTranslation objects in the collection.
   */
  getAll(): readonly VendorTranslation[] {
    return this.translations;
  }

  /**
   * Checks if the collection is empty.
   */
  isEmpty(): boolean {
    return this.translations.length === 0;
  }

  get length(): number {
    return this.translations.length;
  }
}

// --- Private Invariant Enforcement Functions ---

const ensureNoDuplicateLocales = (
  translations: readonly VendorTranslation[],
): Effect.Effect<readonly VendorTranslation[], DuplicateLocaleError> => {
  const locales = translations.map((t) => t.getLocale());
  const uniqueLocales = new Set(locales);

  if (locales.length !== uniqueLocales.size) {
    const duplicates = locales.filter(
      (locale, index) => locales.indexOf(locale) !== index,
    );
    const uniqueDuplicates = [...new Set(duplicates)];

    return Effect.fail(DuplicateLocaleError.create(uniqueDuplicates));
  }

  return Effect.succeed(translations);
};
