{"name": "@rie/config", "version": "1.0.0", "type": "module", "description": "Config package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "devDependencies": {"@rie/biome-config": "workspace:*", "@types/bun": "^1.2.21", "dotenv": "^16.5.0", "typescript": "^5.9.2"}, "peerDependencies": {"effect": "^3.17.13"}}