import { GeneralInfo } from '@/app/[locale]/bottin/locaux/form/sections/general-info';
import { FormSection } from '@/components/form-section/form-section';
import { roomFormSections } from '@/constants/directory/room';
import { Card, CardContent, CardHeader, CardTitle } from '@/ui/card';
import { useTranslations } from 'next-intl';

export const Description = () => {
  const t = useTranslations('rooms.form.sections.description');

  return (
    <Card id={roomFormSections.description.key}>
      <CardHeader>
        <CardTitle className="text-3xl">{t('title')}</CardTitle>
      </CardHeader>
      <CardContent>
        <FormSection>
          <GeneralInfo />
        </FormSection>
      </CardContent>
    </Card>
  );
};
