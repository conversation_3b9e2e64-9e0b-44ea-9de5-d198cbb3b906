import { UnitFormRequestDtoSchema } from '@rie/api-contracts';
import { DbUnitI18NInputSchema, UnitInputSchema } from '@rie/domain/schemas';
import type { UnitI18NInput, Writable } from '@rie/domain/types';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const UnitFormToDomainInput = Schema.transformOrFail(
  UnitFormRequestDtoSchema,
  UnitInputSchema,
  {
    strict: true,
    decode: (formDto, _, ast) => {
      return ParseResult.try({
        try: () => {
          const translationsMap = new Map<string, Writable<UnitI18NInput>>();

          const ensureLocale = (locale: string) => {
            if (!translationsMap.has(locale)) {
              translationsMap.set(locale, {
                locale,
                name: null,
                description: null,
                otherNames: null,
                acronyms: null,
              });
            }
          };

          for (const item of formDto.names) {
            ensureLocale(item.locale);
            const itemFound = translationsMap.get(item.locale);
            if (itemFound) {
              itemFound.name = item.value;
            }
          }

          for (const item of formDto.description) {
            ensureLocale(item.locale);
            const itemFound = translationsMap.get(item.locale);
            if (itemFound) {
              itemFound.description = item.value;
            }
          }

          for (const item of formDto.acronyms) {
            ensureLocale(item.locale);
            const itemFound = translationsMap.get(item.locale);
            if (itemFound) {
              itemFound.acronyms = item.value;
            }
          }

          for (const item of formDto.otherNames) {
            ensureLocale(item.locale);
            const itemFound = translationsMap.get(item.locale);
            if (itemFound) {
              itemFound.otherNames = item.value;
            }
          }

          const translations = Array.from(translationsMap.values()).map((t) =>
            Schema.decodeSync(DbUnitI18NInputSchema)(t),
          );

          return {
            isActive: formDto.isActive,
            modifiedBy: null,
            guidId: formDto.id,
            typeId: formDto.type.value,
            parentId: formDto.parent.value,
            translations: translations,
          };
        },
        catch: (error) =>
          new ParseResult.Type(ast, formDto, (error as Error).message),
      });
    },
    encode: (domainInput, _, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(
          ast,
          domainInput,
          'Encoding from domain to form is not supported by this transformer',
        ),
      ),
  },
);
