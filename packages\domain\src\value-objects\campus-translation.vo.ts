import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type { ParseError } from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DbCampusI18NDataSchema } from '../schemas';
import type { CampusTranslationFields } from '../types';

export const CampusTranslationData = Data.case<CampusTranslationFields>();

/**
 * Represents a single, immutable, and validated translation for a campus.
 * Its responsibility is to hold the value and enforce its invariants.
 * It does not handle data transformation between layers.
 */
export class CampusTranslation {
  // The constructor is kept private to enforce creation via the validated factory method.
  private constructor(private readonly data: CampusTranslationFields) {}

  /**
   * Creates a new CampusTranslation value object from raw data.
   * It validates the input against the CampusTranslationSchema.
   * This is the single, authoritative entry point for creating a valid CampusTranslation.
   */
  static create(input: unknown): Effect.Effect<CampusTranslation, ParseError> {
    return pipe(
      Schema.decodeUnknown(DbCampusI18NDataSchema)(input),
      Effect.map(
        (validatedData) =>
          new CampusTranslation(CampusTranslationData(validatedData)),
      ),
    );
  }

  // --- Accessors ---

  getLocale() {
    return this.data.locale;
  }

  getName() {
    return this.data.name;
  }

  /**
   * Converts the value object to its raw data representation.
   * Useful for persistence or serialization.
   */
  toRaw(): CampusTranslationFields {
    return this.data;
  }

  /**
   * Checks for equality with another CampusTranslation object based on its values.
   */
  equals(other: CampusTranslation): boolean {
    return (
      this.data.locale === other.data.locale &&
      this.data.name === other.data.name
    );
  }
}
