'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { Button } from '@/components/ui/button';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';
import type { CampusListItemDTO } from '../../../../../../packages/api-contracts/build/dts/campuses';
import { useDeleteDirectoryEntity } from '@/hooks/directory/use-delete-directory-entity';

export const campusColumns = (
  _locale: SupportedLocale,
): ColumnDef<CampusListItemDTO>[] => {
  const directoryEntity = 'campus' as const;

  return [
    {
      accessorKey: 'text',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'jurisdiction',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.jurisdiction"
        />
      ),
      id: 'jurisdiction',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'createdAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.createdAt"
        />
      ),
      id: 'createdAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <CampusActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const CampusActions = ({ row }: { row: Row<CampusListItemDTO> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteDirectoryEntity } = useDeleteDirectoryEntity('campus');
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.campus;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-campus"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteDirectoryEntity(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.text })}
        trigger={
          <Button size="icon" variant="destructive" data-testid="delete-campus">
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
