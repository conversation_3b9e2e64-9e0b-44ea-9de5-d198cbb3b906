import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { RoomMigrationConverter } from '../converters/31-01-convert-room-inserts';

async function convertRoomData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new RoomMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertRoomData().catch(console.error);
