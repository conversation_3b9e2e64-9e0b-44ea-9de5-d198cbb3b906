import {
  getAllPermissionsGroups,
  getPermissionsGroupById,
} from '@/services/permissions/permissions-groups.service';
import type {
  CollectionPermissionGroupResultType,
  ResourcePermissionGroupResultType,
} from '@/types/permission.type';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getAllPermissionsGroupsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return queryOptions<CollectionPermissionGroupResultType<View>>({
    queryFn: () => getAllPermissionsGroups({ view }),
    queryKey: ['permissionGroups'],
  });
};

interface GetPermissionsGroupByIdParams {
  id: string;
  view: ResourceViewType;
}

export const getPermissionsGroupByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: GetPermissionsGroupByIdParams) => {
  return queryOptions<ResourcePermissionGroupResultType<View>>({
    queryFn: () => getPermissionsGroupById({ id, view }),
    queryKey: ['permissionGroups', id, view],
    enabled: !!id,
  });
};
