'use client';

import { RoleForm } from '@/app/[locale]/admin/roles/(form)/role-form';
import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form-schema';
import { useCreateRole } from '@/hooks/admin/permissions/roles.hook';

export const NewRole = () => {
  const { mutate, status } = useCreateRole();

  const handleOnSubmit = (data: RoleFormSchema) => {
    mutate(data);
  };

  return <RoleForm onSubmitAction={handleOnSubmit} status={status} />;
};
