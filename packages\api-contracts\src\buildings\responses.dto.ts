import * as Schema from 'effect/Schema';
import {
    OptionalLocalizedFieldDtoSchema,
    RequiredLocalizedFieldDtoSchema,
} from '../common';

// Base schema for buildings
export const BuildingBaseDtoSchema = Schema.Struct({
    id: Schema.String,
    isActive: Schema.Boolean,
    sadId: Schema.NullOr(Schema.String),
    campusId: Schema.NullOr(Schema.String),
});

// DTO for lists of buildings (e.g., view='list')
export const BuildingListItemDtoSchema = Schema.Struct({
    id: Schema.String,
    text: Schema.String,
    campus: Schema.NullOr(Schema.String),
    lastUpdatedAt: Schema.String,
    createdAt: Schema.String,
    uid: Schema.NullOr(Schema.String),
});

export type BuildingListItemDTO = Schema.Schema.Type<
    typeof BuildingListItemDtoSchema
>;

// Building-specific translation schemas
export const BuildingTranslationDtoSchema = Schema.Struct({
    name: RequiredLocalizedFieldDtoSchema,
    alias: OptionalLocalizedFieldDtoSchema,
});

export const BuildingTranslationInputDtoSchema = Schema.Struct({
    name: OptionalLocalizedFieldDtoSchema,
    alias: OptionalLocalizedFieldDtoSchema,
});

// DTO for the detailed view of a single building (e.g., view='detail')
export const BuildingDetailResponseDtoSchema = Schema.extend(
    BuildingBaseDtoSchema,
    Schema.Struct({
        translations: BuildingTranslationDtoSchema,
    }),
);

export type BuildingDetailResponseDTO = Schema.Schema.Type<
    typeof BuildingDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const BuildingEditResponseDtoSchema = Schema.extend(
    BuildingBaseDtoSchema,
    Schema.Struct({
        translations: Schema.Array(BuildingTranslationInputDtoSchema),
    }),
);
export type BuildingEditResponseDTO = Schema.Schema.Type<
    typeof BuildingEditResponseDtoSchema
>;