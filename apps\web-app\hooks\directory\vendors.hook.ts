import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import {
  createDirectory,
  deleteDirectory,
  updateDirectory,
} from '@/services/directory/directory-list.service';
import type { VendorEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateVendor = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<VendorEdit, Error, VendorFormSchema>({
    mutationFn: async (payload) =>
      (await createDirectory({
        directoryListKey: 'supplier',
        payload,
      })) as unknown as VendorEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'supplier'],
      });
      toast({
        title: 'Success',
        description: 'Vendor created successfully',
        variant: 'success',
      });
      router.push('/bottin/manufacturiers');
    },
  });
};

export const useUpdateVendor = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    VendorEdit,
    Error,
    { id: string; payload: VendorFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateDirectory({
        directoryListKey: 'supplier',
        id,
        payload,
      })) as unknown as VendorEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'supplier'],
      });
      toast({
        title: 'Success',
        description: 'Vendor updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeleteVendor = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteDirectory({ directoryListKey: 'supplier', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['directory', 'supplier'],
      });
      toast({
        title: 'Success',
        description: 'Vendor deleted successfully',
        variant: 'success',
      });
    },
  });
};
