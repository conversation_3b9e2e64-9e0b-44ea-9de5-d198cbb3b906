import { serviceOfferRawToResponse } from '@rie/domain/serializers';
import {
  LocaleRepositoryLive,
  ServiceOfferRepositoryLive,
} from '@rie/repositories';
import { Effect } from 'effect';

export class ServiceOfferServiceLive extends Effect.Service<ServiceOfferServiceLive>()(
  'ServiceOfferServiceLive',
  {
    dependencies: [
      ServiceOfferRepositoryLive.Default,
      LocaleRepositoryLive.Default,
    ],
    effect: Effect.gen(function* () {
      const findById = (id: string, locale: string) => {
        return Effect.gen(function* () {
          const serviceOfferRepository = yield* ServiceOfferRepositoryLive;
          const localeRepository = yield* LocaleRepositoryLive;
          const fallbackLocale =
            yield* localeRepository.getFallbackLocale(locale);

          const maybeRawServiceOffer =
            yield* serviceOfferRepository.findById(id);

          if (!maybeRawServiceOffer) {
            return yield* Effect.fail(new Error('Service Offer not found'));
          }

          return serviceOfferRawToResponse(
            locale,
            fallbackLocale,
            maybeRawServiceOffer,
          );
        });
      };

      return { findById } as const;
    }),
  },
) {}
