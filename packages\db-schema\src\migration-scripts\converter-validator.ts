/**
 * Per-Converter Data Validation Helper
 *
 * This utility can be used by individual converters to validate their specific
 * table conversions during the migration process.
 */

import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from './constants';
import { BaseConverter } from './converters/base-converter';

interface ConverterValidationResult {
  converterName: string;
  mysqlTable: string;
  postgresTable: string;
  mysqlRecords: number;
  postgresRecords: number;
  match: boolean;
  status: 'PASS' | 'FAIL' | 'WARNING';
  details?: string;
}

export class ConverterValidator extends BaseConverter {
  /**
   * Validate a specific table conversion
   */
  async validateTableConversion(
    converterName: string,
    mysqlTableName: string,
    postgresTableName: string,
    expectedRecords?: number,
  ): Promise<ConverterValidationResult> {
    const mysqlContent = await fs.readFile(INPUT_PATH, 'utf8');
    const postgresContent = await fs.readFile(OUTPUT_PATH, 'utf8');

    const mysqlCount = this.countMySQLRecords(mysqlContent, mysqlTableName);
    const postgresCount = this.countPostgreSQLRecords(
      postgresContent,
      postgresTableName,
    );

    let status: 'PASS' | 'FAIL' | 'WARNING' = 'PASS';
    let details = '';

    if (mysqlCount !== postgresCount) {
      status = 'FAIL';
      details = `Record count mismatch: MySQL has ${mysqlCount}, PostgreSQL has ${postgresCount}`;
    } else if (expectedRecords && mysqlCount !== expectedRecords) {
      status = 'WARNING';
      details = `Expected ${expectedRecords} records, but found ${mysqlCount}`;
    } else if (mysqlCount === 0) {
      status = 'WARNING';
      details = 'No records found in either source or destination';
    }

    return {
      converterName,
      mysqlTable: mysqlTableName,
      postgresTable: postgresTableName,
      mysqlRecords: mysqlCount,
      postgresRecords: postgresCount,
      match: mysqlCount === postgresCount,
      status,
      details,
    };
  }

  /**
   * Validate multiple table conversions for a single converter
   */
  async validateConverterTables(
    converterName: string,
    tableMappings: Array<{
      mysql: string;
      postgres: string;
      expectedRecords?: number;
    }>,
  ): Promise<ConverterValidationResult[]> {
    const results: ConverterValidationResult[] = [];

    for (const mapping of tableMappings) {
      const result = await this.validateTableConversion(
        converterName,
        mapping.mysql,
        mapping.postgres,
        mapping.expectedRecords,
      );
      results.push(result);
    }

    return results;
  }

  /**
   * Print validation results for a converter
   */
  printConverterValidation(results: ConverterValidationResult[]): void {
    if (results.length === 0) return;

    const converterName = results[0]?.converterName;
    console.log(`\n📊 Validation Results for ${converterName}`);
    console.log('─'.repeat(50));

    for (const result of results) {
      const icon =
        result.status === 'PASS'
          ? '✅'
          : result.status === 'WARNING'
            ? '⚠️'
            : '❌';

      console.log(`${icon} ${result.mysqlTable} → ${result.postgresTable}`);
      console.log(
        `   MySQL: ${result.mysqlRecords} | PostgreSQL: ${result.postgresRecords} | ${result.status}`,
      );

      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    }

    const passed = results.filter((r) => r.status === 'PASS').length;
    const failed = results.filter((r) => r.status === 'FAIL').length;
    const warnings = results.filter((r) => r.status === 'WARNING').length;

    console.log(
      `\nSummary: ${passed} passed, ${failed} failed, ${warnings} warnings`,
    );
  }

  private countMySQLRecords(sqlContent: string, tableName: string): number {
    const insertStatements = this.extractInsertStatements(
      sqlContent,
      tableName,
    );
    let totalRecords = 0;

    for (const statement of insertStatements) {
      const valuesMatch = statement.match(/VALUES\s*(.+?);$/i);
      if (valuesMatch?.[1]) {
        const rowCount = this.splitValueRows(valuesMatch[1]).length;
        totalRecords += rowCount;
      }
    }

    return totalRecords;
  }

  private countPostgreSQLRecords(
    sqlContent: string,
    tableName: string,
  ): number {
    // Find all INSERT statements for the PostgreSQL table
    // Use a more robust approach that handles multi-line statements
    const insertPattern = `INSERT\\s+INTO\\s+"${tableName}"`;
    const insertRegex = new RegExp(insertPattern, 'gims');
    let totalRecords = 0;

    let match: RegExpExecArray | null;
    // biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
    while ((match = insertRegex.exec(sqlContent)) !== null) {
      const startIndex = match.index;

      // Find the VALUES keyword after the INSERT statement
      const valuesMatch = sqlContent.substring(startIndex).match(/VALUES\s*/i);
      if (!valuesMatch) {
        continue;
      }

      if (valuesMatch.index === undefined) {
        continue;
      }
      const valuesStartIndex =
        startIndex + valuesMatch.index + valuesMatch[0].length;

      // Find the semicolon that ends this INSERT statement
      let semicolonIndex = -1;
      let depth = 0;
      let inString = false;
      let stringChar = '';

      for (let i = valuesStartIndex; i < sqlContent.length; i++) {
        const char = sqlContent[i];
        const prevChar = i > 0 ? sqlContent[i - 1] : '';

        // Handle string literals
        if (!inString && (char === "'" || char === '"')) {
          inString = true;
          stringChar = char;
        } else if (inString && char === stringChar && prevChar !== '\\') {
          inString = false;
          stringChar = '';
        } else if (!inString) {
          if (char === '(') {
            depth++;
          } else if (char === ')') {
            depth--;
          } else if (char === ';' && depth === 0) {
            semicolonIndex = i;
            break;
          }
        }
      }

      if (semicolonIndex > valuesStartIndex) {
        const valuesSection = sqlContent.substring(
          valuesStartIndex,
          semicolonIndex,
        );
        // Use the same proper parsing method as MySQL counting
        // This correctly handles nested parentheses in field values
        if (valuesSection.trim()) {
          const rowCount = this.splitValueRows(valuesSection).length;
          totalRecords += rowCount;
        }
      }
    }

    return totalRecords;
  }
}

/**
 * Helper function to quickly validate a converter (non-throwing version)
 * Logs validation results and continues execution regardless of failures
 */
export async function validateConverter(
  converterName: string,
  tableMappings: Array<{
    mysql: string;
    postgres: string;
    expectedRecords?: number;
  }>,
): Promise<boolean> {
  try {
    const validator = new ConverterValidator();
    const results = await validator.validateConverterTables(
      converterName,
      tableMappings,
    );

    // Always print results
    validator.printConverterValidation(results);

    // Log specific errors for failed validations
    const failures = results.filter((r) => r.status === 'FAIL');
    const warnings = results.filter((r) => r.status === 'WARNING');

    if (failures.length > 0) {
      console.error(`\n❌ ${converterName} - VALIDATION FAILURES:`);
      for (const failure of failures) {
        const diff = failure.postgresRecords - failure.mysqlRecords;
        const diffText = diff > 0 ? `+${diff}` : `${diff}`;
        console.error(
          `   • ${failure.mysqlTable} → ${failure.postgresTable}: ${diffText} records (MySQL: ${failure.mysqlRecords}, PostgreSQL: ${failure.postgresRecords})`,
        );
        if (failure.details) {
          console.error(`     Details: ${failure.details}`);
        }
      }
    }

    if (warnings.length > 0) {
      console.warn(`\n⚠️  ${converterName} - VALIDATION WARNINGS:`);
      for (const warning of warnings) {
        console.warn(
          `   • ${warning.mysqlTable} → ${warning.postgresTable}: ${warning.details || 'No details'}`,
        );
      }
    }

    if (failures.length === 0 && warnings.length === 0) {
      console.log(`\n✅ ${converterName} - All validations passed!`);
    }

    // Return true if all validations passed (no failures), but don't throw
    return results.every((r) => r.status !== 'FAIL');
  } catch (error) {
    console.error(`\n💥 ${converterName} - Validation error:`, error);
    return false;
  }
}

/**
 * Simplified validation function that just logs the record counts
 */
export async function logConverterRecordCounts(
  converterName: string,
  tableMappings: Array<{
    mysql: string;
    postgres: string;
  }>,
): Promise<void> {
  try {
    const validator = new ConverterValidator();

    console.log(`\n📊 ${converterName} - Record Count Summary:`);
    console.log('─'.repeat(60));

    for (const mapping of tableMappings) {
      const result = await validator.validateTableConversion(
        converterName,
        mapping.mysql,
        mapping.postgres,
      );

      const icon = result.match ? '✅' : '❌';
      const diff = result.postgresRecords - result.mysqlRecords;
      const diffText = diff === 0 ? '' : ` (${diff > 0 ? '+' : ''}${diff})`;

      console.log(
        `${icon} ${mapping.mysql.padEnd(20)} → ${mapping.postgres.padEnd(20)} | ${result.mysqlRecords.toString().padStart(4)} → ${result.postgresRecords.toString().padStart(4)}${diffText}`,
      );
    }
  } catch (error) {
    console.error(
      `\n💥 ${converterName} - Failed to get record counts:`,
      error,
    );
  }
}
