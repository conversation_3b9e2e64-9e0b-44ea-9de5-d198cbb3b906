'use client';

import { DirectoryEntityActions } from '@/app/[locale]/bottin/directory-page/directory-entity-actions';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import type { Entity } from '@/types/directory/directory';
import type { SupportedLocale } from '@/types/locale';
import type { DbBuilding } from '@rie/domain/types';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter } from 'next-intl';

// Serializer function to transform DbBuilding to Entity format for DirectoryEntityActions
const dbBuildingToEntity = (dbBuilding: DbBuilding): Entity => {
  const defaultTranslation = dbBuilding.translations?.[0];
  const campusText =
    dbBuilding.campus?.translations?.[0]?.name || dbBuilding.campus?.id || '';

  return {
    id: dbBuilding.id,
    text: defaultTranslation?.name || 'Building',
    createdAt: dbBuilding.createdAt,
    lastUpdatedAt: dbBuilding.updatedAt,
    uid: null,
    campus: campusText,
    jurisdiction: null,
    nom: defaultTranslation?.name || '',
  };
};

export const buildingsColumns = (
  _locale: SupportedLocale,
): ColumnDef<DbBuilding>[] => {
  const directoryEntity = 'building' as const;

  return [
    {
      accessorKey: 'translations',
      cell: (cell) => {
        const translations = cell.getValue() as Array<{
          locale: string;
          name: string | null;
        }> | null;
        return translations && translations.length > 0
          ? translations[0]?.name || '-'
          : '-';
      },
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'campus',
      cell: (cell) => {
        const campus = cell.getValue() as {
          id: string;
          translations: Array<{ locale: string; name: string | null }>;
        } | null;
        return campus?.translations && campus.translations.length > 0
          ? campus.translations[0]?.name || '-'
          : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.campus"
        />
      ),
      id: 'campus',
      maxSize: 150,
    },
    {
      accessorKey: 'otherNames',
      cell: (cell) => {
        const names = cell.getValue() as string[];
        return names && names.length > 0 ? names.join(', ') : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.otherNames"
        />
      ),
      id: 'otherNames',
      maxSize: 200,
    },
    {
      accessorKey: 'updatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => {
        // Transform DbBuilding to Entity format using serializer
        const entityData = dbBuildingToEntity(row.original);
        const adaptedRow = {
          ...row,
          original: entityData,
        } as unknown as Row<Entity>;

        return (
          <DirectoryEntityActions
            row={adaptedRow}
            directoryEntity={directoryEntity}
          />
        );
      },
      id: 'actions',
      size: 105,
    },
  ];
};
