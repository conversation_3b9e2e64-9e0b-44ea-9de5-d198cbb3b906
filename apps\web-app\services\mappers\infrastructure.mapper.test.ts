import { CAMPUS_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import type { InfrastructureData } from '@rie/domain/types';
import type { InfrastructureWithRelatedEquipments } from '@rie/domain/types';
import { describe, expect, it } from 'vitest';
import {
  mapInfrastructureSchemaToForm,
  mapInfrastructureWithEquipmentsToDetails,
} from './infrastructure.mapper';

describe('mapInfrastructureSchemaToForm', () => {
  it('should map basic infrastructure schema to form schema', () => {
    const infrastructureData: InfrastructureData = {
      id: 'infra-123',
      guidId: 'guid-456',
      addressId: null,
      website: 'https://example.com',
      isFeatured: true,
      visibilityId: 'visibility-202',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      modifiedBy: null,

      // Related entities with their own translations
      address: null,
      type: {
        id: 'type-789',
        translations: [
          { id: 'type-trans-1', locale: 'en', name: 'Research Infrastructure' },
          {
            id: 'type-trans-2',
            locale: 'fr',
            name: 'Infrastructure de Recherche',
          },
        ],
      },
      status: {
        id: 'status-456',
        translations: [
          { id: 'status-trans-1', locale: 'en', name: 'Active' },
          { id: 'status-trans-2', locale: 'fr', name: 'Actif' },
        ],
      },
      unit: {
        id: 'unit-101',
        translations: [
          { id: 'unit-trans-1', locale: 'en', name: 'Engineering Department' },
          { id: 'unit-trans-2', locale: 'fr', name: 'Département de Génie' },
        ],
      },
      visibility: {
        id: 'visibility-202',
        translations: [
          { id: 'vis-trans-1', locale: 'en', name: 'Public' },
          { id: 'vis-trans-2', locale: 'fr', name: 'Publique' },
        ],
      },

      // Manager fields
      scientificManagers: [
        {
          id: 'sci-mgr-1',
          firstName: 'John',
          lastName: 'Smith',
        },
        {
          id: 'sci-mgr-2',
          firstName: 'Jane',
          lastName: 'Doe',
        },
      ],
      operationalManagers: [
        {
          id: 'tech-mgr-1',
          firstName: 'Bob',
          lastName: 'Johnson',
        },
      ],
      sstManagers: [
        {
          id: 'sst-mgr-1',
          firstName: 'Alice',
          lastName: 'Wilson',
        },
      ],

      // Infrastructure translations
      translations: [
        {
          id: '123',
          locale: 'en',
          name: 'Test Infrastructure',
          description: 'Test Description',
          otherNames: 'Test Alias',
          acronyms: 'TI',
        },
        {
          id: '456',
          locale: 'fr',
          name: 'Infrastructure Test',
          description: 'Description Test',
          otherNames: 'Alias Test',
          acronyms: 'IT',
        },
      ],
    };

    const result = mapInfrastructureSchemaToForm(infrastructureData, 'en');

    const expected: InfrastructureFormSchema = {
      name: [
        { locale: 'en', value: 'Test Infrastructure' },
        { locale: 'fr', value: 'Infrastructure Test' },
      ],
      description: [
        { locale: 'en', value: 'Test Description' },
        { locale: 'fr', value: 'Description Test' },
      ],
      acronym: [
        { locale: 'en', value: 'TI' },
        { locale: 'fr', value: 'IT' },
      ],
      alias: 'Test Alias',
      infoType: 'type-789',
      status: 'status-456',
      visibility: 'visibility-202',
      website: 'https://example.com',
      highlight: true,
      parentUnit: { value: 'unit-101', label: 'Engineering Department' },
      address: CAMPUS_ADDRESS_DEFAULT_VALUE,
      affiliatedPersons: [],
      associatedFinancialProject: [],
      innovationLab: [],
      locationBuilding: [],
      sstManager: [{ value: 'sst-mgr-1', label: 'Alice Wilson ' }],
      jurisdiction: { value: null, label: null },
      scientificAndTechnicalManager: {
        scientificManager: [
          { value: 'sci-mgr-1', label: 'John Smith ' },
          { value: 'sci-mgr-2', label: 'Jane Doe ' },
        ],
        technicalManager: [{ value: 'tech-mgr-1', label: 'Bob Johnson ' }],
      },
    };

    expect(result).toEqual(expected);
  });

  it('should handle infrastructure with partial translations', () => {
    const infrastructureData: InfrastructureData = {
      id: 'infra-123',
      guidId: 'guid-456',
      addressId: null,
      website: null,
      isFeatured: false,
      visibilityId: 'visibility-202',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      modifiedBy: null,

      // Related entities
      address: null,
      type: {
        id: 'type-789',
        translations: [
          { id: 'type-trans-3', locale: 'en', name: 'Research Infrastructure' },
        ],
      },
      status: {
        id: 'status-456',
        translations: [{ id: 'status-trans-3', locale: 'fr', name: 'Actif' }],
      },
      unit: null,
      visibility: {
        id: 'visibility-202',
        translations: [{ id: 'vis-trans-3', locale: 'en', name: 'Public' }],
      },

      // Manager fields - empty for this test
      scientificManagers: [],
      operationalManagers: [],
      sstManagers: [],

      // Infrastructure translations - partial
      translations: [
        {
          id: '123',
          locale: 'en',
          name: 'English Only Infrastructure',
          description: null,
          otherNames: null,
          acronyms: 'EOI',
        },
        {
          id: '456',
          locale: 'fr',
          name: null,
          description: 'Description française seulement',
          otherNames: null,
          acronyms: null,
        },
      ],
    };

    const result = mapInfrastructureSchemaToForm(infrastructureData, 'en');

    expect(result.name).toEqual([
      { locale: 'en', value: 'English Only Infrastructure' },
    ]);
    expect(result.description).toEqual([
      { locale: 'en', value: '' },
      { locale: 'fr', value: 'Description française seulement' },
    ]);
    expect(result.acronym).toEqual([{ locale: 'en', value: 'EOI' }]);
    expect(result.alias).toBe('');
    expect(result.parentUnit).toEqual({ value: null, label: null });
    expect(result.website).toBe('');
    expect(result.highlight).toBe(false);
  });

  it('should handle infrastructure with no translations', () => {
    const infrastructureData: InfrastructureData = {
      id: 'infra-123',
      guidId: 'guid-456',
      addressId: null,
      website: null,
      isFeatured: false,
      visibilityId: 'visibility-202',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      modifiedBy: null,

      // Related entities
      address: null,
      type: {
        id: 'type-789',
        translations: [
          { id: 'type-trans-4', locale: 'en', name: 'Research Infrastructure' },
        ],
      },
      status: {
        id: 'status-456',
        translations: [{ id: 'status-trans-4', locale: 'fr', name: 'Actif' }],
      },
      unit: null,
      visibility: {
        id: 'visibility-202',
        translations: [{ id: 'vis-trans-4', locale: 'en', name: 'Public' }],
      },

      // Manager fields - empty for this test
      scientificManagers: [],
      operationalManagers: [],
      sstManagers: [],

      // Empty translations
      translations: [],
    };

    const result = mapInfrastructureSchemaToForm(infrastructureData, 'fr');

    expect(result.name).toEqual([{ locale: 'fr', value: '' }]);
    expect(result.description).toEqual([{ locale: 'fr', value: '' }]);
    expect(result.acronym).toEqual([{ locale: 'fr', value: '' }]);
    expect(result.alias).toBe('');
  });

  it('should prioritize current locale for alias extraction', () => {
    const infrastructureData: InfrastructureData = {
      id: 'infra-123',
      guidId: 'guid-456',
      addressId: null,
      website: null,
      isFeatured: false,
      visibilityId: 'visibility-202',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      modifiedBy: null,

      // Related entities
      address: null,
      type: {
        id: 'type-789',
        translations: [
          { id: 'type-trans-5', locale: 'en', name: 'Research Infrastructure' },
        ],
      },
      status: {
        id: 'status-456',
        translations: [{ id: 'status-trans-5', locale: 'fr', name: 'Actif' }],
      },
      unit: null,
      visibility: {
        id: 'visibility-202',
        translations: [{ id: 'vis-trans-5', locale: 'en', name: 'Public' }],
      },

      // Manager fields - empty for this test
      scientificManagers: [],
      operationalManagers: [],
      sstManagers: [],

      // Infrastructure translations with different aliases per locale
      translations: [
        {
          id: '123',
          locale: 'en',
          name: 'Test',
          description: null,
          otherNames: 'English Alias',
          acronyms: null,
        },
        {
          id: '456',
          locale: 'fr',
          name: 'Test',
          description: null,
          otherNames: 'French Alias',
          acronyms: null,
        },
      ],
    };

    const resultEn = mapInfrastructureSchemaToForm(infrastructureData, 'en');
    const resultFr = mapInfrastructureSchemaToForm(infrastructureData, 'fr');

    expect(resultEn.alias).toBe('English Alias');
    expect(resultFr.alias).toBe('French Alias');
  });
});

const mockInfrastructureWithEquipments: InfrastructureWithRelatedEquipments = {
  id: 'infra-1',
  guidId: null,
  addressId: 'addr-1',
  website: 'https://example.com',
  isFeatured: true,
  visibilityId: 'vis-1',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  modifiedBy: null,
  scientificManagers: [
    {
      id: 'mgr-1',
      firstName: 'John',
      lastName: 'Doe',
    },
  ],
  operationalManagers: [
    {
      id: 'mgr-2',
      firstName: 'Jane',
      lastName: 'Smith',
    },
  ],
  sstManagers: [
    {
      id: 'mgr-3',
      firstName: 'Bob',
      lastName: 'Johnson',
    },
  ],
  address: null,
  type: {
    id: 'type-1',
    translations: [
      { id: 'type-trans-6', locale: 'en', name: 'Research Platform' },
      { id: 'type-trans-7', locale: 'fr', name: 'Plateforme de recherche' },
    ],
  },
  status: {
    id: 'status-1',
    translations: [
      { id: 'status-trans-6', locale: 'en', name: 'Active' },
      { id: 'status-trans-7', locale: 'fr', name: 'Actif' },
    ],
  },
  unit: {
    id: 'unit-1',
    translations: [
      { id: 'unit-trans-3', locale: 'en', name: 'Engineering Department' },
      { id: 'unit-trans-4', locale: 'fr', name: 'Département de génie' },
    ],
  },
  translations: [
    {
      id: 'infra-trans-1',
      locale: 'en',
      name: 'Biophysics Platform',
      description: 'A state-of-the-art biophysics research platform',
      otherNames: null,
      acronyms: 'BP',
    },
    {
      id: 'infra-trans-2',
      locale: 'fr',
      name: 'Plateforme de biophysique',
      description: 'Une plateforme de recherche en biophysique de pointe',
      otherNames: null,
      acronyms: 'PB',
    },
  ],
  visibility: {
    id: 'vis-1',
    translations: [
      { id: 'vis-trans-6', locale: 'en', name: 'Public' },
      { id: 'vis-trans-7', locale: 'fr', name: 'Public' },
    ],
  },
  equipments: [
    {
      id: 'eq-1',
      translations: [
        { id: 'eq-trans-1', locale: 'en', name: 'Microscope A' },
        { id: 'eq-trans-2', locale: 'fr', name: 'Microscope A' },
      ],
    },
    {
      id: 'eq-2',
      translations: [
        { id: 'eq-trans-3', locale: 'en', name: 'Spectrometer B' },
        { id: 'eq-trans-4', locale: 'fr', name: 'Spectromètre B' },
      ],
    },
  ],
};

describe('mapInfrastructureWithEquipmentsToDetails', () => {
  it('should map infrastructure data correctly for English locale', () => {
    const result = mapInfrastructureWithEquipmentsToDetails(
      mockInfrastructureWithEquipments,
      'en',
    );

    expect(result).toEqual({
      descriptionHeader: {
        nom: 'Biophysics Platform',
        status: 'status-1',
        statusText: 'Active',
        directeursTechnique: ['Jane Smith'],
        responsablesTechnique: ['John Doe'],
        url: 'https://example.com',
        address: 'addr-1',
        emplacement: null,
        pseudonyme: null,
        telephone: null,
      },
      description: {
        descriptions: {
          en: 'A state-of-the-art biophysics research platform',
          fr: 'Une plateforme de recherche en biophysique de pointe',
        },
        parentUnit: 'Engineering Department',
        responsablesSST: ['Bob Johnson'],
        jurisdiction: undefined,
        emplacement: undefined,
        affiliated: [],
        researcher: [],
        projects: '',
      },
      equipments: [
        {
          id: 'eq-1',
          name: 'Microscope A',
          equipmentName: 'Microscope A',
          infrastructureId: 'infra-1',
          status: 'active',
          technicalManager: [],
          parentInfrastructure: 'Biophysics Platform',
          decommissionDate: null,
          equipmentCategories: null,
          equipmentType: null,
          installationDate: null,
          location: null,
          manufacturer: null,
          model: null,
          purchaseDate: null,
          statusText: null,
          type: null,
          updatedAt: null,
        },
        {
          id: 'eq-2',
          name: 'Spectrometer B',
          equipmentName: 'Spectrometer B',
          infrastructureId: 'infra-1',
          status: 'active',
          technicalManager: [],
          parentInfrastructure: 'Biophysics Platform',
          decommissionDate: null,
          equipmentCategories: null,
          equipmentType: null,
          installationDate: null,
          location: null,
          manufacturer: null,
          model: null,
          purchaseDate: null,
          statusText: null,
          type: null,
          updatedAt: null,
        },
      ],
    });
  });

  it('should map infrastructure data correctly for French locale', () => {
    const result = mapInfrastructureWithEquipmentsToDetails(
      mockInfrastructureWithEquipments,
      'fr',
    );

    expect(result.descriptionHeader.nom).toBe('Plateforme de biophysique');
    expect(result.descriptionHeader.statusText).toBe('Actif');
    expect(result.description.parentUnit).toBe('Département de génie');
    expect(result.equipments[0].name).toBe('Microscope A');
    expect(result.equipments[1].name).toBe('Spectromètre B');
  });

  it('should handle missing translations gracefully', () => {
    const infrastructureWithMissingTranslations = {
      ...mockInfrastructureWithEquipments,
      translations: [],
      equipments: [
        {
          id: 'eq-1',
          translations: [],
        },
      ],
    };

    const result = mapInfrastructureWithEquipmentsToDetails(
      infrastructureWithMissingTranslations,
      'en',
    );

    expect(result.descriptionHeader.nom).toBeNull();
    expect(result.equipments[0].name).toBe('Unknown Equipment');
  });
});
