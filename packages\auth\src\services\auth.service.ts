import type { PermissionAction, ResourceType } from '@rie/domain/types';
import { UserPermissionsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';

export class AuthServiceLive extends Effect.Service<AuthServiceLive>()(
  'AuthServiceLive',
  {
    dependencies: [UserPermissionsServiceLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get user roles for session customization
       */
      const getUserRoles = (userId: string) => {
        return Effect.gen(function* () {
          const userPermissionsService = yield* UserPermissionsServiceLive;

          const userRoles =
            yield* userPermissionsService.getUserRolesWithContext(userId);

          return userRoles.map((userRole) => ({
            role: {
              id: userRole.id,
              name: userRole.name,
            },
            resourceType: userRole.resourceType,
            resourceId: userRole.resourceId,
          }));
        });
      };

      /**
       * Check if user has permission for a specific action
       */
      const checkUserPermission = (params: {
        userId: string;
        domain: ResourceType;
        action: PermissionAction;
        resourceId?: string;
      }) => {
        return Effect.gen(function* () {
          const userPermissionsService = yield* UserPermissionsServiceLive;

          return yield* userPermissionsService.userHasPermission(params);
        });
      };

      /**
       * Get user access tree
       */
      const getUserAccessTree = (userId: string) => {
        return Effect.gen(function* () {
          const userPermissionsService = yield* UserPermissionsServiceLive;

          return yield* userPermissionsService.getUserAccessTree(userId);
        });
      };

      return {
        getUserRoles,
        checkUserPermission,
        getUserAccessTree,
      } as const;
    }),
  },
) {}
