import path from 'node:path';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

export default defineConfig(() => {
  return {
    plugins: [tsconfigPaths()],
    test: {
      environment: 'node',
      exclude: ['**/node_modules/**', '**/build/**'],
      globals: true,
      coverage: {
        provider: 'v8' as const,
        enabled: true,
        reportsDirectory: 'coverage',
        reporter: ['html', 'text'],
        include: ['src/**/*.ts'],
        exclude: [
          'src/**/*.test.ts',
          'src/**/*.spec.ts',
          'src/**/types.ts',
          'src/migration-scripts/data/**',
          'src/migration-scripts/output/**',
        ],
      },
      setupFiles: [
        path.resolve(
          __dirname,
          './src/migration-scripts/converters/__tests__/vitest.setup.ts',
        ),
      ],
    },
  };
});
