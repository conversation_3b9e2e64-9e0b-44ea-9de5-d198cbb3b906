import * as Schema from 'effect/Schema';
import {
  OptionalLocalizedFieldDtoSchema,
  RequiredLocalizedFieldDtoSchema,
} from '../common';

// Base schema for campuses
export const CampusBaseDtoSchema = Schema.Struct({
  id: Schema.String,
  isActive: Schema.Boolean,
  sadId: Schema.NullOr(Schema.String),
  institutionId: Schema.NonEmptyString,
});

// DTO for lists of campuses (e.g., view='list')
export const CampusListItemDtoSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String,
  jurisdiction: Schema.NullOr(Schema.String),
  lastUpdatedAt: Schema.String,
  createdAt: Schema.String,
  uid: Schema.NullOr(Schema.String),
});

export type CampusListItemDTO = Schema.Schema.Type<
  typeof CampusListItemDtoSchema
>;

// Campus-specific translation schemas
export const CampusTranslationDtoSchema = Schema.Struct({
  name: RequiredLocalizedFieldDtoSchema,
});

export const CampusTranslationInputDtoSchema = Schema.Struct({
  name: OptionalLocalizedFieldDtoSchema,
});

// DTO for the detailed view of a single campus (e.g., view='detail')
export const CampusDetailResponseDtoSchema = Schema.extend(
  CampusBaseDtoSchema,
  Schema.Struct({
    translations: CampusTranslationDtoSchema,
  }),
);

export type CampusDetailResponseDTO = Schema.Schema.Type<
  typeof CampusDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const CampusEditResponseDtoSchema = Schema.extend(
  CampusBaseDtoSchema,
  Schema.Struct({
    translations: Schema.Array(CampusTranslationInputDtoSchema),
  }),
);
export type CampusEditResponseDTO = Schema.Schema.Type<
  typeof CampusEditResponseDtoSchema
>;
