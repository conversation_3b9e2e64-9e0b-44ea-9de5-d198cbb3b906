import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { UnitTypeMigrationConverter } from '../converters/18-01-convert-unit-type-inserts';

async function convertUnitTypeData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new UnitTypeMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertUnitTypeData().catch(console.error);
