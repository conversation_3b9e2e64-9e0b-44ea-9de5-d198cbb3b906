import { CampusI18nMigrationConverter } from '../converters/29-02-convert-campus-i18n-inserts';

async function convertCampusI18nData() {
  try {
    // Create converter instance
    const converter = new CampusI18nMigrationConverter();

    // Run the conversion
    await converter.convertFile();
  } catch (error) {
    console.error('Error converting campus i18n data:', error);
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (require.main === module) {
  convertCampusI18nData().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { convertCampusI18nData };
