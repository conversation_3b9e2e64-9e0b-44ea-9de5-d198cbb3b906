import { getAllUsers, getUserById } from '@/services/users/users.service';
import { queryOptions } from '@tanstack/react-query';

export const getAllUsersOptions = () => {
  return queryOptions({
    queryFn: getAllUsers,
    queryKey: ['users'],
    select: (data) =>
      data.map((role) => ({
        label: role.name,
        value: role.id,
      })),
  });
};

export const getUserByIdOptions = (id?: string) => {
  return queryOptions({
    queryFn: () => getUserById(id as string),
    queryKey: ['users', id],
    enabled: !!id,
  });
};
