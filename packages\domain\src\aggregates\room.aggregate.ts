import type { DbRoom } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
    RoomInvariantViolationError,
} from '../errors';
import { DbRoomDataSchema } from '../schemas';
import type { RoomData } from '../types';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface RoomAggregateState
    extends Omit<DbRoom, 'createdAt' | 'updatedAt'> {
    createdAt?: string;
    updatedAt?: string;
}

const RoomAggregateState = Data.case<RoomAggregateState>();

// Room Aggregate Root
export class Room {
    private constructor(private readonly data: RoomAggregateState) { }

    /**
     * Factory for creating a NEW Room aggregate.
     * Use this when creating a room from user input for the first time.
     */
    static create(props: {
        number: string;
        area?: number | null;
        floorLoad?: number | null;
        buildingId?: string | null;
        modifiedBy: string;
    }): Effect.Effect<Room, RoomInvariantViolationError> {
        const now = new Date().toISOString();
        const newRoom = new Room(
            RoomAggregateState({
                id: DbUtils.cuid2(),
                isActive: true,
                // Set temporary timestamps to satisfy toRaw() validation
                createdAt: now as string,
                updatedAt: now as string,
                number: props.number,
                area: props.area ?? null,
                floorLoad: props.floorLoad ?? null,
                buildingId: props.buildingId ?? null,
                modifiedBy: props.modifiedBy,
            }),
        );

        // Ensure the new room is valid before returning it
        return pipe(
            newRoom.validateInvariants(),
            Effect.map(() => newRoom),
        );
    }

    /**
     * Factory for rehydrating a Room aggregate from raw database data.
     */
    static fromDatabaseData(
        rawData: RoomData,
    ): Effect.Effect<
        Room,
        | ParseResult.ParseError
        | RoomInvariantViolationError
    > {
        return pipe(
            Schema.decodeUnknown(DbRoomDataSchema)(rawData),
            Effect.mapError((parseError) =>
                new RoomInvariantViolationError({
                    roomId: rawData.id,
                    reason: `Room database data validation failed: ${parseError.message}`,
                }),
            ),
            Effect.map(
                (hydratedData) =>
                    new Room(
                        RoomAggregateState({
                            ...hydratedData,
                            id: rawData.id, // ensure original id is preserved
                        }),
                    ),
            ),
            Effect.flatMap((room) =>
                pipe(
                    room.validateInvariants(),
                    Effect.map(() => room),
                ),
            ),
        );
    }

    // --- Business Methods ---

    activate(
        modifiedBy: string,
    ): Effect.Effect<Room, RoomInvariantViolationError> {
        if (this.data.isActive) {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Cannot activate a room that is already active',
                }),
            );
        }
        return Effect.succeed(
            new Room(
                RoomAggregateState({
                    ...this.data,
                    isActive: true,
                    modifiedBy,
                    // `updatedAt` is no longer set here; the database trigger will handle it.
                }),
            ),
        );
    }

    deactivate(
        modifiedBy: string,
    ): Effect.Effect<Room, RoomInvariantViolationError> {
        if (!this.data.isActive) {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Cannot deactivate a room that is already inactive',
                }),
            );
        }
        return Effect.succeed(
            new Room(
                RoomAggregateState({
                    ...this.data,
                    isActive: false,
                    modifiedBy,
                    // `updatedAt` is no longer set here; the database trigger will handle it.
                }),
            ),
        );
    }

    /**
     * Updates the room with new data.
     */
    update(props: {
        number: string;
        area?: number | null;
        floorLoad?: number | null;
        buildingId?: string | null;
        isActive: boolean;
        modifiedBy: string;
    }): Effect.Effect<Room, RoomInvariantViolationError> {
        const newRoom = new Room(
            RoomAggregateState({
                ...this.data,
                number: props.number,
                area: props.area ?? null,
                floorLoad: props.floorLoad ?? null,
                buildingId: props.buildingId ?? null,
                isActive: props.isActive,
                modifiedBy: props.modifiedBy,
                // `updatedAt` is no longer set here; the database trigger will handle it.
            }),
        );

        return pipe(
            newRoom.validateInvariants(),
            Effect.map(() => newRoom),
        );
    }

    // --- Query Methods ---

    get id(): string {
        return this.data.id;
    }

    get number(): string {
        return this.data.number;
    }

    get area(): number | null {
        return this.data.area;
    }

    get floorLoad(): number | null {
        return this.data.floorLoad;
    }

    get buildingId(): string | null {
        return this.data.buildingId;
    }

    isActive(): boolean {
        return this.data.isActive ?? true;
    }

    toRaw(): Effect.Effect<RoomData, ParseResult.ParseError> {
        const SourceRoomSchema = Schema.extend(
            DbRoomDataSchema.omit('createdAt', 'updatedAt'),
            Schema.Struct({
                createdAt: Schema.optional(Schema.String),
                updatedAt: Schema.optional(Schema.String),
            }),
        );

        // Create the failable transformer
        const transformer = Schema.transformOrFail(
            SourceRoomSchema,
            DbRoomDataSchema,
            {
                decode: (source, _, ast) => {
                    if (source.createdAt && source.updatedAt) {
                        // The cast is safe because we have checked the properties exist.
                        return ParseResult.succeed(source as RoomData);
                    }
                    return ParseResult.fail(
                        new ParseResult.Type(
                            ast,
                            source,
                            'Cannot serialize for persistence because createdAt or updatedAt is missing',
                        ),
                    );
                },
                encode: (val, _options, ast) =>
                    ParseResult.fail(
                        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
                    ),
            },
        );

        return Schema.decode(transformer)(this.data);
    }

    // --- Invariant Validation ---

    private validateInvariants(): Effect.Effect<
        void,
        RoomInvariantViolationError
    > {
        // Check that room has a valid number
        if (!this.data.number || this.data.number.trim() === '') {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Room must have a valid number',
                }),
            );
        }

        // Check that buildingId is valid
        if (this.data.buildingId && this.data.buildingId.trim() === '') {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Room building ID cannot be empty string',
                }),
            );
        }

        // Check that area is positive if provided
        if (this.data.area !== null && this.data.area <= 0) {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Room area must be positive',
                }),
            );
        }

        // Check that floorLoad is positive if provided
        if (this.data.floorLoad !== null && this.data.floorLoad <= 0) {
            return Effect.fail(
                new RoomInvariantViolationError({
                    roomId: this.data.id,
                    reason: 'Room floor load must be positive',
                }),
            );
        }

        return Effect.succeed(void 0);
    }
}