'use client';

import { createInfrastructureMutation } from '@/app/[locale]/infrastructures/ajouter/hooks/useCreateInfrastructureMutation';
import { InfrastructureForm } from '@/app/[locale]/infrastructures/form/infrastructure-form';
import { infrastructureFormDefaultValues } from '@/constants/infrastructures';
import type { SupportedLocale } from '@/types/locale';

type NewInfrastructureFormProps = {
  formSections: Record<string, string>;
  locale: SupportedLocale;
};
export const NewInfrastructureForm = ({
  formSections,
  locale,
}: NewInfrastructureFormProps) => {
  const onSubmitPost = createInfrastructureMutation(locale);

  const status = 'success'; //TODO: Placeholder until mutation hook is implemented

  return (
    <InfrastructureForm
      defaultValues={infrastructureFormDefaultValues(locale)}
      formSections={formSections}
      onSubmitAction={onSubmitPost}
      status={status}
    />
  );
};
