import type { DbFundingProject } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import type * as ParseResult from 'effect/ParseResult';
import type {
    DuplicateLocaleError,
    FundingProjectInvariantViolationError,
} from '../errors';
import type { FundingProject as FundingProjectType } from '../types';
import {
    FundingProjectTranslation,
    FundingProjectTranslationCollection,
} from '../value-objects';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface FundingProjectAggregateState
    extends Omit<DbFundingProject, 'createdAt' | 'updatedAt'> {
    createdAt?: string;
    updatedAt?: string;
    translations: FundingProjectTranslationCollection;
}

const FundingProjectAggregateState = Data.case<FundingProjectAggregateState>();

// FundingProject Aggregate Root
export class FundingProject {
    private constructor(private readonly data: FundingProjectAggregateState) { }

    /**
     * Factory for creating a NEW FundingProject aggregate.
     * Use this when creating a funding project from user input for the first time.
     */
    static create(props: {
        holderId: string;
        typeId: string;
        fciId?: string | null;
        synchroId: string;
        obtainingYear: number;
        endDate?: string | null;
        modifiedBy: string;
        translations: FundingProjectTranslationCollection;
    }): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const now = new Date().toISOString();
        const newFundingProject = new FundingProject(
            FundingProjectAggregateState({
                id: DbUtils.cuid2(),
                isActive: true,
                // Set temporary timestamps to satisfy toRaw() validation
                createdAt: now,
                updatedAt: now,
                modifiedBy: props.modifiedBy,
                holderId: props.holderId,
                typeId: props.typeId,
                fciId: props.fciId ?? null,
                synchroId: props.synchroId,
                obtainingYear: props.obtainingYear,
                endDate: props.endDate ?? null,
                translations: props.translations,
            }),
        );

        return Effect.succeed(newFundingProject);
    }

    /**
     * Factory for RECONSTRUCTING a FundingProject aggregate from the database.
     * Use this when hydrating from stored data.
     */
    static fromDatabase(
        data: FundingProjectType,
    ): Effect.Effect<
        FundingProject,
        ParseResult.ParseError | DuplicateLocaleError
    > {
        return Effect.gen(function* () {
            // Convert translation data to value objects
            const translationVOs = yield* Effect.all(
                data.translations.map((translation) =>
                    FundingProjectTranslation.create({
                        locale: translation.locale,
                        name: translation.name,
                        description: translation.description,
                    }),
                ),
            );

            const translationCollection =
                yield* FundingProjectTranslationCollection.create(translationVOs);

            return new FundingProject(
                FundingProjectAggregateState({
                    id: data.id,
                    isActive: data.isActive,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt,
                    modifiedBy: data.modifiedBy ?? null,
                    holderId: data.holderId,
                    typeId: data.typeId,
                    fciId: data.fciId,
                    synchroId: data.synchroId,
                    obtainingYear: data.obtainingYear,
                    endDate: data.endDate,
                    translations: translationCollection,
                }),
            );
        });
    }

    // --- Getters ---

    getId(): string {
        return this.data.id;
    }

    getIsActive(): boolean {
        return this.data.isActive;
    }

    getHolderId(): string {
        return this.data.holderId;
    }

    getTypeId(): string {
        return this.data.typeId;
    }

    getFciId(): string | null {
        return this.data.fciId;
    }

    getSynchroId(): string {
        return this.data.synchroId;
    }

    getObtainingYear(): number {
        return this.data.obtainingYear;
    }

    getEndDate(): string | null {
        return this.data.endDate;
    }

    getModifiedBy(): string | null {
        return this.data.modifiedBy;
    }

    getCreatedAt(): string | undefined {
        return this.data.createdAt;
    }

    getUpdatedAt(): string | undefined {
        return this.data.updatedAt;
    }

    getTranslations(): FundingProjectTranslationCollection {
        return this.data.translations;
    }

    /**
     * Returns composite translations with intelligent locale fallback logic.
     * This method leverages the translation collection's fallback system to provide
     * the best available translation for each field based on locale preferences.
     */
    getCompositeTranslations(locale: string, fallbackLocale: string) {
        return this.data.translations.getCompositeTranslations(locale, fallbackLocale);
    }

    // --- Business Logic Methods ---

    /**
     * Updates the funding project's holder.
     */
    updateHolder(
        holderId: string,
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            holderId,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Updates the funding project's type.
     */
    updateType(
        typeId: string,
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            typeId,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Updates the funding project's obtaining year.
     */
    updateObtainingYear(
        obtainingYear: number,
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            obtainingYear,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Updates the funding project's end date.
     */
    updateEndDate(
        endDate: string | null,
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            endDate,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Updates the translations for this funding project.
     */
    updateTranslations(
        translations: FundingProjectTranslationCollection,
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            translations,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Deactivates the funding project.
     */
    deactivate(
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            isActive: false,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    /**
     * Reactivates the funding project.
     */
    reactivate(
        modifiedBy: string,
    ): Effect.Effect<FundingProject, FundingProjectInvariantViolationError> {
        const updatedData = FundingProjectAggregateState({
            ...this.data,
            isActive: true,
            modifiedBy,
            updatedAt: new Date().toISOString(),
        });

        return Effect.succeed(new FundingProject(updatedData));
    }

    // --- Serialization Methods ---

    /**
     * Converts the aggregate to its raw database representation.
     * Used for persistence operations.
     */
    toRaw(): Effect.Effect<FundingProjectType, ParseResult.ParseError> {
        return pipe(
            this.data.translations.toPersistenceArray(),
            Effect.map((translations) => ({
                id: this.data.id,
                isActive: this.data.isActive,
                createdAt: this.data.createdAt ?? new Date().toISOString(),
                updatedAt: this.data.updatedAt ?? new Date().toISOString(),
                modifiedBy: this.data.modifiedBy,
                holderId: this.data.holderId,
                typeId: this.data.typeId,
                fciId: this.data.fciId,
                synchroId: this.data.synchroId,
                obtainingYear: this.data.obtainingYear,
                endDate: this.data.endDate,
                translations,
            })),
        );
    }
}