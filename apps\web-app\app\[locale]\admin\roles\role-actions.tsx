'use client';

import { ActionsButton } from '@/components/actions-button/actions-button';
import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { Button } from '@/components/ui/button';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useDeleteRole } from '@/hooks/admin/permissions/roles.hook';
import { Link } from '@/lib/navigation';
import type { RoleList } from '@rie/domain/types';
import { useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

interface RoleActionsProps {
  role: RoleList;
}

export const RoleActions = ({ role }: RoleActionsProps) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteRole } = useDeleteRole();

  const handleDelete = () => {
    deleteRole(role.id);
  };

  return (
    <ActionsButton>
      <DropdownMenuItem asChild>
        <Link
          href={{
            pathname: '/admin/roles/[id]/editer',
            params: { id: role.id },
          }}
          className="flex items-center gap-2 w-full"
        >
          <FaEdit className="h-4 w-4" />
          {tCommon('edit')}
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem asChild>
        <DeleteConfirmation
          onDeleteAction={handleDelete}
          title={tCommon('deleteItemQuestion', { item: role.name })}
          trigger={
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 w-full justify-start px-2 py-1.5 h-auto font-normal"
            >
              <MdDeleteForever className="h-4 w-4" />
              {tCommon('delete')}
            </Button>
          }
        />
      </DropdownMenuItem>
    </ActionsButton>
  );
};
