import * as path from 'node:path';
import { SQL_FILE_NAME } from '../constants';
import { PeopleRoleTypeMigrationConverter } from '../converters/15-01-convert-people-role-type-inserts';

async function convertPeopleRoleTypeData() {
  // Output will go to migration-scripts/output
  const outputDir = path.join(__dirname, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Initialize converter
    const converter = new PeopleRoleTypeMigrationConverter();

    // Convert the file
    await converter.convertFile();

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertPeopleRoleTypeData().catch(console.error);
