import * as Schema from 'effect/Schema';
import {
  RequiredLocalizedFieldDtoSchema,
  TranslationDtoSchema,
  TranslationInputDtoSchema,
} from '../common';

// Base schema for units
export const UnitBaseDtoSchema = Schema.Struct({
  id: Schema.String,
  isActive: Schema.Boolean,
  typeId: Schema.NonEmptyString,
  parentId: Schema.NullOr(Schema.String),
});

// DTO for lists of units (e.g., view='list')
export const UnitListItemDtoSchema = Schema.Struct({
  id: Schema.String,
  name: RequiredLocalizedFieldDtoSchema,
  acronym: Schema.NullOr(Schema.String),
  parent: Schema.NullOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
});

export type UnitListItemDTO = Schema.Schema.Type<typeof UnitListItemDtoSchema>;

// DTO for the detailed view of a single unit (e.g., view='detail')
export const UnitDetailResponseDtoSchema = Schema.extend(
  UnitBaseDtoSchema,
  Schema.Struct({
    translations: TranslationDtoSchema,
  }),
);

export type UnitDetailResponseDTO = Schema.Schema.Type<
  typeof UnitDetailResponseDtoSchema
>;

// DTO for the data needed to populate an edit form (e.g., view='edit')
export const UnitEditResponseDtoSchema = Schema.extend(
  UnitBaseDtoSchema,
  Schema.Struct({
    translations: Schema.Array(TranslationInputDtoSchema),
  }),
);
export type UnitEditResponseDTO = Schema.Schema.Type<
  typeof UnitEditResponseDtoSchema
>;
