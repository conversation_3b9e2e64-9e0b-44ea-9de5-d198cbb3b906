import { CampusMigrationConverter } from '../converters/29-01-convert-campus-inserts';

async function convertCampusData() {
  // Output will go to migration-scripts/output

  try {
    // Create converter instance
    const converter = new CampusMigrationConverter();

    // Run the conversion
    await converter.convertFile();
  } catch (error) {
    console.error('Error converting campus data:', error);
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (require.main === module) {
  convertCampusData().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { convertCampusData };
